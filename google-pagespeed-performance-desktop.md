For Desktops

Metrics Expand view First Contentful Paint 0.5 s Largest Contentful Paint 0.6 s
Total Blocking Time 290 ms Cumulative Layout Shift 0 Speed Index 2.0 s Captured
at Jun 11, 2025, 4:43 PM GMT+3 Emulated Desktop with Lighthouse 12.6.1 Single
page session Initial page load Custom throttling Using HeadlessChromium
136.0.7103.113 with lr View Treemap Screenshot Screenshot Screenshot Screenshot
Screenshot Screenshot Screenshot Screenshot Later this year, insights will
replace performance audits. Learn more and provide feedback here. Go back to
audits Show audits relevant to:

All

FCP

LCP

TBT Insights Document request latency Your first network request is the most
important. Reduce its latency by avoiding redirects, ensuring a fast server
response, and enabling text compression.LCPFCP Avoids redirects Server responded
slowly (observed 1550 ms) Applies text compression Improve image delivery Est
savings of 537 KiB Reducing the download time of images can improve the
perceived load time of the page and LCP. Learn more about optimizing image
sizeLCPFCP URL Resource Size Est Savings ponyclub.gr 1st party 526.1 KiB 287.9
KiB /_next/image?url=%2Fimages%2Fhero-image.webp&w=1920&q=85(www.ponyclub.gr)
178.9 KiB 84.8 KiB Increasing the image compression factor could improve this
image's download size. 84.8 KiB /images/hero-image.webp(www.ponyclub.gr) 178.9
KiB 84.8 KiB Increasing the image compression factor could improve this image's
download size. 84.8 KiB
/\_next/image?url=%2Fimages%2Fround2.jpg&w=384&q=75(www.ponyclub.gr) 40.0 KiB
31.8 KiB Increasing the image compression factor could improve this image's
download size. 26.8 KiB This image file is larger than it needs to be (384x210)
for its displayed dimensions (183x275). Use responsive images to reduce the
image download size. 15.1 KiB
/\_next/image?url=%2Fimages%2Fround1.jpg&w=384&q=75(www.ponyclub.gr) 27.3 KiB
24.2 KiB Increasing the image compression factor could improve this image's
download size. 14.2 KiB This image file is larger than it needs to be (384x209)
for its displayed dimensions (183x106). Use responsive images to reduce the
image download size. 20.7 KiB
/\_next/image?url=%2Fimages%2Fround3.jpg&w=384&q=75(www.ponyclub.gr) 23.7 KiB
19.6 KiB Increasing the image compression factor could improve this image's
download size. 10.7 KiB This image file is larger than it needs to be (384x209)
for its displayed dimensions (183x137). Use responsive images to reduce the
image download size. 16.3 KiB
/\_next/image?url=%2Fimages%2Fponyclub_logo.png&w=256&q=75(www.ponyclub.gr) 16.4
KiB 13.9 KiB Increasing the image compression factor could improve this image's
download size. 13.9 KiB
/\_next/image?url=%2Fimages%2FRafting….jpg&w=384&q=75(www.ponyclub.gr) 24.4 KiB
12.4 KiB Increasing the image compression factor could improve this image's
download size. 12.4 KiB
/\_next/image?url=%2Fimages%2Fround2.jpg&w=192&q=75(www.ponyclub.gr) 13.1 KiB
7.1 KiB Increasing the image compression factor could improve this image's
download size. 7.1 KiB
/\_next/image?url=%2Fimages%2Fround1.jpg&w=192&q=75(www.ponyclub.gr) 6.9 KiB 4.9
KiB Increasing the image compression factor could improve this image's download
size. 4.9 KiB
/\_next/image?url=%2Fimages%2FKayaker….jpg&w=384&q=75(www.ponyclub.gr) 16.5 KiB
4.5 KiB Increasing the image compression factor could improve this image's
download size. 4.5 KiB Other Google APIs/SDKs utility 252.7 KiB 249.3 KiB
/a-/ALV-UjXo6…=s120-c-rp-mo-br100(lh3.googleusercontent.com) 31.1 KiB 30.9 KiB
Using a modern image format (WebP, AVIF) or increasing the image compression
could improve this image's download size. 28.8 KiB This image file is larger
than it needs to be (120x120) for its displayed dimensions (40x40). Use
responsive images to reduce the image download size. 27.7 KiB
/a-/ALV-UjWBx…=s120-c-rp-mo-br100(lh3.googleusercontent.com) 29.0 KiB 28.7 KiB
Using a modern image format (WebP, AVIF) or increasing the image compression
could improve this image's download size. 26.6 KiB This image file is larger
than it needs to be (120x120) for its displayed dimensions (40x40). Use
responsive images to reduce the image download size. 25.7 KiB
/a-/ALV-UjXA4…=s120-c-rp-mo-br100(lh3.googleusercontent.com) 28.8 KiB 28.5 KiB
Using a modern image format (WebP, AVIF) or increasing the image compression
could improve this image's download size. 26.4 KiB This image file is larger
than it needs to be (120x120) for its displayed dimensions (40x40). Use
responsive images to reduce the image download size. 25.6 KiB
/a-/ALV-UjUCi…=s120-c-rp-mo-br100(lh3.googleusercontent.com) 28.8 KiB 28.5 KiB
Using a modern image format (WebP, AVIF) or increasing the image compression
could improve this image's download size. 26.4 KiB This image file is larger
than it needs to be (120x120) for its displayed dimensions (40x40). Use
responsive images to reduce the image download size. 25.6 KiB
/a-/ALV-UjUFq…=s120-c-rp-mo-br100(lh3.googleusercontent.com) 25.6 KiB 25.4 KiB
Using a modern image format (WebP, AVIF) or increasing the image compression
could improve this image's download size. 23.3 KiB This image file is larger
than it needs to be (120x120) for its displayed dimensions (40x40). Use
responsive images to reduce the image download size. 22.8 KiB
/a-/ALV-UjVXn…=s120-c-rp-mo-ba4-br100(lh3.googleusercontent.com) 24.0 KiB 23.7
KiB Using a modern image format (WebP, AVIF) or increasing the image compression
could improve this image's download size. 21.6 KiB This image file is larger
than it needs to be (120x120) for its displayed dimensions (40x40). Use
responsive images to reduce the image download size. 21.3 KiB
/a-/ALV-UjVLJ…=s120-c-rp-mo-br100(lh3.googleusercontent.com) 21.4 KiB 21.2 KiB
Using a modern image format (WebP, AVIF) or increasing the image compression
could improve this image's download size. 19.1 KiB This image file is larger
than it needs to be (120x120) for its displayed dimensions (40x40). Use
responsive images to reduce the image download size. 19.0 KiB
/a-/ALV-UjUA5…=s120-c-rp-mo-ba3-br100(lh3.googleusercontent.com) 18.9 KiB 18.6
KiB Using a modern image format (WebP, AVIF) or increasing the image compression
could improve this image's download size. 16.5 KiB This image file is larger
than it needs to be (120x120) for its displayed dimensions (40x40). Use
responsive images to reduce the image download size. 16.8 KiB
/a-/ALV-UjXXV…=s120-c-rp-mo-ba2-br100(lh3.googleusercontent.com) 15.9 KiB 15.6
KiB Using a modern image format (WebP, AVIF) or increasing the image compression
could improve this image's download size. 13.5 KiB This image file is larger
than it needs to be (120x120) for its displayed dimensions (40x40). Use
responsive images to reduce the image download size. 14.1 KiB
/a/ACg8ocK2B…=s120-c-rp-mo-ba4-br100(lh3.googleusercontent.com) 7.5 KiB 7.3 KiB
Using a modern image format (WebP, AVIF) or increasing the image compression
could improve this image's download size. 5.2 KiB This image file is larger than
it needs to be (120x120) for its displayed dimensions (40x40). Use responsive
images to reduce the image download size. 6.7 KiB
/a/ACg8ocIX4…=s120-c-rp-mo-ba2-br100(lh3.googleusercontent.com) 7.5 KiB 7.3 KiB
Using a modern image format (WebP, AVIF) or increasing the image compression
could improve this image's download size. 5.2 KiB This image file is larger than
it needs to be (120x120) for its displayed dimensions (40x40). Use responsive
images to reduce the image download size. 6.7 KiB
/a/ACg8ocJlt…=s120-c-rp-mo-ba2-br100(lh3.googleusercontent.com) 7.2 KiB 6.9 KiB
Using a modern image format (WebP, AVIF) or increasing the image compression
could improve this image's download size. 4.8 KiB This image file is larger than
it needs to be (120x120) for its displayed dimensions (40x40). Use responsive
images to reduce the image download size. 6.4 KiB
/a/ACg8ocIDs…=s120-c-rp-mo-ba3-br100(lh3.googleusercontent.com) 7.0 KiB 6.8 KiB
Using a modern image format (WebP, AVIF) or increasing the image compression
could improve this image's download size. 4.7 KiB This image file is larger than
it needs to be (120x120) for its displayed dimensions (40x40). Use responsive
images to reduce the image download size. 6.3 KiB Render blocking requests
Requests are blocking the page's initial render, which may delay LCP. Deferring
or inlining can move these network requests out of the critical path.LCPFCP URL
Transfer Size Est Savings ponyclub.gr 1st party 137.0 KiB 610 ms
…css/698858c8367ac2a0.css(www.ponyclub.gr) 122.0 KiB 310 ms
…css/264526a98927e243.css(www.ponyclub.gr) 8.2 KiB 150 ms
…css/d515496b8f5b2e0a.css(www.ponyclub.gr) 6.9 KiB 150 ms Forced reflow Many
APIs, typically reading layout geometry, force the rendering engine to pause
script execution in order to calculate the style and layout. Learn more about
forced reflow and its mitigations. Top function call Total reflow time
…chunks/797-1d16e539206e578b.js:19:63174(www.ponyclub.gr) 68 ms Source Total
reflow time …chunks/945-7b4c98ba143510df.js:1:31735(www.ponyclub.gr) 70 ms
…chunks/7f358c6e-b3c55055b0d02466.js:1:9388(www.ponyclub.gr) 9 ms [unattributed]
8 ms /BokunWidgets.91c21ce….js:2:20861(static.bokun.io) 0 ms LCP request
discovery Optimize LCP by making the LCP image discoverable from the HTML
immediately, and avoiding lazy-loadingLCP lazy load not applied
fetchpriority=high should be applied Request is discoverable in initial document
main.relative > div.relative > div.absolute > video.absolute
<video src="/images/hero-video.mp4" poster="/images/hero-image.webp" autoplay="" muted="" loop="" playsinline="" preload="metadata" class="absolute inset-0 w-full h-full object-cover z-10">
Network dependency tree Avoid chaining critical requests by reducing the length
of chains, reducing the download size of resources, or deferring the download of
unnecessary resources to improve page load.LCP Maximum critical path latency:
1,830 ms Initial Navigation /en(www.ponyclub.gr) - 1,602 ms, 65.85 KiB
…css/698858c8367ac2a0.css(www.ponyclub.gr) - 1,830 ms, 121.95 KiB
…css/d515496b8f5b2e0a.css(www.ponyclub.gr) - 1,783 ms, 6.87 KiB
…css/264526a98927e243.css(www.ponyclub.gr) - 1,798 ms, 8.15 KiB Use efficient
cache lifetimes Est savings of 447 KiB A long cache lifetime can speed up repeat
visits to your page. Learn more.LCPFCP Request Cache TTL Transfer Size Google
Maps utility 247 KiB …api/js?key=AIzaSyB9j…(maps.googleapis.com) 30m 221 KiB
…js/StaticMapService.GetMapImage?…(maps.googleapis.com) 1d 14 KiB
…api/js?client=…(maps.googleapis.com) 30m 11 KiB Other Google APIs/SDKs utility
199 KiB /a-/ALV-UjXo6…=s120-c-rp-mo-br100(lh3.googleusercontent.com) 1d 13 KiB
/a-/ALV-UjWQS…=s120-c-rp-mo-ba2-br100(lh3.googleusercontent.com) 1d 12 KiB
/a-/ALV-UjWBx…=s120-c-rp-mo-br100(lh3.googleusercontent.com) 1d 12 KiB
/a-/ALV-UjXA4…=s120-c-rp-mo-br100(lh3.googleusercontent.com) 1d 12 KiB
/a-/ALV-UjUCi…=s120-c-rp-mo-br100(lh3.googleusercontent.com) 1d 12 KiB
/a-/ALV-UjXor…=s120-c-rp-mo-ba3-br100(lh3.googleusercontent.com) 1d 11 KiB
/a-/ALV-UjUFq…=s120-c-rp-mo-br100(lh3.googleusercontent.com) 1d 10 KiB
/a-/ALV-UjVJL…=s120-c-rp-mo-ba3-br100(lh3.googleusercontent.com) 1d 10 KiB
/a-/ALV-UjVXn…=s120-c-rp-mo-ba4-br100(lh3.googleusercontent.com) 1d 10 KiB
/a-/ALV-UjVOB…=s120-c-rp-mo-ba3-br100(lh3.googleusercontent.com) 1d 9 KiB
/a-/ALV-UjW51…=s120-c-rp-mo-br100(lh3.googleusercontent.com) 1d 9 KiB
/a-/ALV-UjVxb…=s120-c-rp-mo-br100(lh3.googleusercontent.com) 1d 9 KiB
/a-/ALV-UjVLJ…=s120-c-rp-mo-br100(lh3.googleusercontent.com) 1d 9 KiB
/a-/ALV-UjVxz…=s120-c-rp-mo-ba4-br100(lh3.googleusercontent.com) 1d 9 KiB
/a-/ALV-UjUA5…=s120-c-rp-mo-ba3-br100(lh3.googleusercontent.com) 1d 8 KiB
/a-/ALV-UjXXV…=s120-c-rp-mo-ba2-br100(lh3.googleusercontent.com) 1d 7 KiB
/a/ACg8ocKJ9…=s120-c-rp-mo-ba3-br100(lh3.googleusercontent.com) 1d 3 KiB
/a/ACg8ocK2B…=s120-c-rp-mo-ba4-br100(lh3.googleusercontent.com) 1d 3 KiB
/a/ACg8ocIX4…=s120-c-rp-mo-ba2-br100(lh3.googleusercontent.com) 1d 3 KiB
/a/ACg8ocKTf…=s120-c-rp-mo-ba3-br100(lh3.googleusercontent.com) 1d 3 KiB
/a/ACg8ocJlt…=s120-c-rp-mo-ba2-br100(lh3.googleusercontent.com) 1d 3 KiB
/a/ACg8ocIDs…=s120-c-rp-mo-ba3-br100(lh3.googleusercontent.com) 1d 3 KiB
/a/ACg8ocIlm…=s120-c-rp-mo-br100(lh3.googleusercontent.com) 1d 2 KiB
/a/ACg8ocIpK…=s120-c-rp-mo-br100(lh3.googleusercontent.com) 1d 2 KiB
/a/ACg8ocIi_…=s120-c-rp-mo-br100(lh3.googleusercontent.com) 1d 2 KiB
/a/ACg8ocKlz…=s120-c-rp-mo-br100(lh3.googleusercontent.com) 1d 2 KiB
/a/ACg8ocKmV…=s120-c-rp-mo-br100(lh3.googleusercontent.com) 1d 2 KiB
/a/ACg8ocLvN…=s120-c-rp-mo-br100(lh3.googleusercontent.com) 1d 2 KiB
/a/ACg8ocIOh…=s120-c-rp-mo-br100(lh3.googleusercontent.com) 1d 2 KiB
/a/ACg8ocIvu…=s120-c-rp-mo-br100(lh3.googleusercontent.com) 1d 2 KiB
/a/ACg8ocJcB…=s120-c-rp-mo-br100(lh3.googleusercontent.com) 1d 2 KiB
/a/ACg8ocKxL…=s120-c-rp-mo-br100(lh3.googleusercontent.com) 1d 1 KiB bokun.io 1
KiB …flags/gb.png(widgets.bokun.io) 1h 1 KiB Legacy JavaScript Est savings of 76
KiB Polyfills and transforms enable older browsers to use new JavaScript
features. However, many aren't necessary for modern browsers. Consider modifying
your JavaScript build process to not transpile Baseline features, unless you
know you must support older browsers. Learn why most sites can deploy ES6+ code
without transpilingLCPFCP URL Wasted bytes ponyclub.gr 1st party 76.4 KiB
…chunks/797-1d16e539206e578b.js(www.ponyclub.gr) 42.9 KiB
…chunks/797-1d16e539206e578b.js:18:70632(www.ponyclub.gr) Array.prototype.at
…chunks/797-1d16e539206e578b.js:18:70020(www.ponyclub.gr) Array.prototype.flat
…chunks/797-1d16e539206e578b.js:18:70133(www.ponyclub.gr)
Array.prototype.flatMap
…chunks/797-1d16e539206e578b.js:18:70509(www.ponyclub.gr) Object.fromEntries
…chunks/797-1d16e539206e578b.js:18:70767(www.ponyclub.gr) Object.hasOwn
…chunks/797-1d16e539206e578b.js:18:69762(www.ponyclub.gr)
String.prototype.trimEnd
…chunks/797-1d16e539206e578b.js:18:69677(www.ponyclub.gr)
String.prototype.trimStart …chunks/7f358c6e-b3c55055b0d02466.js(www.ponyclub.gr)
33.5 KiB …chunks/7f358c6e-b3c55055b0d02466.js:1:52368(www.ponyclub.gr)
Array.from LCP by phase 3rd parties These insights are also available in the
Chrome DevTools Performance Panel - record a trace to view more detailed
information. Diagnostics Defer offscreen images Est savings of 120 KiB Consider
lazy-loading offscreen and hidden images after all critical resources have
finished loading to lower time to interactive. Learn how to defer offscreen
images.LCPFCP URL Resource Size Est Savings Other Google APIs/SDKs utility 120.1
KiB 120.1 KiB div > div.css-1povu0j > div.css-1fjogy5 > img.css-1pelb8y
<img class="css-1pelb8y" src="https://lh3.googleusercontent.com/a-/ALV-UjXo6A0q597qeUH5nP8wmpHXaL01yNc2w…">
/a-/ALV-UjXo6…=s120-c-rp-mo-br100(lh3.googleusercontent.com) 31.1 KiB 31.1 KiB
div > div.css-1povu0j > div.css-1fjogy5 > img.css-1pelb8y
<img class="css-1pelb8y" src="https://lh3.googleusercontent.com/a-/ALV-UjWBxT2rgteH4h0v7ZwHM9jDZghBMlhU1…">
/a-/ALV-UjWBx…=s120-c-rp-mo-br100(lh3.googleusercontent.com) 29.0 KiB 29.0 KiB
div > div.css-1povu0j > div.css-1fjogy5 > img.css-1pelb8y
<img class="css-1pelb8y" src="https://lh3.googleusercontent.com/a-/ALV-UjXA4lsc4ituHxMAxvrdHK-XwdxjRlCit…">
/a-/ALV-UjXA4…=s120-c-rp-mo-br100(lh3.googleusercontent.com) 28.8 KiB 28.8 KiB
div > div.css-1povu0j > div.css-1fjogy5 > img.css-1pelb8y
<img class="css-1pelb8y" src="https://lh3.googleusercontent.com/a-/ALV-UjUA5N5C4d-LLl2YDed1dwtswJD5_5B6s…">
/a-/ALV-UjUA5…=s120-c-rp-mo-ba3-br100(lh3.googleusercontent.com) 18.9 KiB 18.9
KiB div > div.css-1povu0j > div.css-1fjogy5 > img.css-1pelb8y
<img class="css-1pelb8y" src="https://lh3.googleusercontent.com/a/ACg8ocIi_P7n75neOi2T_W6nRs2_VTIOtKBVuR…">
/a/ACg8ocIi\_…=s120-c-rp-mo-br100(lh3.googleusercontent.com) 4.4 KiB 4.4 KiB
div > div.css-1povu0j > div.css-1fjogy5 > img.css-1pelb8y
<img class="css-1pelb8y" src="https://lh3.googleusercontent.com/a/ACg8ocKlzpWOlPBsWu79xmPTiNx6pAGR8IE5hA…">
/a/ACg8ocKlz…=s120-c-rp-mo-br100(lh3.googleusercontent.com) 4.3 KiB 4.3 KiB
div > div.css-1povu0j > div.css-1fjogy5 > img.css-1pelb8y
<img class="css-1pelb8y" src="https://lh3.googleusercontent.com/a/ACg8ocJcBpyGTl_gDhTURTmpD3DB6dKPEUE3Vc…">
/a/ACg8ocJcB…=s120-c-rp-mo-br100(lh3.googleusercontent.com) 3.5 KiB 3.5 KiB
Avoid serving legacy JavaScript to modern browsers Est savings of 24 KiB
Polyfills and transforms enable legacy browsers to use new JavaScript features.
However, many aren't necessary for modern browsers. Consider modifying your
JavaScript build process to not transpile Baseline features, unless you know you
must support legacy browsers. Learn why most sites can deploy ES6+ code without
transpilingLCPFCP URL Est Savings ponyclub.gr 1st party 24.3 KiB
…chunks/797-1d16e539206e578b.js(www.ponyclub.gr) 13.0 KiB
…chunks/797-1d16e539206e578b.js:18:70632(www.ponyclub.gr) Array.prototype.at
…chunks/797-1d16e539206e578b.js:18:70020(www.ponyclub.gr) Array.prototype.flat
…chunks/797-1d16e539206e578b.js:18:70133(www.ponyclub.gr)
Array.prototype.flatMap
…chunks/797-1d16e539206e578b.js:18:70509(www.ponyclub.gr) Object.fromEntries
…chunks/797-1d16e539206e578b.js:18:70767(www.ponyclub.gr) Object.hasOwn
…chunks/797-1d16e539206e578b.js:18:69762(www.ponyclub.gr)
String.prototype.trimEnd
…chunks/797-1d16e539206e578b.js:18:69677(www.ponyclub.gr)
String.prototype.trimStart …chunks/7f358c6e-b3c55055b0d02466.js(www.ponyclub.gr)
11.0 KiB …chunks/7f358c6e-b3c55055b0d02466.js:1:52368(www.ponyclub.gr)
Array.from …chunks/945-7b4c98ba143510df.js(www.ponyclub.gr) 0.3 KiB
…chunks/945-7b4c98ba143510df.js:1:46621(www.ponyclub.gr)
@babel/plugin-transform-classes Reduce unused JavaScript Est savings of 235 KiB
Reduce unused JavaScript and defer loading scripts until they are required to
decrease bytes consumed by network activity. Learn how to reduce unused
JavaScript.LCPFCP URL Transfer Size Est Savings ponyclub.gr 1st party 155.7 KiB
83.9 KiB …chunks/797-1d16e539206e578b.js(www.ponyclub.gr) 116.6 KiB 49.9 KiB
…chunks/507-8a62939ad235c32b.js(www.ponyclub.gr) 39.1 KiB 34.0 KiB Google Tag
Manager tag-manager 146.3 KiB 80.0 KiB
/gtag/js?id=G-6J3ELVNTQE(www.googletagmanager.com) 146.3 KiB 80.0 KiB bokun.io
138.8 KiB 71.0 KiB /BokunWidgets.91c21ce….js(static.bokun.io) 138.8 KiB 71.0 KiB
Avoid enormous network payloads Total size was 5,112 KiB Large network payloads
cost users real money and are highly correlated with long load times. Learn how
to reduce payload sizes. URL Transfer Size ponyclub.gr 1st party 2,409.8 KiB
/images/hero-video.mp4(www.ponyclub.gr) 1,932.9 KiB
/\_next/image?url=%2Fimages%2Fhero-image.webp&w=1920&q=85(www.ponyclub.gr) 179.8
KiB /images/hero-image.webp(www.ponyclub.gr) 179.6 KiB
…chunks/797-1d16e539206e578b.js(www.ponyclub.gr) 117.5 KiB bokun.io 297.9 KiB
/BokunWidgets.91c21ce….js(static.bokun.io) 139.4 KiB
/OnlineSal….b74eade….js(static.bokun.io) 86.4 KiB
/46929.fb94355….js(static.bokun.io) 72.1 KiB Google Maps utility 161.2 KiB
…api/js?key=AIzaSyB9j…(maps.googleapis.com) 86.7 KiB
…3b/util.js(maps.googleapis.com) 74.5 KiB Google Tag Manager tag-manager 147.4
KiB /gtag/js?id=G-6J3ELVNTQE(www.googletagmanager.com) 147.4 KiB Avoid long
main-thread tasks 5 long tasks found User Timing marks and measures 2 user
timings
