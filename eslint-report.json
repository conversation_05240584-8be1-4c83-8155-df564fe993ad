[{"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/app/[locale]/error.tsx", "messages": [{"ruleId": "no-console", "severity": 1, "message": "Unexpected console statement.", "line": 19, "column": 5, "nodeType": "MemberExpression", "messageId": "unexpected", "endLine": 19, "endColumn": 18, "suggestions": [{"fix": {"range": [472, 492], "text": ""}, "messageId": "removeConsole", "data": {"propertyName": "error"}, "desc": "Remove the console.error()."}]}], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "'use client'\n\nimport Link from 'next/link' // Added Link for logo\nimport { useEffect } from 'react'\n\nimport ResponsiveNavigation from '@/components/responsive-navigation'\nimport { OptimizedImage } from '@/components/ui/OptimizedImage'\nimport { useLanguage } from '@/contexts/language-context'\n\ninterface ErrorProps {\n  error: Error\n  reset: () => void\n}\n\nexport default function Error({ error, reset }: ErrorProps) {\n  const { t } = useLanguage()\n\n  useEffect(() => {\n    console.error(error)\n  }, [error])\n\n  return (\n    <>\n      <header className='fixed top-0 left-0 right-0 z-40 bg-[#FAF7F2] border-b border-gray-200 px-4 sm:px-6 lg:px-8 py-3 flex justify-between items-center'>\n        {/* Logo */}\n        <div>\n          <Link href='/' className='flex items-center'>\n            <div className='relative w-48 h-12 md:w-56 md:h-14 lg:w-64 lg:h-16'>\n              <OptimizedImage\n                src='/images/ponyclub_logo.png'\n                alt='Pony Club Logo'\n                fill\n                sizes='(max-width: 768px) 192px, (max-width: 1024px) 224px, 256px'\n                className='object-contain p-1'\n                imageType='logo'\n              />\n            </div>\n          </Link>\n        </div>\n\n        {/* Responsive Navigation */}\n        <div>\n          <ResponsiveNavigation />\n        </div>\n      </header>\n\n      <main className='flex flex-col items-center justify-center min-h-screen p-4 text-center pt-20 bg-[#f5f0e8]'>\n        {' '}\n        {/* Added bg color and pt-20 */}\n        <h1 className='text-5xl md:text-6xl font-bold mb-4 text-amber-800'>{t.error.title}</h1> {/* Styled heading */}\n        <p className='mb-6 text-lg text-gray-700'>{t.error.message}</p>\n        <button\n          onClick={() => reset()}\n          className='px-6 py-3 font-semibold text-white bg-[#c27a5f] rounded-lg hover:bg-[#b06c50] transition-colors' /* Styled button (using a site color) */\n        >\n          {t.error.tryAgain}\n        </button>\n      </main>\n    </>\n  )\n}\n", "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/app/[locale]/for-schools/page.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/app/[locale]/kayak-rafting/page.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/app/[locale]/kayaking/page.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/app/[locale]/layout.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/app/[locale]/not-found.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/app/[locale]/page.tsx", "messages": [{"ruleId": "@typescript-eslint/no-unused-vars", "severity": 1, "message": "'robotoSlab' is assigned a value but never used. Allowed unused vars must match /^_/u.", "line": 7, "column": 7, "nodeType": null, "messageId": "unusedVar", "endLine": 7, "endColumn": 17}, {"ruleId": "@typescript-eslint/no-unused-vars", "severity": 1, "message": "'locale' is assigned a value but never used. Allowed unused vars must match /^_/u.", "line": 18, "column": 11, "nodeType": null, "messageId": "unusedVar", "endLine": 18, "endColumn": 17}], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "import { Roboto_Slab } from 'next/font/google'\n\nimport HomePageContent from '@/components/HomePageContent'\n\n// This page now uses the global translation system via HomePageContent component\n\nconst robotoSlab = Roboto_Slab({\n  subsets: ['latin', 'greek'],\n  weight: ['400', '700'],\n  variable: '--font-roboto-slab',\n})\n\ninterface PageProps {\n  params: Promise<{ locale: string }>\n}\n\nexport default async function Home({ params }: PageProps) {\n  const { locale } = await params\n\n  return <HomePageContent />\n}\n", "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/app/[locale]/rafting/page.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/app/[locale]/riding/page.tsx", "messages": [{"ruleId": "@typescript-eslint/no-unused-vars", "severity": 1, "message": "'locale' is assigned a value but never used. Allowed unused vars must match /^_/u.", "line": 29, "column": 11, "nodeType": null, "messageId": "unusedVar", "endLine": 29, "endColumn": 17}], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "import type { Metadata } from 'next'\n\nimport ActivityPageLayout from '@/components/ActivityPageLayout'\nimport DynamicBokunWidget from '@/components/DynamicBokunWidget'\n\ninterface PageProps {\n  params: Promise<{ locale: string }>\n}\n\nexport async function generateMetadata({ params }: PageProps): Promise<Metadata> {\n  const { locale } = await params\n\n  const isGreek = locale === 'el'\n\n  return {\n    title: isGreek\n      ? 'Ιππασία στον Αχέροντα - Pony Club | Βόλτες με Άλογα Γλυκή Θεσπρωτίας'\n      : 'Horse Riding in Acheron - Pony Club | Horseback Adventures Glyki Thesprotia',\n    description: isGreek\n      ? 'Απολαύστε μαγικές βόλτες με άλογα στις όχθες του Αχέροντα. Ασφαλείς ιππικές εμπειρίες για όλες τις ηλικίες με εκπαιδευμένα άλογα στη Γλυκή Θεσπρωτίας.'\n      : 'Enjoy magical horseback rides along the banks of Acheron River. Safe equestrian experiences for all ages with trained horses in Glyki, Thesprotia.',\n    keywords: isGreek\n      ? 'ιππασία Αχέροντας, ιππασία Γλυκή, άλογα Θεσπρωτία, ιππασία Ήπειρος, βόλτες με άλογα, ποταμός Αχέροντας'\n      : 'horse riding Acheron, horseback riding Glyki, horses Thesprotia, riding Epirus, Acheron river horseback, Greece horse riding',\n  }\n}\n\nconst RidingPage = async ({ params }: PageProps) => {\n  const { locale } = await params\n  const bokunExperienceId = '1020659' // Riding experience ID\n\n  return (\n    <ActivityPageLayout\n      title='Riding'\n      subtitle=''\n      descriptionTitle=''\n      descriptionContent={<DynamicBokunWidget experienceId={bokunExperienceId} />} // Use DynamicBokunWidget\n      detailsTitle=''\n      detailsContent={<></>}\n      pricingTitle=''\n      pricingContent={<></>}\n      showBookingButton={false}\n      fullWidthContent={true}\n    />\n  )\n}\n\nexport default RidingPage\n", "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/app/[locale]/river-village/page.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/app/[locale]/test-footer/page.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/app/[locale]/trekking/page.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/app/api/csp-violations/route.ts", "messages": [{"ruleId": "no-console", "severity": 1, "message": "Unexpected console statement.", "line": 6, "column": 5, "nodeType": "MemberExpression", "messageId": "unexpected", "endLine": 6, "endColumn": 17, "suggestions": [{"fix": {"range": [143, 181], "text": ""}, "messageId": "removeConsole", "data": {"propertyName": "warn"}, "desc": "Remove the console.warn()."}]}, {"ruleId": "no-console", "severity": 1, "message": "Unexpected console statement.", "line": 11, "column": 5, "nodeType": "MemberExpression", "messageId": "unexpected", "endLine": 11, "endColumn": 18, "suggestions": [{"fix": {"range": [430, 482], "text": ""}, "messageId": "removeConsole", "data": {"propertyName": "error"}, "desc": "Remove the console.error()."}]}], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "import { NextResponse } from 'next/server'\n\nexport async function POST(request: Request) {\n  try {\n    const report = await request.json()\n    console.warn('CSP Violation:', report)\n    // In a real application, you would send this report to a logging service\n    // like Sentry, Report URI, or a custom analytics platform.\n    return NextResponse.json({ message: 'CSP report received' }, { status: 200 })\n  } catch (error) {\n    console.error('Error processing CSP report:', error)\n    return NextResponse.json({ message: 'Error processing report' }, { status: 400 })\n  }\n}\n", "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/app/global-error.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/app/sitemap.ts", "messages": [{"ruleId": "@typescript-eslint/no-unused-vars", "severity": 1, "message": "'routes' is assigned a value but never used. Allowed unused vars must match /^_/u.", "line": 8, "column": 9, "nodeType": null, "messageId": "unusedVar", "endLine": 8, "endColumn": 15}], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "import type { MetadataRoute } from 'next'\n\nimport { routeData } from '@/lib/sitemap-data'\n\nexport default function sitemap(): MetadataRoute.Sitemap {\n  const baseUrl = 'https://ponyclub.gr'\n\n  const routes = routeData.map(({ route, fileMtime }) => ({\n    url: `${baseUrl}${route}`,\n    lastModified: new Date(fileMtime * 1000).toISOString(),\n    changeFrequency: 'weekly' as const,\n    priority: route === '' ? 1 : 0.8,\n  }))\n\n  // Generate localized routes for i18n\n  const locales = ['en', 'el']\n  const localizedRoutes: MetadataRoute.Sitemap = []\n\n  routeData.forEach(({ route, fileMtime }) => {\n    locales.forEach(locale => {\n      // Ensure the path starts with a slash if it's not empty\n      const pathSegment = route.startsWith('/') ? route : `/${route}`\n      // Handle the root path correctly for locales\n      const finalPath = route === '' ? `/${locale}` : `/${locale}${pathSegment}`\n\n      localizedRoutes.push({\n        url: `${baseUrl}${finalPath}`,\n        lastModified: new Date(fileMtime * 1000).toISOString(),\n        changeFrequency: 'weekly' as const,\n        priority: route === '' ? 1 : 0.8,\n      })\n    })\n  })\n\n  return localizedRoutes\n}\n", "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ActivityPageLayout.tsx", "messages": [{"ruleId": "@typescript-eslint/no-unused-vars", "severity": 1, "message": "'OptimizedImage' is defined but never used. Allowed unused vars must match /^_/u.", "line": 7, "column": 10, "nodeType": null, "messageId": "unusedVar", "endLine": 7, "endColumn": 24}, {"ruleId": "@typescript-eslint/no-unused-vars", "severity": 1, "message": "'title' is defined but never used. Allowed unused args must match /^_/u.", "line": 31, "column": 3, "nodeType": null, "messageId": "unusedVar", "endLine": 31, "endColumn": 8}, {"ruleId": "@typescript-eslint/no-unused-vars", "severity": 1, "message": "'subtitle' is defined but never used. Allowed unused args must match /^_/u.", "line": 32, "column": 3, "nodeType": null, "messageId": "unusedVar", "endLine": 32, "endColumn": 11}, {"ruleId": "@typescript-eslint/no-unused-vars", "severity": 1, "message": "'descriptionTitle' is defined but never used. Allowed unused args must match /^_/u.", "line": 33, "column": 3, "nodeType": null, "messageId": "unusedVar", "endLine": 33, "endColumn": 19}, {"ruleId": "@typescript-eslint/no-unused-vars", "severity": 1, "message": "'showBookingButton' is assigned a value but never used. Allowed unused args must match /^_/u.", "line": 39, "column": 3, "nodeType": null, "messageId": "unusedVar", "endLine": 39, "endColumn": 20}, {"ruleId": "@typescript-eslint/no-unused-vars", "severity": 1, "message": "'fullWidthContent' is assigned a value but never used. Allowed unused args must match /^_/u.", "line": 41, "column": 3, "nodeType": null, "messageId": "unusedVar", "endLine": 41, "endColumn": 19}], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "import { Roboto_Slab } from 'next/font/google'\n\n// ResponsiveNavigation and Link/OptimizedImage for logo are no longer needed here directly.\n// SiteHeader is now part of PageLayout.\n// OptimizedImage might be used elsewhere if other image components are part of the content.\nimport { Container } from './ui/Container' // Import the Container component\nimport { OptimizedImage } from './ui/OptimizedImage'\n\n// Define Roboto Slab font instance\nconst robotoSlab = Roboto_Slab({\n  subsets: ['latin', 'greek'],\n  variable: '--font-roboto-slab',\n  weight: ['400', '700', '900'],\n})\n\ninterface ActivityPageLayoutProps {\n  title: string\n  subtitle: string\n  descriptionTitle: string\n  descriptionContent: React.ReactNode\n  detailsTitle: string\n  detailsContent: React.ReactNode\n  pricingTitle: string\n  pricingContent: React.ReactNode\n  showBookingButton?: boolean\n  useSingleColumn?: boolean\n  fullWidthContent?: boolean\n}\n\nexport default function ActivityPageLayout({\n  title,\n  subtitle,\n  descriptionTitle,\n  descriptionContent,\n  detailsTitle,\n  detailsContent,\n  pricingTitle,\n  pricingContent,\n  showBookingButton = true,\n  useSingleColumn = false,\n  fullWidthContent = false,\n}: ActivityPageLayoutProps) {\n  // Determine content max-width class based on column preference\n  const contentMaxWidthClass = useSingleColumn ? 'max-w-none' : 'max-w-none'\n\n  return (\n    <>\n      {/*\n        SiteHeader is now rendered by PageLayout.\n        The main PageLayout's <main> tag has pt-20 and the background color.\n        This component's content will be rendered inside that <main> tag.\n        The containerClasses will define the layout for the content of this specific activity page.\n        Adjusting pt-24 to pt-4 in containerClasses to provide a small top padding for the content block itself,\n        relative to the PageLayout's main content area.\n      */}\n      <Container className='py-6'>\n        <div className='flex flex-col gap-8'>\n          {/* Description Section */}\n          {descriptionContent && (\n            <div className='relative bg-white/80 backdrop-blur-sm p-6 rounded-lg shadow-lg border border-amber-100/70 hover:shadow-xl transition-shadow duration-300'>\n              <div className={`prose ${contentMaxWidthClass} text-gray-700`}>{descriptionContent}</div>\n              <div className='absolute -inset-[1px] -z-10 rounded-lg bg-gradient-to-tr from-amber-200/20 via-white/50 to-[#6b8362]/20 blur-sm'></div>\n            </div>\n          )}\n\n          {/* Details Section - Always inside container */}\n          {detailsTitle && detailsContent && (\n            <div className='relative bg-white/80 backdrop-blur-sm p-6 rounded-lg shadow-lg border border-amber-100/70 hover:shadow-xl transition-shadow duration-300'>\n              <h2\n                className={`${robotoSlab.variable} font-roboto-slab text-2xl font-bold text-amber-800 mb-4 relative inline-block`}\n              >\n                {detailsTitle}\n                <div className='absolute -bottom-1 left-0 w-full h-[2px] bg-gradient-to-r from-transparent via-amber-500/50 to-transparent'></div>\n              </h2>\n              <div className={`prose ${contentMaxWidthClass} text-gray-700`}>{detailsContent}</div>\n              <div className='absolute -inset-[1px] -z-10 rounded-lg bg-gradient-to-tr from-amber-200/20 via-white/50 to-[#6b8362]/20 blur-sm'></div>\n            </div>\n          )}\n\n          {/* Pricing Section */}\n          {pricingTitle && pricingContent && (\n            <div className='relative bg-white/80 backdrop-blur-sm p-6 rounded-lg shadow-lg border border-amber-100/70 hover:shadow-xl transition-shadow duration-300'>\n              <h2\n                className={`${robotoSlab.variable} font-roboto-slab text-2xl font-bold text-amber-800 mb-4 relative inline-block`}\n              >\n                {pricingTitle}\n                <div className='absolute -bottom-1 left-0 w-full h-[2px] bg-gradient-to-r from-transparent via-amber-500/50 to-transparent'></div>\n              </h2>\n              <div className={`prose ${contentMaxWidthClass} text-gray-700`}>{pricingContent}</div>\n              <div className='absolute -inset-[1px] -z-10 rounded-lg bg-gradient-to-tr from-amber-200/20 via-white/50 to-[#6b8362]/20 blur-sm'></div>\n            </div>\n          )}\n\n          {/* If pricingTitle is not provided but pricingContent is, show just the content */}\n          {!pricingTitle && pricingContent && (\n            <div className='relative bg-white/80 backdrop-blur-sm p-6 rounded-lg shadow-lg border border-amber-100/70 hover:shadow-xl transition-shadow duration-300'>\n              <div className={`prose ${contentMaxWidthClass} text-gray-700`}>{pricingContent}</div>\n              <div className='absolute -inset-[1px] -z-10 rounded-lg bg-gradient-to-tr from-amber-200/20 via-white/50 to-[#6b8362]/20 blur-sm'></div>\n            </div>\n          )}\n        </div>\n      </Container>\n    </>\n  )\n}\n", "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/BokunWidget.tsx", "messages": [{"ruleId": "@typescript-eslint/no-unused-vars", "severity": 1, "message": "'IFramePage' is defined but never used. Allowed unused vars must match /^_/u.", "line": 4, "column": 25, "nodeType": null, "messageId": "unusedVar", "endLine": 4, "endColumn": 35}, {"ruleId": "no-console", "severity": 1, "message": "Unexpected console statement.", "line": 26, "column": 7, "nodeType": "MemberExpression", "messageId": "unexpected", "endLine": 26, "endColumn": 18, "suggestions": [{"fix": {"range": [934, 995], "text": ""}, "messageId": "removeConsole", "data": {"propertyName": "log"}, "desc": "Remove the console.log()."}]}, {"ruleId": "no-console", "severity": 1, "message": "Unexpected console statement.", "line": 30, "column": 5, "nodeType": "MemberExpression", "messageId": "unexpected", "endLine": 30, "endColumn": 16, "suggestions": [{"fix": {"range": [1020, 1114], "text": ""}, "messageId": "removeConsole", "data": {"propertyName": "log"}, "desc": "Remove the console.log()."}]}, {"ruleId": "@typescript-eslint/no-explicit-any", "severity": 1, "message": "Unexpected any. Specify a different type.", "line": 33, "column": 71, "nodeType": "TSAnyKeyword", "messageId": "unexpectedAny", "endLine": 33, "endColumn": 74, "suggestions": [{"messageId": "suggestUnknown", "fix": {"range": [1223, 1226], "text": "unknown"}, "desc": "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct."}, {"messageId": "suggestNever", "fix": {"range": [1223, 1226], "text": "never"}, "desc": "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of."}]}, {"ruleId": "@typescript-eslint/no-explicit-any", "severity": 1, "message": "Unexpected any. Specify a different type.", "line": 40, "column": 70, "nodeType": "TSAnyKeyword", "messageId": "unexpectedAny", "endLine": 40, "endColumn": 73, "suggestions": [{"messageId": "suggestUnknown", "fix": {"range": [1663, 1666], "text": "unknown"}, "desc": "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct."}, {"messageId": "suggestNever", "fix": {"range": [1663, 1666], "text": "never"}, "desc": "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of."}]}, {"ruleId": "no-console", "severity": 1, "message": "Unexpected console statement.", "line": 41, "column": 9, "nodeType": "MemberExpression", "messageId": "unexpected", "endLine": 41, "endColumn": 20, "suggestions": [{"fix": {"range": [1683, 1782], "text": ""}, "messageId": "removeConsole", "data": {"propertyName": "log"}, "desc": "Remove the console.log()."}]}, {"ruleId": "no-console", "severity": 1, "message": "Unexpected console statement.", "line": 45, "column": 9, "nodeType": "MemberExpression", "messageId": "unexpected", "endLine": 45, "endColumn": 20, "suggestions": [{"fix": {"range": [1981, 2052], "text": ""}, "messageId": "removeConsole", "data": {"propertyName": "log"}, "desc": "Remove the console.log()."}]}, {"ruleId": "no-console", "severity": 1, "message": "Unexpected console statement.", "line": 48, "column": 9, "nodeType": "MemberExpression", "messageId": "unexpected", "endLine": 48, "endColumn": 20, "suggestions": [{"fix": {"range": [2119, 2204], "text": ""}, "messageId": "removeConsole", "data": {"propertyName": "log"}, "desc": "Remove the console.log()."}]}, {"ruleId": "@typescript-eslint/no-explicit-any", "severity": 1, "message": "Unexpected any. Specify a different type.", "line": 55, "column": 21, "nodeType": "TSAnyKeyword", "messageId": "unexpectedAny", "endLine": 55, "endColumn": 24, "suggestions": [{"messageId": "suggestUnknown", "fix": {"range": [2491, 2494], "text": "unknown"}, "desc": "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct."}, {"messageId": "suggestNever", "fix": {"range": [2491, 2494], "text": "never"}, "desc": "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of."}]}, {"ruleId": "@typescript-eslint/no-explicit-any", "severity": 1, "message": "Unexpected any. Specify a different type.", "line": 56, "column": 19, "nodeType": "TSAnyKeyword", "messageId": "unexpectedAny", "endLine": 56, "endColumn": 22, "suggestions": [{"messageId": "suggestUnknown", "fix": {"range": [2531, 2534], "text": "unknown"}, "desc": "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct."}, {"messageId": "suggestNever", "fix": {"range": [2531, 2534], "text": "never"}, "desc": "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of."}]}, {"ruleId": "@typescript-eslint/no-explicit-any", "severity": 1, "message": "Unexpected any. Specify a different type.", "line": 58, "column": 17, "nodeType": "TSAnyKeyword", "messageId": "unexpectedAny", "endLine": 58, "endColumn": 20, "suggestions": [{"messageId": "suggestUnknown", "fix": {"range": [2577, 2580], "text": "unknown"}, "desc": "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct."}, {"messageId": "suggestNever", "fix": {"range": [2577, 2580], "text": "never"}, "desc": "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of."}]}, {"ruleId": "@typescript-eslint/no-explicit-any", "severity": 1, "message": "Unexpected any. Specify a different type.", "line": 58, "column": 100, "nodeType": "TSAnyKeyword", "messageId": "unexpectedAny", "endLine": 58, "endColumn": 103, "suggestions": [{"messageId": "suggestUnknown", "fix": {"range": [2660, 2663], "text": "unknown"}, "desc": "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct."}, {"messageId": "suggestNever", "fix": {"range": [2660, 2663], "text": "never"}, "desc": "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of."}]}, {"ruleId": "no-console", "severity": 1, "message": "Unexpected console statement.", "line": 59, "column": 7, "nodeType": "MemberExpression", "messageId": "unexpected", "endLine": 59, "endColumn": 18, "suggestions": [{"fix": {"range": [2678, 2855], "text": ""}, "messageId": "removeConsole", "data": {"propertyName": "log"}, "desc": "Remove the console.log()."}]}, {"ruleId": "@typescript-eslint/no-explicit-any", "severity": 1, "message": "Unexpected any. Specify a different type.", "line": 70, "column": 17, "nodeType": "TSAnyKeyword", "messageId": "unexpectedAny", "endLine": 70, "endColumn": 20, "suggestions": [{"messageId": "suggestUnknown", "fix": {"range": [3008, 3011], "text": "unknown"}, "desc": "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct."}, {"messageId": "suggestNever", "fix": {"range": [3008, 3011], "text": "never"}, "desc": "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of."}]}, {"ruleId": "@typescript-eslint/no-explicit-any", "severity": 1, "message": "Unexpected any. Specify a different type.", "line": 71, "column": 17, "nodeType": "TSAnyKeyword", "messageId": "unexpectedAny", "endLine": 71, "endColumn": 20, "suggestions": [{"messageId": "suggestUnknown", "fix": {"range": [3073, 3076], "text": "unknown"}, "desc": "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct."}, {"messageId": "suggestNever", "fix": {"range": [3073, 3076], "text": "never"}, "desc": "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of."}]}, {"ruleId": "no-console", "severity": 1, "message": "Unexpected console statement.", "line": 77, "column": 9, "nodeType": "MemberExpression", "messageId": "unexpected", "endLine": 77, "endColumn": 20, "suggestions": [{"fix": {"range": [3457, 3562], "text": ""}, "messageId": "removeConsole", "data": {"propertyName": "log"}, "desc": "Remove the console.log()."}]}, {"ruleId": "no-console", "severity": 1, "message": "Unexpected console statement.", "line": 81, "column": 11, "nodeType": "MemberExpression", "messageId": "unexpected", "endLine": 81, "endColumn": 22, "suggestions": [{"fix": {"range": [3719, 3803], "text": ""}, "messageId": "removeConsole", "data": {"propertyName": "log"}, "desc": "Remove the console.log()."}]}, {"ruleId": "no-console", "severity": 1, "message": "Unexpected console statement.", "line": 83, "column": 11, "nodeType": "MemberExpression", "messageId": "unexpected", "endLine": 83, "endColumn": 24, "suggestions": [{"fix": {"range": [3840, 3940], "text": ""}, "messageId": "removeConsole", "data": {"propertyName": "error"}, "desc": "Remove the console.error()."}]}, {"ruleId": "no-console", "severity": 1, "message": "Unexpected console statement.", "line": 87, "column": 9, "nodeType": "MemberExpression", "messageId": "unexpected", "endLine": 87, "endColumn": 20, "suggestions": [{"fix": {"range": [4127, 4210], "text": ""}, "messageId": "removeConsole", "data": {"propertyName": "log"}, "desc": "Remove the console.log()."}]}, {"ruleId": "no-console", "severity": 1, "message": "Unexpected console statement.", "line": 90, "column": 9, "nodeType": "MemberExpression", "messageId": "unexpected", "endLine": 90, "endColumn": 20, "suggestions": [{"fix": {"range": [4272, 4369], "text": ""}, "messageId": "removeConsole", "data": {"propertyName": "log"}, "desc": "Remove the console.log()."}]}, {"ruleId": "no-console", "severity": 1, "message": "Unexpected console statement.", "line": 94, "column": 5, "nodeType": "MemberExpression", "messageId": "unexpected", "endLine": 94, "endColumn": 16, "suggestions": [{"fix": {"range": [4390, 4487], "text": ""}, "messageId": "removeConsole", "data": {"propertyName": "log"}, "desc": "Remove the console.log()."}]}, {"ruleId": "@typescript-eslint/no-unused-vars", "severity": 1, "message": "'mutationsList' is defined but never used. Allowed unused args must match /^_/u.", "line": 98, "column": 48, "nodeType": null, "messageId": "unusedVar", "endLine": 98, "endColumn": 61}, {"ruleId": "@typescript-eslint/no-unused-vars", "severity": 1, "message": "'observerInstance' is defined but never used. Allowed unused args must match /^_/u.", "line": 98, "column": 63, "nodeType": null, "messageId": "unusedVar", "endLine": 98, "endColumn": 79}, {"ruleId": "no-console", "severity": 1, "message": "Unexpected console statement.", "line": 102, "column": 9, "nodeType": "MemberExpression", "messageId": "unexpected", "endLine": 102, "endColumn": 20, "suggestions": [{"fix": {"range": [4988, 5091], "text": ""}, "messageId": "removeConsole", "data": {"propertyName": "log"}, "desc": "Remove the console.log()."}]}, {"ruleId": "no-console", "severity": 1, "message": "Unexpected console statement.", "line": 106, "column": 11, "nodeType": "MemberExpression", "messageId": "unexpected", "endLine": 106, "endColumn": 22, "suggestions": [{"fix": {"range": [5238, 5317], "text": ""}, "messageId": "removeConsole", "data": {"propertyName": "log"}, "desc": "Remove the console.log()."}]}, {"ruleId": "no-console", "severity": 1, "message": "Unexpected console statement.", "line": 110, "column": 11, "nodeType": "MemberExpression", "messageId": "unexpected", "endLine": 110, "endColumn": 24, "suggestions": [{"fix": {"range": [5476, 5571], "text": ""}, "messageId": "removeConsole", "data": {"propertyName": "error"}, "desc": "Remove the console.error()."}]}, {"ruleId": "no-console", "severity": 1, "message": "Unexpected console statement.", "line": 117, "column": 5, "nodeType": "MemberExpression", "messageId": "unexpected", "endLine": 117, "endColumn": 16, "suggestions": [{"fix": {"range": [5749, 5845], "text": ""}, "messageId": "removeConsole", "data": {"propertyName": "log"}, "desc": "Remove the console.log()."}]}, {"ruleId": "no-console", "severity": 1, "message": "Unexpected console statement.", "line": 123, "column": 7, "nodeType": "MemberExpression", "messageId": "unexpected", "endLine": 123, "endColumn": 18, "suggestions": [{"fix": {"range": [6082, 6162], "text": ""}, "messageId": "removeConsole", "data": {"propertyName": "log"}, "desc": "Remove the console.log()."}]}], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 28, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "'use client'\n\nimport type { IFrameOptions } from 'iframe-resizer'\nimport { iframeResizer, IFramePage } from 'iframe-resizer' // Types for v3/v4\nimport React, { useEffect, useRef } from 'react'\n\nimport { useLanguage } from '@/contexts/language-context'\nimport { bokunLangMap } from '@/lib/bokun-lang'\n\ntype BokunWidgetProps = {\n  experienceId: string\n  partialView?: number\n}\n\nexport default function BokunWidget({ experienceId, partialView = 1 }: BokunWidgetProps) {\n  const { language } = useLanguage()\n  const bokunLang = bokunLangMap[language] || 'en'\n  const widgetContainerRef = useRef<HTMLDivElement>(null)\n\n  const baseUrl = 'https://widgets.bokun.io/online-sales/c078b762-6f7f-474f-8edb-bdd1bdb7d12a/experience'\n  const bokunWidgetSrcUrl = `${baseUrl}/${experienceId}?partialView=${partialView}&lang=${bokunLang}`\n\n  useEffect(() => {\n    const containerElement = widgetContainerRef.current\n    if (!containerElement) {\n      console.log('[Bokun Widget Parent] Container ref not found.')\n      return\n    }\n\n    console.log('[Bokun Widget Parent] useEffect triggered. Container element:', containerElement)\n\n    const options: IFrameOptions & {\n      onMessage?: (messageData: { iframe: HTMLIFrameElement; message: any }) => void\n      onResized?: (sizeData: { iframe: HTMLIFrameElement; height: number; width: number; type: string }) => void\n      onInit?: (iFrameEl: HTMLIFrameElement) => void\n    } = {\n      log: process.env.NODE_ENV === 'development', // Enable logs only in dev\n      checkOrigin: false, // Be cautious in production\n      // Updated to use current callback names\n      onMessage: (messageData: { iframe: HTMLIFrameElement; message: any }) => {\n        console.log('[Bokun Widget Parent] Received message from iframe (onMessage):', messageData.message)\n        // Potentially handle specific messages if needed in the future\n      },\n      onResized: (sizeData: { iframe: HTMLIFrameElement; height: number; width: number; type: string }) => {\n        console.log('[Bokun Widget Parent] iframeResizer onResized:', sizeData)\n      },\n      onInit: (iFrameEl: HTMLIFrameElement) => {\n        console.log('[Bokun Widget Parent] iframeResizer onInit: iframe is ready.', iFrameEl)\n      },\n    }\n\n    // Ensure window.iFrameResizer exists and has onMessage, onResized, onInit\n    // This is a workaround for the \"onMessage function not defined\" warning\n    // if the Bokun widget's internal iframe-resizer script is looking for a global function.\n    if (!(window as any).iFrameResizer) {\n      ;(window as any).iFrameResizer = {}\n    }\n    ;(window as any).iFrameResizer.onMessage = (messageData: { iframe: HTMLIFrameElement; message: any }) => {\n      console.log(\n        '[Bokun Widget Parent] window.iFrameResizer.onMessage received:',\n        messageData.message,\n        'from iframe:',\n        messageData.iframe.id\n      )\n      // Call the options.onMessage handler as well\n      if (options.onMessage) {\n        options.onMessage(messageData)\n      }\n    }\n    ;(window as any).iFrameResizer.onResized = options.onResized\n    ;(window as any).iFrameResizer.onInit = options.onInit\n\n    // 1. Initialize iframeResizer for the iframe embedded within this component (catalogue view)\n    const catalogueObserver = new MutationObserver((mutationsList, observerInstance) => {\n      const catalogueIframe = containerElement.querySelector('iframe')\n      if (catalogueIframe && !catalogueIframe.dataset.resizerAttached) {\n        console.log('[Bokun Widget Parent] Found catalogue iframe, initializing iframeResizer:', catalogueIframe)\n        try {\n          iframeResizer(options, catalogueIframe)\n          catalogueIframe.dataset.resizerAttached = 'true' // Mark as initialized\n          console.log('[Bokun Widget Parent] iframeResizer initialized for catalogue iframe.')\n        } catch (error) {\n          console.error('[Bokun Widget Parent] Error initializing iframeResizer for catalogue iframe:', error)\n        }\n        observerInstance.disconnect() // Stop observing once the catalogue iframe is found and initialized\n      } else if (catalogueIframe?.dataset.resizerAttached) {\n        console.log('[Bokun Widget Parent] Catalogue iframe already has resizer attached.')\n        observerInstance.disconnect()\n      } else {\n        console.log('[Bokun Widget Parent] Catalogue iframe not found yet in MutationObserver callback.')\n      }\n    })\n\n    console.log('[Bokun Widget Parent] Starting MutationObserver on container for catalogue iframe.')\n    catalogueObserver.observe(containerElement, { childList: true, subtree: true })\n\n    // 2. Initialize iframeResizer for the cart iframe injected into the body by Bokun\n    const bodyObserver = new MutationObserver((mutationsList, observerInstance) => {\n      const cartIframe = document.getElementById('bokun-widgets-cart') as HTMLIFrameElement | null\n      // Check if the cart iframe exists and hasn't been initialized yet\n      if (cartIframe && !cartIframe.dataset.resizerAttached) {\n        console.log('[Bokun Widget Parent] Found cart iframe in body, initializing iframeResizer:', cartIframe)\n        try {\n          iframeResizer(options, cartIframe)\n          cartIframe.dataset.resizerAttached = 'true' // Mark as initialized\n          console.log('[Bokun Widget Parent] iframeResizer initialized for cart iframe.')\n          // Optional: Disconnect if you only expect one cart iframe instance\n          // observerInstance.disconnect();\n        } catch (error) {\n          console.error('[Bokun Widget Parent] Error initializing iframeResizer for cart iframe:', error)\n        }\n      } else if (cartIframe?.dataset.resizerAttached) {\n        // console.log(\"[Bokun Widget Parent] Cart iframe already has resizer attached.\");\n      }\n    })\n\n    console.log('[Bokun Widget Parent] Starting MutationObserver on document body for cart iframe.')\n    // Observe the body for additions/removals, including the cart iframe\n    bodyObserver.observe(document.body, { childList: true, subtree: false }) // Observe direct children of body\n\n    // Cleanup function\n    return () => {\n      console.log('[Bokun Widget Parent] useEffect cleanup: Disconnecting observers.')\n      catalogueObserver.disconnect()\n      bodyObserver.disconnect()\n      // Note: iframe-resizer v4 might need manual cleanup if iframes are removed dynamically.\n      // e.g., iframeResizer.iframeResizer.close(iframeElement)\n      // For now, relying on component unmount and observer disconnect.\n    }\n  }, [bokunWidgetSrcUrl]) // Re-run if the URL changes\n\n  return (\n    <>\n      <div\n        ref={widgetContainerRef}\n        className='bokunWidget'\n        data-src={bokunWidgetSrcUrl}\n        data-lang={bokunLang}\n        style={{ width: '100%', minHeight: '500px' }} // Ensure div has dimensions\n      >\n        {/* Bokun's script will inject the iframe here */}\n      </div>\n      <noscript>Please enable javascript in your browser to book</noscript>\n    </>\n  )\n}\n", "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ClientLayout.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/DynamicBokunWidget.tsx", "messages": [{"ruleId": "react-hooks/exhaustive-deps", "severity": 1, "message": "The ref value 'ref.current' will likely have changed by the time this effect cleanup function runs. If this ref points to a node rendered by React, copy 'ref.current' to a variable inside the effect, and use that variable in the cleanup function.", "line": 43, "column": 32, "nodeType": "Identifier", "endLine": 43, "endColumn": 39}], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "'use client'\n\nimport dynamic from 'next/dynamic'\nimport React, { useState, useEffect, useRef } from 'react'\n\n// Dynamically import BokunWidget with ssr: false and a loading placeholder\nconst BokunWidget = dynamic(() => import('@/components/BokunWidget'), {\n  ssr: false,\n  loading: () => <div className='h-96 w-full bg-gray-200 animate-pulse rounded-lg' />,\n})\n\ntype DynamicBokunWidgetProps = {\n  experienceId: string\n  partialView?: number\n}\n\nexport default function DynamicBokunWidget({ experienceId, partialView }: DynamicBokunWidgetProps) {\n  const [shouldLoad, setShouldLoad] = useState(false)\n  const ref = useRef<HTMLDivElement>(null)\n\n  useEffect(() => {\n    if (!ref.current) return\n\n    const observer = new IntersectionObserver(\n      ([entry]) => {\n        // Load when the placeholder is intersecting or nearly intersecting\n        if (entry.isIntersecting) {\n          setShouldLoad(true)\n          observer.disconnect() // Stop observing once loaded\n        }\n      },\n      {\n        rootMargin: '200px 0px', // Load when 200px away from viewport\n        threshold: 0.01, // Trigger even if only 1% is visible\n      }\n    )\n\n    observer.observe(ref.current)\n\n    // Cleanup observer on component unmount\n    return () => {\n      if (observer && ref.current) {\n        observer.unobserve(ref.current)\n      }\n      observer.disconnect()\n    }\n  }, []) // Empty dependency array ensures this runs only once on mount\n\n  return (\n    <div ref={ref} style={{ minHeight: '384px' }}>\n      {' '}\n      {/* Wrapper div for observer, with min-height matching placeholder */}\n      {shouldLoad ? (\n        <BokunWidget experienceId={experienceId} partialView={partialView} />\n      ) : (\n        // Render the loading placeholder defined in dynamic import\n        <div className='h-96 w-full bg-gray-200 animate-pulse rounded-lg' />\n      )}\n    </div>\n  )\n}\n", "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/DynamicContactDetails.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/DynamicGallery.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/DynamicGoogleMap.tsx", "messages": [{"ruleId": "react-hooks/exhaustive-deps", "severity": 1, "message": "The ref value 'mapRef.current' will likely have changed by the time this effect cleanup function runs. If this ref points to a node rendered by React, copy 'mapRef.current' to a variable inside the effect, and use that variable in the cleanup function.", "line": 37, "column": 35, "nodeType": "Identifier", "endLine": 37, "endColumn": 42}], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "'use client'\n\nimport dynamic from 'next/dynamic'\nimport React, { useState, useEffect, useRef } from 'react'\n\n// Dynamically import GoogleMap with ssr: false\nconst GoogleMapComponent = dynamic(() => import('@/components/google-map'), {\n  ssr: false,\n  loading: () => (\n    <div className='h-[400px] w-full bg-gray-200 animate-pulse rounded-lg shadow-xl border border-amber-100/70' />\n  ),\n})\n\nexport default function DynamicGoogleMap() {\n  const [isIntersecting, setIsIntersecting] = useState(false)\n  const mapRef = useRef<HTMLDivElement | null>(null)\n\n  useEffect(() => {\n    const observer = new IntersectionObserver(\n      ([entry]) => {\n        if (entry.isIntersecting) {\n          setIsIntersecting(true)\n          observer.unobserve(entry.target) // Stop observing once visible\n        }\n      },\n      {\n        rootMargin: '100px', // Load when the map is 100px away from viewport\n      }\n    )\n\n    if (mapRef.current) {\n      observer.observe(mapRef.current)\n    }\n\n    return () => {\n      if (mapRef.current) {\n        observer.unobserve(mapRef.current)\n      }\n    }\n  }, [])\n\n  return (\n    <div ref={mapRef} className='h-[400px] w-full'>\n      {' '}\n      {/* Container for observer and to maintain space */}\n      {isIntersecting ? (\n        <GoogleMapComponent />\n      ) : (\n        <div className='h-[400px] w-full bg-gray-200 animate-pulse rounded-lg shadow-xl border border-amber-100/70' />\n      )}\n    </div>\n  )\n}\n", "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/EnhancedPackageCard.tsx", "messages": [{"ruleId": "@typescript-eslint/no-unused-vars", "severity": 1, "message": "'useLanguage' is defined but never used. Allowed unused vars must match /^_/u.", "line": 8, "column": 10, "nodeType": null, "messageId": "unusedVar", "endLine": 8, "endColumn": 21}], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "'use client'\n\nimport { Waves, MountainSnow, Sailboat, User } from 'lucide-react'\nimport { useState, useEffect } from 'react'\n\nimport BookingButton from '@/components/client/BookingButton'\nimport { OptimizedImage } from '@/components/ui/OptimizedImage'\nimport { useLanguage } from '@/contexts/language-context'\n\ninterface PackageCardProps {\n  title: string\n  badge: string\n  activities: {\n    primary: string\n    riding: string\n    hiking: string\n  }\n  pricing: {\n    adults?: string\n    children?: string\n    perPerson?: string\n  }\n  images: {\n    main: string\n    top: string\n    bottom: string\n  }\n  bookingId: string\n  dataSrc: string\n  bookNowText: string\n  packageName: string\n  packagePrice: string\n  trackingLabel: string\n  variant: 'green' | 'amber'\n}\n\nexport default function EnhancedPackageCard({\n  title,\n  badge,\n  activities,\n  pricing,\n  images,\n  bookingId,\n  dataSrc,\n  bookNowText,\n  packageName,\n  packagePrice,\n  trackingLabel,\n  variant,\n}: PackageCardProps) {\n  const [nonce, setNonce] = useState('')\n\n  useEffect(() => {\n    // Get nonce from meta tag\n    const metaNonce = document.querySelector('meta[name=\"csp-nonce\"]')?.getAttribute('content') || ''\n    setNonce(metaNonce)\n  }, [])\n  const colorTheme = {\n    green: {\n      gradient: 'from-emerald-400 via-teal-500 to-cyan-600',\n      badge: 'bg-yellow-400 text-yellow-900',\n      glass: 'bg-white/10',\n      border: 'border-white/15',\n      icon: 'text-emerald-300',\n      priceBox: 'bg-white/20',\n      button: 'from-emerald-500 to-green-500 hover:from-emerald-600 hover:to-green-600',\n      text: 'text-emerald-50',\n    },\n    amber: {\n      gradient: 'from-amber-400 via-orange-500 to-red-500',\n      badge: 'bg-blue-400 text-blue-900',\n      glass: 'bg-white/10',\n      border: 'border-white/15',\n      icon: 'text-amber-300',\n      priceBox: 'bg-white/20',\n      button: 'from-amber-500 to-orange-500 hover:from-amber-600 hover:to-orange-600',\n      text: 'text-amber-50',\n    },\n  }\n\n  const theme = colorTheme[variant]\n\n  const renderActivityIcon = (index: number) => {\n    if (index === 0) {\n      return variant === 'green' ? (\n        <Waves className='w-3 h-3 sm:w-4 sm:h-4 md:w-5 md:h-5 text-white' />\n      ) : (\n        <Sailboat className='w-3 h-3 sm:w-4 sm:h-4 md:w-5 md:h-5 text-white' />\n      )\n    }\n    return <MountainSnow className='w-3 h-3 sm:w-4 sm:h-4 md:w-5 md:h-5 text-white' />\n  }\n\n  return (\n    <div className={`bg-gradient-to-tr ${theme.gradient} p-1 rounded-3xl shadow-2xl w-full max-w-md mx-auto`}>\n      <div\n        className={`${theme.glass} backdrop-blur-xl border ${theme.border} shadow-2xl rounded-3xl overflow-hidden w-full h-full flex flex-col`}\n      >\n        <div className='p-4 sm:p-6 md:p-8 flex flex-col h-full min-h-[580px] sm:min-h-[620px]'>\n          {/* Header */}\n          <div className='flex justify-between items-start mb-4 flex-shrink-0'>\n            <h2 className='text-2xl sm:text-3xl font-bold text-white'>{title}</h2>\n            <span\n              className={`${theme.badge} text-xs sm:text-sm font-semibold px-3 py-1.5 rounded-full shadow-md whitespace-nowrap`}\n            >\n              {badge}\n            </span>\n          </div>\n\n          {/* Image Mosaic */}\n          <div className='mosaic-grid h-44 sm:h-52 mb-4 sm:mb-6 rounded-xl overflow-hidden border border-white/20 flex-shrink-0'>\n            <div className='relative mosaic-main'>\n              <OptimizedImage\n                src={images.main}\n                alt={`${title} main activity`}\n                fill\n                sizes='(max-width: 512px) 45vw, 240px'\n                className='w-full h-full object-cover'\n                imageType='default'\n              />\n            </div>\n            <div className='relative mosaic-top'>\n              <OptimizedImage\n                src={images.top}\n                alt={`${title} activity 2`}\n                fill\n                sizes='(max-width: 512px) 45vw, 240px'\n                className='w-full h-full object-cover'\n                imageType='default'\n              />\n            </div>\n            <div className='relative mosaic-bottom'>\n              <OptimizedImage\n                src={images.bottom}\n                alt={`${title} activity 3`}\n                fill\n                sizes='(max-width: 512px) 45vw, 240px'\n                className='w-full h-full object-cover'\n                imageType='default'\n              />\n            </div>\n          </div>\n\n          {/* Activities List */}\n          <ul className={`space-y-2 sm:space-y-3 ${theme.text} mb-6 sm:mb-8 flex-grow`}>\n            {[activities.primary, activities.riding, activities.hiking].map((activity, index) => (\n              <li key={index} className='flex items-center text-sm sm:text-base'>\n                <span\n                  className={`inline-flex items-center justify-center w-6 h-6 sm:w-8 sm:h-8 ${theme.icon} mr-2 sm:mr-3 bg-white/20 rounded-full flex-shrink-0`}\n                >\n                  {renderActivityIcon(index)}\n                </span>\n                <span className='flex-1'>{activity}</span>\n              </li>\n            ))}\n          </ul>\n\n          {/* Pricing */}\n          <div className='mb-6 sm:mb-8 space-y-3 sm:space-y-4 flex-shrink-0'>\n            {pricing.adults && (\n              <div\n                className={`flex justify-between items-center ${theme.priceBox} backdrop-blur-sm p-3 sm:p-4 rounded-xl border border-white/10 shadow-inner`}\n              >\n                <p className='text-base sm:text-lg font-semibold text-white'>Adults</p>\n                <p className='text-xl sm:text-2xl font-bold text-white'>{pricing.adults}</p>\n              </div>\n            )}\n\n            {pricing.children && (\n              <div\n                className={`flex justify-between items-center ${theme.priceBox} backdrop-blur-sm p-3 sm:p-4 rounded-xl border border-white/10 shadow-inner`}\n              >\n                <div>\n                  <p className='text-base sm:text-lg font-semibold text-white'>Children</p>\n                  <p className='text-xs sm:text-sm text-white/80'>under 12 years old</p>\n                </div>\n                <p className='text-xl sm:text-2xl font-bold text-white'>{pricing.children}</p>\n              </div>\n            )}\n\n            {pricing.perPerson && (\n              <div\n                className={`flex justify-between items-center ${theme.priceBox} backdrop-blur-sm p-3 sm:p-4 rounded-xl border border-white/10 shadow-inner`}\n              >\n                <div className='flex items-center'>\n                  <User className='w-4 h-4 sm:w-5 sm:h-5 text-white mr-2' />\n                  <p className='text-base sm:text-lg font-semibold text-white'>Per Person</p>\n                </div>\n                <p className='text-xl sm:text-2xl font-bold text-white'>{pricing.perPerson}</p>\n              </div>\n            )}\n\n            {/* Add empty space for cards with less pricing info to maintain height */}\n            {!pricing.children && !pricing.perPerson && <div className='h-16 sm:h-20'></div>}\n            {!pricing.adults && !pricing.children && !pricing.perPerson && <div className='h-32 sm:h-40'></div>}\n          </div>\n\n          {/* Book Now Button */}\n          <div className='mt-auto flex-shrink-0'>\n            <BookingButton\n              id={bookingId}\n              dataSrc={dataSrc}\n              className={`w-full bg-gradient-to-r ${theme.button} text-white font-semibold py-3 sm:py-3.5 px-4 sm:px-6 rounded-xl transition duration-300 ease-in-out transform hover:scale-105 flex items-center justify-center text-base sm:text-lg shadow-lg hover:shadow-xl`}\n              trackingLabel={trackingLabel}\n              packageName={packageName}\n              packagePrice={packagePrice}\n            >\n              {bookNowText}\n              <svg className='w-4 h-4 sm:w-5 sm:h-5 ml-2' fill='none' stroke='currentColor' viewBox='0 0 24 24'>\n                <path strokeLinecap='round' strokeLinejoin='round' strokeWidth={2} d='M13 7l5 5m0 0l-5 5m5-5H6' />\n              </svg>\n            </BookingButton>\n          </div>\n        </div>\n      </div>\n\n      <style jsx nonce={nonce}>{`\n        .mosaic-grid {\n          display: grid;\n          grid-template-columns: repeat(2, 1fr);\n          grid-template-rows: repeat(2, 1fr);\n          gap: 6px;\n        }\n        .mosaic-main {\n          grid-column: 1 / 2;\n          grid-row: 1 / 3;\n        }\n        .mosaic-top {\n          grid-column: 2 / 3;\n          grid-row: 1 / 2;\n        }\n        .mosaic-bottom {\n          grid-column: 2 / 3;\n          grid-row: 2 / 3;\n        }\n      `}</style>\n    </div>\n  )\n}\n", "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/Footer.tsx", "messages": [{"ruleId": "@typescript-eslint/no-unused-vars", "severity": 1, "message": "'Link' is defined but never used. Allowed unused vars must match /^_/u.", "line": 4, "column": 8, "nodeType": null, "messageId": "unusedVar", "endLine": 4, "endColumn": 12}], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "'use client'\n\nimport { MapP<PERSON>, Phone, Mail, Clock } from 'lucide-react'\nimport Link from 'next/link'\n\nimport { useLanguage } from '@/contexts/language-context'\n\nexport default function Footer() {\n  const { t } = useLanguage()\n  const mapsUrl = 'https://www.google.com/maps/dir/?api=1&destination=Pony+Club+Ecotourism'\n  const currentYear = new Date().getFullYear()\n\n  return (\n    <footer className='relative bg-secondary border-t border-border'>\n      {' '}\n      {/* Removed mt-16 pt-12 pb-12 to eliminate spacing */}\n      <div className='container mx-auto px-4 sm:px-6 lg:px-8 max-w-5xl py-12'>\n        {' '}\n        {/* Added padding to the container instead */}\n        <h2 className='text-center text-3xl mb-8 font-bold text-primary'>{t.footer.contactUs}</h2>\n        <div className='grid md:grid-cols-2 gap-8'>\n          <div className='space-y-6'>\n            <div className='flex items-start gap-3'>\n              <MapPin className='h-5 w-5 mt-1 text-primary' />\n              <div>\n                <p className='font-semibold text-foreground'>{t.footer.address.line1}</p>\n                <p className='text-foreground/90'>{t.footer.address.line2}</p>\n                <p className='text-foreground/90'>{t.footer.address.line3}</p>\n              </div>\n            </div>\n\n            <div className='flex items-start gap-3'>\n              <Phone className='h-5 w-5 mt-1 text-primary' />\n              <div>\n                <p className='text-foreground/90'>{t.footer.phone}</p>\n                <p className='text-sm text-muted-foreground mt-1'>{t.footer.phoneLang}</p>\n              </div>\n            </div>\n\n            <div className='flex items-start gap-3'>\n              <Mail className='h-5 w-5 mt-1 text-primary' />\n              <div>\n                <p className='text-foreground/90'>{t.footer.email}</p>\n                <p className='text-sm text-muted-foreground mt-1'>{t.footer.emailNote}</p>\n              </div>\n            </div>\n          </div>\n\n          <div className='space-y-6'>\n            <div className='flex items-start gap-3'>\n              <Clock className='h-5 w-5 mt-1 text-primary' />\n              <div>\n                <p className='font-semibold text-foreground'>{t.footer.openHours.title}</p>\n                <p className='text-foreground/90'>{t.footer.openHours.schedule}</p>\n                <p className='text-sm text-muted-foreground'>{t.footer.openHours.season}</p>\n              </div>\n            </div>\n\n            <div className='pt-4'>\n              <a\n                href={mapsUrl}\n                target='_blank'\n                rel='noopener noreferrer'\n                className='inline-flex items-center justify-center px-6 py-3 font-semibold text-primary-foreground bg-primary rounded-lg hover:bg-primary/90 transition-colors gap-2'\n              >\n                <MapPin className='h-5 w-5' />\n                <span>{t.footer.findUs}</span>\n              </a>\n            </div>\n          </div>\n        </div>\n        <div className='mt-12 text-center text-xs text-muted-foreground tracking-wide'>\n          <p>{t.footer.copyright.replace('{year}', currentYear.toString())}</p>\n        </div>\n      </div>\n    </footer>\n  )\n}\n", "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/HomePageContent.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/PageLayout.tsx", "messages": [{"ruleId": "@typescript-eslint/no-unused-vars", "severity": 1, "message": "'Container' is defined but never used. Allowed unused vars must match /^_/u.", "line": 9, "column": 10, "nodeType": null, "messageId": "unusedVar", "endLine": 9, "endColumn": 19}], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "'use client'\n\nimport { usePathname } from 'next/navigation'\nimport type { ReactNode } from 'react'\nimport { useEffect, useState } from 'react'\n\nimport Footer from './Footer'\nimport SiteHeader from './site-header' // Added SiteHeader import\nimport { Container } from './ui/Container' // Import the Container component\n\ninterface PageLayoutProps {\n  children: ReactNode\n}\n\nexport default function PageLayout({ children }: PageLayoutProps) {\n  const pathname = usePathname()\n  const [isHomePage, setIsHomePage] = useState(true)\n\n  useEffect(() => {\n    // More defensive check for homepage\n    if (pathname) {\n      setIsHomePage(pathname === '/' || pathname === '')\n    }\n  }, [pathname])\n\n  return (\n    <div className='flex flex-col min-h-screen'>\n      <SiteHeader />\n      {/* Apply pt-20 to main to account for fixed SiteHeader */}\n      <main className='grow pt-20 bg-[#f9f7f2]'>\n        {/* Page content */}\n        {children}\n      </main>\n      {!isHomePage && <Footer />}\n    </div>\n  )\n}\n", "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/SummerProgramCard.tsx", "messages": [{"ruleId": "@typescript-eslint/no-unused-vars", "severity": 1, "message": "'Waves' is defined but never used. Allowed unused vars must match /^_/u.", "line": 3, "column": 10, "nodeType": null, "messageId": "unusedVar", "endLine": 3, "endColumn": 15}, {"ruleId": "@typescript-eslint/no-unused-vars", "severity": 1, "message": "'MountainSnow' is defined but never used. Allowed unused vars must match /^_/u.", "line": 3, "column": 17, "nodeType": null, "messageId": "unusedVar", "endLine": 3, "endColumn": 29}, {"ruleId": "@typescript-eslint/no-unused-vars", "severity": 1, "message": "'Sailboat' is defined but never used. Allowed unused vars must match /^_/u.", "line": 3, "column": 31, "nodeType": null, "messageId": "unusedVar", "endLine": 3, "endColumn": 39}, {"ruleId": "@typescript-eslint/no-unused-vars", "severity": 1, "message": "'GridPattern' is defined but never used. Allowed unused vars must match /^_/u.", "line": 8, "column": 10, "nodeType": null, "messageId": "unusedVar", "endLine": 8, "endColumn": 21}], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "'use client'\n\nimport { Waves, <PERSON>Snow, Sailboat } from 'lucide-react'\nimport { Roboto_Slab } from 'next/font/google'\nimport React from 'react'\n\nimport { BorderBeam } from '@/components/ui/border-beam'\nimport { GridPattern } from '@/components/ui/grid-pattern'\nimport { NumberTicker } from '@/components/ui/number-ticker'\nimport { OptimizedImage } from '@/components/ui/OptimizedImage'\nimport { PulsatingButton } from '@/components/ui/pulsating-button'\nimport { useLanguage } from '@/contexts/language-context' // Assuming this context provides 't' for translations\nimport { cn } from '@/lib/utils'\n\nconst robotoSlab = Roboto_Slab({\n  subsets: ['latin', 'greek'],\n  weight: ['400', '700'],\n  variable: '--font-roboto-slab',\n})\n\ninterface ActivityHighlight {\n  icon: React.ElementType\n  text: string\n}\n\ninterface SummerProgramCardProps {\n  title: string\n  price: number\n  priceSuffix?: string // e.g., \"€ Adults\" or \"€ per person\"\n  priceDetails?: string // e.g., \"10 € children under 12 years old\"\n  videoSrc?: string\n  imageSrc: string // Fallback if video isn't available or for poster\n  highlights: ActivityHighlight[]\n  badgeLabel?: string\n  badgeColor?: string // Tailwind color class e.g., \"bg-green-500\"\n  bookingId: string\n  bokunDataSrc: string\n  onBookNowClick: (event: React.MouseEvent<HTMLButtonElement>) => void\n  isPopular?: boolean // To conditionally apply \"Most Popular\" styling or different BorderBeam\n}\n\nexport function SummerProgramCard({\n  title,\n  price,\n  priceSuffix = '€',\n  priceDetails,\n  videoSrc,\n  imageSrc,\n  highlights,\n  badgeLabel,\n  badgeColor = 'bg-green-500',\n  bookingId,\n  bokunDataSrc,\n  onBookNowClick,\n  isPopular = false,\n}: SummerProgramCardProps) {\n  const { t } = useLanguage() // For \"Book Now\" and other potential translations\n\n  return (\n    <div className='group relative w-full md:w-1/2'>\n      <div className='relative rounded-2xl overflow-hidden shadow-xl transform hover:scale-[1.03] hover:translate-y-[-8px] transition-all duration-500 h-[600px] md:h-[650px] border-l-4 border-[#19563F]'>\n        {/* Background: Video or Image */}\n        {videoSrc ? (\n          <video\n            src={videoSrc}\n            autoPlay\n            muted\n            loop\n            playsInline\n            preload='metadata' // Changed from none to metadata for faster first frame\n            className='absolute inset-0 w-full h-full object-cover z-0'\n            poster={imageSrc} // Use imageSrc as poster\n          />\n        ) : (\n          <OptimizedImage\n            src={imageSrc}\n            alt={title}\n            fill\n            sizes='(max-width: 767px) 100vw, 50vw'\n            className='object-cover z-0'\n            imageType='default'\n            priority // Consider making this conditional if many cards\n          />\n        )}\n\n        {/* Subtle Topo Lines Background for the entire card section - to be added in parent page.tsx */}\n        {/* <GridPattern\n          width={40}\n          height={40}\n          x={-1}\n          y={-1}\n          className=\"absolute inset-0 -z-10 h-full w-full stroke-gray-500/10 opacity-50 [mask-image:radial-gradient(ellipse_at_center,white,transparent_80%)]\"\n        /> */}\n\n        {/* Glass Overlay / Content Protection */}\n        <div className='absolute inset-0 bg-gradient-to-t from-black/70 via-black/40 to-transparent z-10'></div>\n\n        {/* Card Content Layer */}\n        <div className='absolute inset-0 flex flex-col h-full z-20 p-6'>\n          {/* Top Section - Badge and Title */}\n          <div className='mb-4'>\n            {badgeLabel && (\n              <div\n                className={cn(\n                  'inline-block rounded-full px-4 py-1.5 text-white text-xs sm:text-sm font-semibold mb-3 shadow-lg border border-white/20 backdrop-blur-sm',\n                  badgeColor\n                )}\n              >\n                {badgeLabel}\n              </div>\n            )}\n            <h3\n              className={`${robotoSlab.variable} font-roboto-slab text-3xl sm:text-4xl font-bold text-white drop-shadow-lg leading-tight`}\n            >\n              {title}\n            </h3>\n          </div>\n\n          {/* Center Section - Activity List with \"Breaking\" Icons */}\n          <div className='grow my-4'>\n            <div className='bg-white/80 backdrop-blur-md rounded-xl p-4 shadow-lg border border-white/30 max-w-sm'>\n              <ul className='space-y-3 text-gray-800'>\n                {highlights.map((highlight, index) => (\n                  <li key={index} className='flex items-center gap-3'>\n                    <div className='relative -top-8 -left-2 transform group-hover:-translate-y-1 group-hover:scale-110 transition-transform duration-300'>\n                      <div className='flex items-center justify-center bg-[#19563F] rounded-full p-2.5 shadow-md w-10 h-10'>\n                        <highlight.icon className='w-5 h-5 text-white' />\n                      </div>\n                    </div>\n                    <span className='font-medium text-sm sm:text-base -ml-4'>{highlight.text}</span>\n                  </li>\n                ))}\n              </ul>\n            </div>\n          </div>\n\n          {/* Bottom Section - Price and Button */}\n          <div className='mt-auto'>\n            <div className='bg-black/50 backdrop-blur-md p-5 rounded-xl border-t border-white/20'>\n              <div className='flex flex-col items-center text-center'>\n                <div className='text-4xl sm:text-5xl font-bold text-white mb-1'>\n                  <NumberTicker value={price} className='text-white' />\n                  <span className='text-3xl sm:text-4xl'>{priceSuffix}</span>\n                </div>\n                {priceDetails && <p className='text-xs sm:text-sm text-white/80 mb-4'>{priceDetails}</p>}\n                <PulsatingButton\n                  className='w-full text-lg font-semibold py-3 rounded-lg bg-[#6b8362] hover:bg-[#3E5A35] text-white transition-all duration-300 shadow-lg hover:shadow-xl'\n                  pulseColor='rgba(255, 255, 255, 0.5)'\n                  id={bookingId}\n                  data-src={bokunDataSrc}\n                  onClick={onBookNowClick}\n                >\n                  {t.booking.bookNow || 'Book Now'}\n                </PulsatingButton>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Optional Border Beam */}\n        {isPopular ? (\n          <BorderBeam duration={8} size={150} colorFrom='#FFD700' colorTo='#FFA500' /> // Gold/Orange for popular\n        ) : (\n          <BorderBeam duration={10} size={100} colorFrom='#4ade80' colorTo='#3b82f6' /> // Green/Blue for standard\n        )}\n      </div>\n    </div>\n  )\n}\n\n// Default export for lazy loading if needed, or named export for direct use.\nexport default SummerProgramCard\n", "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/activities-menu.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/api-key-input.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/client/BokunStyles.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/client/BookingButton.tsx", "messages": [{"ruleId": "no-console", "severity": 1, "message": "Unexpected console statement.", "line": 104, "column": 9, "nodeType": "MemberExpression", "messageId": "unexpected", "endLine": 104, "endColumn": 21, "suggestions": [{"fix": {"range": [3537, 3753], "text": ""}, "messageId": "removeConsole", "data": {"propertyName": "warn"}, "desc": "Remove the console.warn()."}]}, {"ruleId": "@typescript-eslint/no-explicit-any", "severity": 1, "message": "Unexpected any. Specify a different type.", "line": 112, "column": 22, "nodeType": "TSAnyKeyword", "messageId": "unexpectedAny", "endLine": 112, "endColumn": 25, "suggestions": [{"messageId": "suggestUnknown", "fix": {"range": [3899, 3902], "text": "unknown"}, "desc": "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct."}, {"messageId": "suggestNever", "fix": {"range": [3899, 3902], "text": "never"}, "desc": "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of."}]}, {"ruleId": "no-console", "severity": 1, "message": "Unexpected console statement.", "line": 133, "column": 5, "nodeType": "MemberExpression", "messageId": "unexpected", "endLine": 133, "endColumn": 16, "suggestions": [{"fix": {"range": [4422, 4576], "text": ""}, "messageId": "removeConsole", "data": {"propertyName": "log"}, "desc": "Remove the console.log()."}]}], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "'use client'\n\nimport { useRef, useCallback } from 'react'\n\nimport { useGDPR } from '@/contexts/gdpr-context'\n\ninterface BookingButtonProps {\n  id: string\n  dataSrc: string\n  className?: string\n  children: React.ReactNode\n  trackingLabel?: string // For identifying which button was clicked\n  packageName?: string // For enhanced ecommerce tracking\n  packagePrice?: string // For conversion value tracking\n}\n\nexport default function BookingButton({\n  id,\n  dataSrc,\n  className,\n  children,\n  trackingLabel = 'Unknown',\n  packageName = 'Unknown Package',\n  packagePrice = '0',\n}: BookingButtonProps) {\n  const clickedButtonRef = useRef<HTMLButtonElement | null>(null)\n  const bokunReadyAttempts = useRef(0)\n  const { consent } = useGDPR()\n\n  const ensureBokunIsReadyAndOpen = useCallback(() => {\n    if (\n      window.BokunWidgets &&\n      (typeof window.BokunWidgets.init === 'function' || typeof window.BokunWidgets.reinit === 'function')\n    ) {\n      // Check if a modal is already open (very basic check, might need refinement)\n      if (document.querySelector('.bokunModalContainer') || document.querySelector('.bokun-modal-open')) {\n        return\n      }\n\n      if (clickedButtonRef.current) {\n        // It's possible Bokun's scripts have now attached proper listeners.\n        // A direct click might be better than trying to call their internal modal functions.\n        clickedButtonRef.current.click()\n        clickedButtonRef.current = null // Clear after attempting\n      }\n      bokunReadyAttempts.current = 0 // Reset attempts\n    } else if (bokunReadyAttempts.current < 30) {\n      // Try for ~3 seconds\n      bokunReadyAttempts.current++\n      setTimeout(ensureBokunIsReadyAndOpen, 100)\n    } else {\n      bokunReadyAttempts.current = 0 // Reset attempts\n    }\n  }, [])\n\n  // Comprehensive tracking function for GDPR-compliant analytics\n  const trackBookingClick = useCallback(() => {\n    if (typeof window === 'undefined' || !consent) return\n\n    // Extract numeric price for conversion tracking\n    const numericPrice = parseFloat(packagePrice.replace(/[^\\d.]/g, '')) || 0\n\n    // Google Analytics 4 Event Tracking (only if analytics consent given)\n    if (window.gtag && consent.analytics) {\n      // Standard GA4 event\n      window.gtag('event', 'book_now_click', {\n        event_category: 'Booking',\n        event_label: trackingLabel,\n        package_name: packageName,\n        package_price: numericPrice,\n        currency: 'EUR',\n        button_id: id,\n        page_location: window.location.href,\n        page_title: document.title,\n      })\n\n      // Enhanced Ecommerce - Begin Checkout Event\n      window.gtag('event', 'begin_checkout', {\n        currency: 'EUR',\n        value: numericPrice,\n        items: [\n          {\n            item_id: id,\n            item_name: packageName,\n            item_category: 'Adventure Package',\n            price: numericPrice,\n            quantity: 1,\n          },\n        ],\n      })\n\n      // Google Ads Conversion Tracking\n      const googleAdsConversionId = process.env.NEXT_PUBLIC_GOOGLE_ADS_CONVERSION_ID\n      const googleAdsConversionLabel = process.env.NEXT_PUBLIC_GOOGLE_ADS_CONVERSION_LABEL\n\n      if (googleAdsConversionId && googleAdsConversionLabel) {\n        window.gtag('event', 'conversion', {\n          send_to: `${googleAdsConversionId}/${googleAdsConversionLabel}`,\n          value: numericPrice,\n          currency: 'EUR',\n          transaction_id: `booking_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n        })\n      } else {\n        console.warn(\n          '[Booking Tracking] Google Ads conversion tracking not configured. Please set NEXT_PUBLIC_GOOGLE_ADS_CONVERSION_ID and NEXT_PUBLIC_GOOGLE_ADS_CONVERSION_LABEL environment variables.'\n        )\n      }\n    }\n\n    // Vercel Analytics (if available and analytics consent given)\n    if (window.va && consent.analytics) {\n      ;(window.va as any)('event', {\n        name: 'Book Now Click',\n        data: {\n          package: packageName,\n          price: numericPrice,\n          button_id: id,\n          label: trackingLabel,\n        },\n      })\n    }\n\n    // Facebook Pixel (if available and marketing consent given)\n    if (window.fbq && consent.marketing) {\n      window.fbq('track', 'InitiateCheckout', {\n        content_name: packageName,\n        content_category: 'Adventure Package',\n        value: numericPrice,\n        currency: 'EUR',\n      })\n    }\n\n    console.log(\n      `[Booking Tracking] ${trackingLabel} clicked - Package: ${packageName}, Price: €${numericPrice}`,\n      'Consent:',\n      consent\n    )\n  }, [trackingLabel, packageName, packagePrice, id, consent])\n\n  const handleBookNowClick = (event: React.MouseEvent<HTMLButtonElement>) => {\n    // Track the click immediately\n    trackBookingClick()\n\n    clickedButtonRef.current = event.currentTarget\n    ensureBokunIsReadyAndOpen() // Direct call as loader is global\n  }\n\n  return (\n    <button\n      className={`bokunButton ${className}`}\n      id={id}\n      data-src={dataSrc}\n      data-testid='widget-book-button'\n      onClick={handleBookNowClick}\n    >\n      {children}\n    </button>\n  )\n}\n", "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/client/GDPRBanner.tsx", "messages": [{"ruleId": "@typescript-eslint/no-unused-vars", "severity": 1, "message": "'consent' is assigned a value but never used. Allowed unused vars must match /^_/u.", "line": 10, "column": 11, "nodeType": null, "messageId": "unusedVar", "endLine": 10, "endColumn": 18}], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "'use client'\n\nimport { <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>3, Target, Settings } from 'lucide-react'\nimport React, { useState } from 'react'\n\nimport { useGDPR, type CookieConsent } from '@/contexts/gdpr-context'\nimport { useLanguage } from '@/contexts/language-context'\n\nexport default function GDPRBanner() {\n  const { consent, showBanner, showCustomize, acceptAll, rejectAll, saveCustom, openCustomize, closeBanner } = useGDPR()\n  const { t } = useLanguage()\n  const [customConsent, setCustomConsent] = useState<CookieConsent>({\n    necessary: true,\n    analytics: true,\n    marketing: true,\n  })\n\n  if (!showBanner) return null\n\n  const handleCustomConsentChange = (type: keyof CookieConsent, value: boolean) => {\n    setCustomConsent(prev => ({\n      ...prev,\n      [type]: type === 'necessary' ? true : value, // Necessary cookies always enabled\n    }))\n  }\n\n  if (showCustomize) {\n    return (\n      <div className='fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4'>\n        <div className='bg-white rounded-2xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto'>\n          {/* Header */}\n          <div className='flex items-center justify-between p-6 border-b border-gray-200'>\n            <h2 className='text-2xl font-bold text-[#3E5A35] flex items-center gap-2'>\n              <Settings className='w-6 h-6' />\n              {t.gdpr.customize}\n            </h2>\n            <button onClick={closeBanner} className='p-2 hover:bg-gray-100 rounded-full transition-colors'>\n              <X className='w-5 h-5 text-gray-500' />\n            </button>\n          </div>\n\n          {/* Content */}\n          <div className='p-6 space-y-6'>\n            <p className='text-gray-600 leading-relaxed'>{t.gdpr.description}</p>\n\n            {/* Cookie Categories */}\n            <div className='space-y-4'>\n              {/* Necessary Cookies */}\n              <div className='border border-gray-200 rounded-lg p-4'>\n                <div className='flex items-center justify-between mb-2'>\n                  <div className='flex items-center gap-2'>\n                    <Shield className='w-5 h-5 text-green-600' />\n                    <h3 className='font-semibold text-gray-900'>{t.gdpr.necessary}</h3>\n                  </div>\n                  <div className='bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium'>\n                    Always Active\n                  </div>\n                </div>\n                <p className='text-sm text-gray-600'>{t.gdpr.necessaryDescription}</p>\n              </div>\n\n              {/* Analytics Cookies */}\n              <div className='border border-gray-200 rounded-lg p-4'>\n                <div className='flex items-center justify-between mb-2'>\n                  <div className='flex items-center gap-2'>\n                    <BarChart3 className='w-5 h-5 text-blue-600' />\n                    <h3 className='font-semibold text-gray-900'>{t.gdpr.analytics}</h3>\n                  </div>\n                  <label className='relative inline-flex items-center cursor-pointer'>\n                    <input\n                      type='checkbox'\n                      checked={customConsent.analytics}\n                      onChange={e => handleCustomConsentChange('analytics', e.target.checked)}\n                      className='sr-only peer'\n                    />\n                    <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#6b8362]/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#6b8362]\"></div>\n                  </label>\n                </div>\n                <p className='text-sm text-gray-600'>{t.gdpr.analyticsDescription}</p>\n              </div>\n\n              {/* Marketing Cookies */}\n              <div className='border border-gray-200 rounded-lg p-4'>\n                <div className='flex items-center justify-between mb-2'>\n                  <div className='flex items-center gap-2'>\n                    <Target className='w-5 h-5 text-purple-600' />\n                    <h3 className='font-semibold text-gray-900'>{t.gdpr.marketing}</h3>\n                  </div>\n                  <label className='relative inline-flex items-center cursor-pointer'>\n                    <input\n                      type='checkbox'\n                      checked={customConsent.marketing}\n                      onChange={e => handleCustomConsentChange('marketing', e.target.checked)}\n                      className='sr-only peer'\n                    />\n                    <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#6b8362]/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#6b8362]\"></div>\n                  </label>\n                </div>\n                <p className='text-sm text-gray-600'>{t.gdpr.marketingDescription}</p>\n              </div>\n            </div>\n\n            {/* Action Buttons */}\n            <div className='flex flex-col sm:flex-row gap-3 pt-4'>\n              <button\n                onClick={() => saveCustom(customConsent)}\n                className='flex-1 bg-[#6b8362] hover:bg-[#3E5A35] text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-300 shadow-md hover:shadow-lg'\n              >\n                {t.gdpr.save}\n              </button>\n              <button\n                onClick={acceptAll}\n                className='flex-1 bg-gray-100 hover:bg-gray-200 text-gray-800 font-semibold py-3 px-6 rounded-lg transition-colors duration-300'\n              >\n                {t.gdpr.acceptAll}\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className='fixed bottom-0 left-0 right-0 bg-white/95 backdrop-blur-md border-t border-gray-200 shadow-2xl z-50 animate-in slide-in-from-bottom duration-500'>\n      <div className='max-w-7xl mx-auto p-4 sm:p-6'>\n        <div className='flex flex-col lg:flex-row items-start lg:items-center gap-4'>\n          {/* Icon and Content */}\n          <div className='flex items-start gap-3 flex-1'>\n            <div className='flex-shrink-0 w-10 h-10 bg-[#6b8362]/10 rounded-full flex items-center justify-center'>\n              <Shield className='w-5 h-5 text-[#6b8362]' />\n            </div>\n            <div className='flex-1'>\n              <h3 className='font-semibold text-gray-900 mb-1'>{t.gdpr.title}</h3>\n              <p className='text-sm text-gray-600 leading-relaxed'>{t.gdpr.description}</p>\n            </div>\n          </div>\n\n          {/* Action Buttons */}\n          <div className='flex flex-col sm:flex-row gap-3 w-full lg:w-auto lg:flex-shrink-0'>\n            <button\n              onClick={openCustomize}\n              className='px-4 py-2 text-sm font-medium text-[#6b8362] hover:text-[#3E5A35] hover:bg-[#6b8362]/5 rounded-lg transition-colors duration-300 border border-[#6b8362]/20 hover:border-[#6b8362]/40'\n            >\n              {t.gdpr.customize}\n            </button>\n            <button\n              onClick={rejectAll}\n              className='px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors duration-300'\n            >\n              {t.gdpr.rejectAll}\n            </button>\n            <button\n              onClick={acceptAll}\n              className='px-6 py-2 text-sm font-semibold bg-[#6b8362] hover:bg-[#3E5A35] text-white rounded-lg transition-colors duration-300 shadow-md hover:shadow-lg'\n            >\n              {t.gdpr.acceptAll}\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n", "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/client/GDPRGoogleAnalytics.tsx", "messages": [{"ruleId": "@typescript-eslint/no-unused-vars", "severity": 1, "message": "'CookieConsent' is defined but never used. Allowed unused vars must match /^_/u.", "line": 6, "column": 19, "nodeType": null, "messageId": "unusedVar", "endLine": 6, "endColumn": 32}, {"ruleId": "no-console", "severity": 1, "message": "Unexpected console statement.", "line": 24, "column": 7, "nodeType": "MemberExpression", "messageId": "unexpected", "endLine": 24, "endColumn": 18, "suggestions": [{"fix": {"range": [885, 935], "text": ""}, "messageId": "removeConsole", "data": {"propertyName": "log"}, "desc": "Remove the console.log()."}]}, {"ruleId": "no-console", "severity": 1, "message": "Unexpected console statement.", "line": 53, "column": 7, "nodeType": "MemberExpression", "messageId": "unexpected", "endLine": 53, "endColumn": 18, "suggestions": [{"fix": {"range": [1717, 1786], "text": ""}, "messageId": "removeConsole", "data": {"propertyName": "log"}, "desc": "Remove the console.log()."}]}, {"ruleId": "no-console", "severity": 1, "message": "Unexpected console statement.", "line": 65, "column": 11, "nodeType": "MemberExpression", "messageId": "unexpected", "endLine": 65, "endColumn": 22, "suggestions": [{"fix": {"range": [2056, 2096], "text": ""}, "messageId": "removeConsole", "data": {"propertyName": "log"}, "desc": "Remove the console.log()."}]}, {"ruleId": "no-console", "severity": 1, "message": "Unexpected console statement.", "line": 68, "column": 11, "nodeType": "MemberExpression", "messageId": "unexpected", "endLine": 68, "endColumn": 24, "suggestions": [{"fix": {"range": [2143, 2193], "text": ""}, "messageId": "removeConsole", "data": {"propertyName": "error"}, "desc": "Remove the console.error()."}]}], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "'use client'\n\nimport Script from 'next/script'\nimport { useEffect } from 'react'\n\nimport { useGDPR, CookieConsent } from '@/contexts/gdpr-context' // Import CookieConsent\n\ninterface GDPRGoogleAnalyticsProps {\n  gaId: string\n}\n\nexport default function GDPRGoogleAnalytics({ gaId }: GDPRGoogleAnalyticsProps) {\n  const { consent } = useGDPR() // Get the consent object\n\n  useEffect(() => {\n    // This effect handles applying consent changes to gtag\n    if (typeof window !== 'undefined' && window.gtag && consent) {\n      window.gtag('consent', 'update', {\n        analytics_storage: consent.analytics ? 'granted' : 'denied',\n        ad_storage: consent.marketing ? 'granted' : 'denied', // Assuming marketing consent maps to ad_storage\n        ad_user_data: consent.marketing ? 'granted' : 'denied',\n        ad_personalization: consent.marketing ? 'granted' : 'denied',\n      })\n      console.log('[GDPR GA] Consent updated:', consent)\n    }\n  }, [consent]) // Re-run when consent object changes\n\n  // Initialize gtag in a CSP-compliant way\n  useEffect(() => {\n    if (typeof window !== 'undefined') {\n      // Initialize dataLayer and gtag function\n      window.dataLayer = window.dataLayer || []\n      function gtag(...args: unknown[]) {\n        window.dataLayer?.push(args)\n      }\n      window.gtag = gtag\n\n      // Set default consent to denied\n      gtag('consent', 'default', {\n        analytics_storage: 'denied',\n        ad_storage: 'denied',\n        ad_user_data: 'denied',\n        ad_personalization: 'denied',\n        wait_for_update: 500,\n      })\n\n      gtag('js', new Date())\n      gtag('config', gaId, {\n        page_title: document.title,\n        page_location: window.location.href,\n      })\n\n      console.log('[GDPR GA] gtag initialized with default denied consent')\n    }\n  }, [gaId])\n\n  return (\n    <>\n      {/* External gtag script - CSP compliant */}\n      <Script\n        id='ga-gtag-script'\n        src={`https://www.googletagmanager.com/gtag/js?id=${gaId}`}\n        strategy='afterInteractive'\n        onLoad={() => {\n          console.log('[GDPR GA] gtag.js loaded.')\n        }}\n        onError={() => {\n          console.error('[GDPR GA] Failed to load gtag.js.')\n        }}\n      />\n    </>\n  )\n}\n", "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/client/GoogleReviews.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/client/PriceListButton.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/contact-details-enhanced.tsx", "messages": [{"ruleId": "@typescript-eslint/no-unused-vars", "severity": 1, "message": "'Twitter' is defined but never used. Allowed unused vars must match /^_/u.", "line": 3, "column": 52, "nodeType": null, "messageId": "unusedVar", "endLine": 3, "endColumn": 59}, {"ruleId": "@typescript-eslint/no-unused-vars", "severity": 1, "message": "'Youtube' is defined but never used. Allowed unused vars must match /^_/u.", "line": 3, "column": 61, "nodeType": null, "messageId": "unusedVar", "endLine": 3, "endColumn": 68}], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "'use client'\n\nimport { Mail, MapPin, Phone, Facebook, Instagram, Twitter, Youtube, Clock } from 'lucide-react'\nimport type React from 'react'\nimport { useState } from 'react'\n\nimport { useLanguage } from '@/contexts/language-context'\n\ninterface SocialIconProps {\n  href: string\n  icon: React.ReactNode\n  label: string\n}\n\nfunction SocialIconWithTooltip({ href, icon, label }: SocialIconProps) {\n  const [showTooltip, setShowTooltip] = useState(false)\n\n  return (\n    <div className='relative'>\n      <a\n        href={href}\n        target='_blank'\n        rel='noopener noreferrer'\n        className='text-[#6b8362] hover:text-[#c27a5f] transition-colors duration-200'\n        onMouseEnter={() => setShowTooltip(true)}\n        onMouseLeave={() => setShowTooltip(false)}\n      >\n        {icon}\n      </a>\n      {showTooltip && (\n        <div className='absolute -bottom-9 left-1/2 transform -translate-x-1/2 px-2 py-1 bg-gray-800 text-white text-xs rounded whitespace-nowrap'>\n          {label}\n        </div>\n      )}\n    </div>\n  )\n}\n\nexport default function ContactDetailsEnhanced() {\n  const { t } = useLanguage()\n\n  return (\n    <div className='relative bg-white/80 backdrop-blur-xs p-6 md:p-8 rounded-lg shadow-xl border border-amber-100/70 transform rotate-1 hover:shadow-2xl transition-shadow duration-300'>\n      <h2 className='text-center text-3xl mb-6 font-bold text-[#6b8362] relative inline-block w-full'>\n        {t.contact.title}\n        <div className='absolute -bottom-1 left-0 w-full h-[2px] bg-linear-to-r from-transparent via-[#6b8362]/70 to-transparent'></div>\n      </h2>\n\n      <div className='space-y-4'>\n        <div className='flex items-start gap-3'>\n          <MapPin className='text-[#c27a5f] h-6 w-6 mt-1 shrink-0' />\n          <div>\n            <h3 className='font-bold text-[#6b8362] uppercase'>{t.contact.location}</h3>\n            <p className='font-medium'>{t.contact.businessName}</p>\n            <p>{t.contact.address1}</p>\n            <p>{t.contact.address2}</p>\n          </div>\n        </div>\n\n        <div className='flex items-start gap-3 mt-4 pt-4 border-t border-[#6b8362]/30'>\n          <Phone className='text-[#c27a5f] h-6 w-6 mt-1 shrink-0' />\n          <div>\n            <h3 className='font-bold text-[#6b8362] uppercase'>{t.contact.phone}</h3>\n            <p>{t.contact.phone1}</p>\n            <p className='text-sm italic'>{t.contact.phone2}</p>\n          </div>\n        </div>\n\n        <div className='flex items-start gap-3'>\n          <Mail className='text-[#c27a5f] h-6 w-6 mt-1 shrink-0' />\n          <div>\n            <h3 className='font-bold text-[#6b8362] uppercase'>{t.contact.emailLabel}</h3>\n            <p>{t.contact.email}</p>\n            <p className='text-sm italic mt-1'>{t.contact.emailNote}</p>\n          </div>\n        </div>\n\n        <div className='flex items-start gap-3 mt-4 pt-4 border-t border-[#6b8362]/30'>\n          <Clock className='text-[#c27a5f] h-6 w-6 mt-1 shrink-0' />\n          <div>\n            <h3 className='font-bold text-[#6b8362] uppercase mb-2'>{t.contact.openingHours}</h3>\n            <p className='text-sm whitespace-pre-line'>{t.contact.openingHoursDetails}</p>\n            <p className='text-sm italic mt-1'>{t.contact.openingHoursSeason}</p>\n          </div>\n        </div>\n\n        {/* Social Media Section */}\n        <div className='mt-6 pt-4 border-t border-[#6b8362]/30'>\n          <h3 className='text-center font-bold mb-3 text-[#6b8362]'>{t.contact.followUs}</h3>\n          <div className='flex justify-center space-x-6'>\n            <SocialIconWithTooltip\n              href='https://www.facebook.com/ponyclubgreece'\n              icon={<Facebook className='h-5 w-5 hover:scale-125 transition-transform duration-200' />}\n              label='Facebook'\n            />\n            <SocialIconWithTooltip\n              href='https://www.instagram.com/ponyclub_greece/'\n              icon={<Instagram className='h-5 w-5 hover:scale-125 transition-transform duration-200' />}\n              label='Instagram'\n            />\n          </div>\n        </div>\n      </div>\n      <div className='absolute -inset-[1px] -z-10 rounded-lg bg-linear-to-tr from-amber-200/20 via-white/50 to-[#6b8362]/20 blur-xs'></div>\n    </div>\n  )\n}\n", "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/desktop-menu.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/google-map.tsx", "messages": [{"ruleId": "@typescript-eslint/no-explicit-any", "severity": 1, "message": "Unexpected any. Specify a different type.", "line": 68, "column": 19, "nodeType": "TSAnyKeyword", "messageId": "unexpectedAny", "endLine": 68, "endColumn": 22, "suggestions": [{"messageId": "suggestUnknown", "fix": {"range": [2307, 2310], "text": "unknown"}, "desc": "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct."}, {"messageId": "suggestNever", "fix": {"range": [2307, 2310], "text": "never"}, "desc": "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of."}]}, {"ruleId": "no-console", "severity": 1, "message": "Unexpected console statement.", "line": 69, "column": 9, "nodeType": "MemberExpression", "messageId": "unexpected", "endLine": 69, "endColumn": 20, "suggestions": [{"fix": {"range": [2345, 2394], "text": ""}, "messageId": "removeConsole", "data": {"propertyName": "log"}, "desc": "Remove the console.log()."}]}, {"ruleId": "@typescript-eslint/no-explicit-any", "severity": 1, "message": "Unexpected any. Specify a different type.", "line": 76, "column": 27, "nodeType": "TSAnyKeyword", "messageId": "unexpectedAny", "endLine": 76, "endColumn": 30, "suggestions": [{"messageId": "suggestUnknown", "fix": {"range": [2530, 2533], "text": "unknown"}, "desc": "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct."}, {"messageId": "suggestNever", "fix": {"range": [2530, 2533], "text": "never"}, "desc": "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of."}]}, {"ruleId": "@typescript-eslint/no-explicit-any", "severity": 1, "message": "Unexpected any. Specify a different type.", "line": 86, "column": 18, "nodeType": "TSAnyKeyword", "messageId": "unexpectedAny", "endLine": 86, "endColumn": 21, "suggestions": [{"messageId": "suggestUnknown", "fix": {"range": [2811, 2814], "text": "unknown"}, "desc": "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct."}, {"messageId": "suggestNever", "fix": {"range": [2811, 2814], "text": "never"}, "desc": "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of."}]}, {"ruleId": "@typescript-eslint/no-explicit-any", "severity": 1, "message": "Unexpected any. Specify a different type.", "line": 87, "column": 18, "nodeType": "TSAnyKeyword", "messageId": "unexpectedAny", "endLine": 87, "endColumn": 21, "suggestions": [{"messageId": "suggestUnknown", "fix": {"range": [2843, 2846], "text": "unknown"}, "desc": "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct."}, {"messageId": "suggestNever", "fix": {"range": [2843, 2846], "text": "never"}, "desc": "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of."}]}, {"ruleId": "@typescript-eslint/no-explicit-any", "severity": 1, "message": "Unexpected any. Specify a different type.", "line": 88, "column": 18, "nodeType": "TSAnyKeyword", "messageId": "unexpectedAny", "endLine": 88, "endColumn": 21, "suggestions": [{"messageId": "suggestUnknown", "fix": {"range": [2880, 2883], "text": "unknown"}, "desc": "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct."}, {"messageId": "suggestNever", "fix": {"range": [2880, 2883], "text": "never"}, "desc": "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of."}]}, {"ruleId": "@typescript-eslint/no-explicit-any", "severity": 1, "message": "Unexpected any. Specify a different type.", "line": 89, "column": 25, "nodeType": "TSAnyKeyword", "messageId": "unexpectedAny", "endLine": 89, "endColumn": 28, "suggestions": [{"messageId": "suggestUnknown", "fix": {"range": [2928, 2931], "text": "unknown"}, "desc": "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct."}, {"messageId": "suggestNever", "fix": {"range": [2928, 2931], "text": "never"}, "desc": "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of."}]}, {"ruleId": "@typescript-eslint/no-explicit-any", "severity": 1, "message": "Unexpected any. Specify a different type.", "line": 95, "column": 44, "nodeType": "TSAnyKeyword", "messageId": "unexpectedAny", "endLine": 95, "endColumn": 47, "suggestions": [{"messageId": "suggestUnknown", "fix": {"range": [3078, 3081], "text": "unknown"}, "desc": "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct."}, {"messageId": "suggestNever", "fix": {"range": [3078, 3081], "text": "never"}, "desc": "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of."}]}, {"ruleId": "@typescript-eslint/no-explicit-any", "severity": 1, "message": "Unexpected any. Specify a different type.", "line": 102, "column": 31, "nodeType": "TSAnyKeyword", "messageId": "unexpectedAny", "endLine": 102, "endColumn": 34, "suggestions": [{"messageId": "suggestUnknown", "fix": {"range": [3379, 3382], "text": "unknown"}, "desc": "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct."}, {"messageId": "suggestNever", "fix": {"range": [3379, 3382], "text": "never"}, "desc": "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of."}]}, {"ruleId": "@typescript-eslint/no-explicit-any", "severity": 1, "message": "Unexpected any. Specify a different type.", "line": 103, "column": 34, "nodeType": "TSAnyKeyword", "messageId": "unexpectedAny", "endLine": 103, "endColumn": 37, "suggestions": [{"messageId": "suggestUnknown", "fix": {"range": [3465, 3468], "text": "unknown"}, "desc": "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct."}, {"messageId": "suggestNever", "fix": {"range": [3465, 3468], "text": "never"}, "desc": "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of."}]}, {"ruleId": "@typescript-eslint/no-explicit-any", "severity": 1, "message": "Unexpected any. Specify a different type.", "line": 107, "column": 34, "nodeType": "TSAnyKeyword", "messageId": "unexpectedAny", "endLine": 107, "endColumn": 37, "suggestions": [{"messageId": "suggestUnknown", "fix": {"range": [3629, 3632], "text": "unknown"}, "desc": "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct."}, {"messageId": "suggestNever", "fix": {"range": [3629, 3632], "text": "never"}, "desc": "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of."}]}, {"ruleId": "@typescript-eslint/no-explicit-any", "severity": 1, "message": "Unexpected any. Specify a different type.", "line": 111, "column": 34, "nodeType": "TSAnyKeyword", "messageId": "unexpectedAny", "endLine": 111, "endColumn": 37, "suggestions": [{"messageId": "suggestUnknown", "fix": {"range": [3795, 3798], "text": "unknown"}, "desc": "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct."}, {"messageId": "suggestNever", "fix": {"range": [3795, 3798], "text": "never"}, "desc": "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of."}]}, {"ruleId": "@typescript-eslint/no-explicit-any", "severity": 1, "message": "Unexpected any. Specify a different type.", "line": 117, "column": 39, "nodeType": "TSAnyKeyword", "messageId": "unexpectedAny", "endLine": 117, "endColumn": 42, "suggestions": [{"messageId": "suggestUnknown", "fix": {"range": [4006, 4009], "text": "unknown"}, "desc": "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct."}, {"messageId": "suggestNever", "fix": {"range": [4006, 4009], "text": "never"}, "desc": "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of."}]}, {"ruleId": "@typescript-eslint/no-explicit-any", "severity": 1, "message": "Unexpected any. Specify a different type.", "line": 121, "column": 33, "nodeType": "TSAnyKeyword", "messageId": "unexpectedAny", "endLine": 121, "endColumn": 36, "suggestions": [{"messageId": "suggestUnknown", "fix": {"range": [4166, 4169], "text": "unknown"}, "desc": "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct."}, {"messageId": "suggestNever", "fix": {"range": [4166, 4169], "text": "never"}, "desc": "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of."}]}, {"ruleId": "@typescript-eslint/no-explicit-any", "severity": 1, "message": "Unexpected any. Specify a different type.", "line": 176, "column": 43, "nodeType": "TSAnyKeyword", "messageId": "unexpectedAny", "endLine": 176, "endColumn": 46, "suggestions": [{"messageId": "suggestUnknown", "fix": {"range": [6384, 6387], "text": "unknown"}, "desc": "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct."}, {"messageId": "suggestNever", "fix": {"range": [6384, 6387], "text": "never"}, "desc": "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of."}]}, {"ruleId": "no-console", "severity": 1, "message": "Unexpected console statement.", "line": 187, "column": 9, "nodeType": "MemberExpression", "messageId": "unexpected", "endLine": 187, "endColumn": 20, "suggestions": [{"fix": {"range": [6682, 6733], "text": ""}, "messageId": "removeConsole", "data": {"propertyName": "log"}, "desc": "Remove the console.log()."}]}, {"ruleId": "no-console", "severity": 1, "message": "Unexpected console statement.", "line": 189, "column": 9, "nodeType": "MemberExpression", "messageId": "unexpected", "endLine": 189, "endColumn": 22, "suggestions": [{"fix": {"range": [6766, 6821], "text": ""}, "messageId": "removeConsole", "data": {"propertyName": "error"}, "desc": "Remove the console.error()."}]}, {"ruleId": "no-console", "severity": 1, "message": "Unexpected console statement.", "line": 198, "column": 7, "nodeType": "MemberExpression", "messageId": "unexpected", "endLine": 198, "endColumn": 18, "suggestions": [{"fix": {"range": [7056, 7120], "text": ""}, "messageId": "removeConsole", "data": {"propertyName": "log"}, "desc": "Remove the console.log()."}]}, {"ruleId": "no-console", "severity": 1, "message": "Unexpected console statement.", "line": 209, "column": 7, "nodeType": "MemberExpression", "messageId": "unexpected", "endLine": 209, "endColumn": 20, "suggestions": [{"fix": {"range": [7382, 7514], "text": ""}, "messageId": "removeConsole", "data": {"propertyName": "error"}, "desc": "Remove the console.error()."}]}, {"ruleId": "no-console", "severity": 1, "message": "Unexpected console statement.", "line": 218, "column": 7, "nodeType": "MemberExpression", "messageId": "unexpected", "endLine": 218, "endColumn": 18, "suggestions": [{"fix": {"range": [7650, 7729], "text": ""}, "messageId": "removeConsole", "data": {"propertyName": "log"}, "desc": "Remove the console.log()."}]}], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 20, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "'use client'\n\nimport { MapPin, Minus, Navigation, Plus } from 'lucide-react'\nimport { useCallback, useEffect, useRef, useState } from 'react'\n\nimport { ScriptLoader } from '@/components/ui/script-loader'\nimport { useGDPR } from '@/contexts/gdpr-context'\n\n// Business coordinates for Pony Club Acheron\nconst BUSINESS_COORDINATES = { lat: 39.3257662, lng: 20.6069899 }\nconst BUSINESS_NAME = 'Pony Club Acheron'\nconst DEFAULT_ZOOM = 16 // 2 levels closer than previous 14\n\nexport default function GoogleMap() {\n  const apiKey = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY\n  const mapRef = useRef<HTMLDivElement>(null)\n  const [isScriptLoaded, setIsScriptLoaded] = useState(false)\n  const [map, setMap] = useState<google.maps.Map | null>(null)\n  const [isLoading, setIsLoading] = useState(true)\n  const [error, setError] = useState<string | null>(null)\n  const { consent } = useGDPR()\n\n  // Custom zoom control handlers\n  const handleZoomIn = useCallback(() => {\n    if (map) {\n      const currentZoom = map.getZoom() || DEFAULT_ZOOM\n      map.setZoom(currentZoom + 1)\n    }\n  }, [map])\n\n  const handleZoomOut = useCallback(() => {\n    if (map) {\n      const currentZoom = map.getZoom() || DEFAULT_ZOOM\n      map.setZoom(Math.max(currentZoom - 1, 1)) // Minimum zoom level 1\n    }\n  }, [map])\n\n  // Get directions to business\n  const handleGetDirections = useCallback(() => {\n    const url = `https://www.google.com/maps/dir/?api=1&destination=${BUSINESS_COORDINATES.lat},${BUSINESS_COORDINATES.lng}&destination_place_id=ChIJYZ6W_v9zXBMRSyYk4s7OGBg`\n    window.open(url, '_blank', 'noopener,noreferrer')\n  }, [])\n\n  // Keyboard navigation support\n  const handleKeyDown = useCallback(\n    (event: React.KeyboardEvent, action: 'zoomIn' | 'zoomOut' | 'directions') => {\n      if (event.key === 'Enter' || event.key === ' ') {\n        event.preventDefault()\n        switch (action) {\n          case 'zoomIn':\n            handleZoomIn()\n            break\n          case 'zoomOut':\n            handleZoomOut()\n            break\n          case 'directions':\n            handleGetDirections()\n            break\n        }\n      }\n    },\n    [handleZoomIn, handleZoomOut, handleGetDirections]\n  )\n\n  // Set up global callback for Google Maps\n  useEffect(() => {\n    if (typeof window !== 'undefined') {\n      ;(window as any).initGoogleMaps = () => {\n        console.log('Google Maps API ready via callback')\n        setIsScriptLoaded(true)\n      }\n    }\n\n    return () => {\n      if (typeof window !== 'undefined') {\n        delete (window as any).initGoogleMaps\n      }\n    }\n  }, [])\n\n  const initializeMap = useCallback(() => {\n    if (!mapRef.current || !apiKey || !isScriptLoaded || !consent?.analytics) return\n\n    // Check if Google Maps API is fully loaded and Map constructor is available\n    if (\n      (window as any).google &&\n      (window as any).google.maps &&\n      (window as any).google.maps.Map &&\n      typeof (window as any).google.maps.Map === 'function'\n    ) {\n      try {\n        setIsLoading(true)\n        setError(null)\n\n        const mapInstance = new (window as any).google.maps.Map(mapRef.current, {\n          center: BUSINESS_COORDINATES,\n          zoom: DEFAULT_ZOOM,\n          mapTypeId: 'roadmap',\n          zoomControl: false, // Disable default zoom controls\n          mapTypeControl: true,\n          mapTypeControlOptions: {\n            style: (window as any).google.maps.MapTypeControlStyle.HORIZONTAL_BAR,\n            position: (window as any).google.maps.ControlPosition.TOP_CENTER,\n          },\n          streetViewControl: true,\n          streetViewControlOptions: {\n            position: (window as any).google.maps.ControlPosition.RIGHT_BOTTOM,\n          },\n          fullscreenControl: true,\n          fullscreenControlOptions: {\n            position: (window as any).google.maps.ControlPosition.RIGHT_TOP,\n          },\n          gestureHandling: 'cooperative', // Better mobile experience\n        })\n\n        // Create enhanced marker\n        const marker = new (window as any).google.maps.Marker({\n          position: BUSINESS_COORDINATES,\n          map: mapInstance,\n          title: BUSINESS_NAME,\n          animation: (window as any).google.maps.Animation.DROP,\n        })\n\n        // Create info window with business details (CSP-compliant - no inline scripts)\n        const infoWindowContent = document.createElement('div')\n        infoWindowContent.style.cssText = 'padding: 10px; font-family: system-ui, -apple-system, sans-serif;'\n\n        const title = document.createElement('h3')\n        title.style.cssText = 'margin: 0 0 8px 0; color: #6b8362; font-size: 16px; font-weight: bold;'\n        title.textContent = BUSINESS_NAME\n\n        const subtitle = document.createElement('p')\n        subtitle.style.cssText = 'margin: 0 0 8px 0; color: #666; font-size: 14px;'\n        subtitle.textContent = 'Adventure Tourism & Ecotourism'\n\n        const activities = document.createElement('p')\n        activities.style.cssText = 'margin: 0 0 10px 0; color: #666; font-size: 13px;'\n        activities.textContent = 'Rafting • Horse Riding • Trekking'\n\n        const directionsButton = document.createElement('button')\n        directionsButton.style.cssText = `\n          background: #6b8362;\n          color: white;\n          border: none;\n          padding: 6px 12px;\n          border-radius: 4px;\n          cursor: pointer;\n          font-size: 12px;\n          font-weight: 500;\n          transition: background-color 0.2s ease;\n        `\n        directionsButton.textContent = 'Get Directions'\n\n        // Add proper event listeners (CSP-compliant)\n        directionsButton.addEventListener('click', () => {\n          window.open(\n            `https://www.google.com/maps/dir/?api=1&destination=${BUSINESS_COORDINATES.lat},${BUSINESS_COORDINATES.lng}`,\n            '_blank'\n          )\n        })\n\n        directionsButton.addEventListener('mouseenter', () => {\n          directionsButton.style.background = '#5a6f53'\n        })\n\n        directionsButton.addEventListener('mouseleave', () => {\n          directionsButton.style.background = '#6b8362'\n        })\n\n        // Assemble the info window content\n        infoWindowContent.appendChild(title)\n        infoWindowContent.appendChild(subtitle)\n        infoWindowContent.appendChild(activities)\n        infoWindowContent.appendChild(directionsButton)\n\n        const infoWindow = new (window as any).google.maps.InfoWindow({\n          content: infoWindowContent,\n        })\n\n        // Open info window when marker is clicked\n        marker.addListener('click', () => {\n          infoWindow.open(mapInstance, marker)\n        })\n\n        setMap(mapInstance)\n        setIsLoading(false)\n        console.log('Google Maps initialized successfully')\n      } catch (error) {\n        console.error('Error initializing Google Maps:', error)\n        setError('Failed to load map. Please try refreshing the page.')\n        setIsLoading(false)\n        // Retry after a short delay\n        setTimeout(() => {\n          initializeMap()\n        }, 2000)\n      }\n    } else {\n      console.log('Google Maps API not fully loaded yet, retrying...')\n      // Retry after a short delay if the API isn't ready\n      setTimeout(() => {\n        initializeMap()\n      }, 100)\n    }\n  }, [apiKey, isScriptLoaded, consent])\n\n  // Initialize map when dependencies are ready\n  useEffect(() => {\n    if (!apiKey) {\n      console.error(\n        'Google Maps API Key is missing. Please set NEXT_PUBLIC_GOOGLE_MAPS_API_KEY in your .env.local file.'\n      )\n      setError('API Key configuration is missing.')\n      setIsLoading(false)\n      return\n    }\n\n    if (!consent?.analytics) {\n      console.log('Google Maps not loaded - analytics consent required', { consent })\n      setIsLoading(false)\n      if (consent === null) {\n        setError('Please accept analytics cookies in the banner below to view the interactive map.')\n      } else {\n        setError(\n          'Analytics cookies are required to display the interactive map. You can change your preferences in the cookie settings.'\n        )\n      }\n      return\n    }\n\n    initializeMap()\n  }, [apiKey, initializeMap, consent])\n\n  // Global keyboard shortcuts for map\n  useEffect(() => {\n    const handleGlobalKeyDown = (event: KeyboardEvent) => {\n      // Only handle shortcuts when map container is focused or when no input is focused\n      const activeElement = document.activeElement\n      const isInputFocused = activeElement?.tagName === 'INPUT' || activeElement?.tagName === 'TEXTAREA'\n\n      if (!isInputFocused && mapRef.current?.contains(activeElement as Node)) {\n        switch (event.key) {\n          case '+':\n          case '=':\n            event.preventDefault()\n            handleZoomIn()\n            break\n          case '-':\n          case '_':\n            event.preventDefault()\n            handleZoomOut()\n            break\n          case 'd':\n          case 'D':\n            if (event.ctrlKey || event.metaKey) {\n              event.preventDefault()\n              handleGetDirections()\n            }\n            break\n        }\n      }\n    }\n\n    document.addEventListener('keydown', handleGlobalKeyDown)\n    return () => document.removeEventListener('keydown', handleGlobalKeyDown)\n  }, [handleZoomIn, handleZoomOut, handleGetDirections])\n\n  // Loading skeleton component\n  const LoadingSkeleton = () => (\n    <div className='h-[400px] w-full rounded-lg shadow-xl bg-gray-200 animate-pulse border border-amber-100/70'>\n      <div className='h-full w-full bg-gradient-to-br from-gray-200 to-gray-300 rounded-lg flex items-center justify-center'>\n        <div className='text-center'>\n          <div className='w-8 h-8 border-4 border-amber-500 border-t-transparent rounded-full animate-spin mx-auto mb-2'></div>\n          <p className='text-gray-600 font-medium'>Loading map...</p>\n        </div>\n      </div>\n    </div>\n  )\n\n  if (!apiKey) {\n    return (\n      <div className='h-[400px] w-full rounded-lg shadow-xl bg-gray-200 flex items-center justify-center text-center p-4 border border-amber-100/70'>\n        <div>\n          <MapPin className='w-12 h-12 text-red-500 mx-auto mb-3' />\n          <p className='text-red-600 font-semibold'>\n            Map cannot be displayed. <br /> API Key configuration is missing.\n          </p>\n        </div>\n      </div>\n    )\n  }\n\n  if (!consent?.analytics) {\n    return (\n      <div className='h-[400px] w-full rounded-lg shadow-xl bg-gray-200 flex items-center justify-center text-center p-4 border border-amber-100/70'>\n        <div>\n          <MapPin className='w-12 h-12 text-gray-500 mx-auto mb-3' />\n          <p className='text-gray-600 font-semibold'>\n            Map requires analytics consent to load. <br /> Please accept analytics cookies to view the map.\n          </p>\n        </div>\n      </div>\n    )\n  }\n\n  if (error) {\n    return (\n      <div className='h-[400px] w-full rounded-lg shadow-xl bg-gray-200 flex items-center justify-center text-center p-4 border border-amber-100/70'>\n        <div>\n          <MapPin className='w-12 h-12 text-red-500 mx-auto mb-3' />\n          <p className='text-red-600 font-semibold mb-3'>{error}</p>\n          <button\n            onClick={() => {\n              setError(null)\n              setIsLoading(true)\n              initializeMap()\n            }}\n            className='px-4 py-2 bg-amber-500 text-white rounded-lg hover:bg-amber-600 transition-colors font-medium'\n          >\n            Try Again\n          </button>\n        </div>\n      </div>\n    )\n  }\n\n  if (isLoading) {\n    return <LoadingSkeleton />\n  }\n\n  return (\n    <div\n      className='relative h-[400px] w-full rounded-lg shadow-xl overflow-hidden border border-amber-100/70 hover:shadow-2xl transition-shadow duration-300'\n      role='application'\n      aria-label='Interactive map showing Pony Club Acheron location'\n    >\n      <ScriptLoader\n        src={`https://maps.googleapis.com/maps/api/js?key=${apiKey}&libraries=places&callback=initGoogleMaps`}\n        strategy='afterInteractive'\n      />\n\n      {/* Map container */}\n      <div\n        ref={mapRef}\n        className='h-full w-full'\n        style={{ border: 0 }}\n        tabIndex={0}\n        role='img'\n        aria-label='Map showing Pony Club Acheron location at coordinates 39.3257662, 20.6069899'\n      />\n\n      {/* Custom zoom controls */}\n      <div className='absolute top-4 right-4 flex flex-col gap-2 z-10' role='group' aria-label='Map zoom controls'>\n        <button\n          onClick={handleZoomIn}\n          onKeyDown={e => handleKeyDown(e, 'zoomIn')}\n          className='w-10 h-10 bg-white hover:bg-gray-50 border border-gray-300 rounded-lg shadow-md flex items-center justify-center transition-all duration-200 hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-amber-500 focus:ring-offset-2'\n          aria-label='Zoom in on map'\n          title='Zoom in (+ key)'\n          tabIndex={0}\n        >\n          <Plus className='w-5 h-5 text-gray-700' />\n        </button>\n        <button\n          onClick={handleZoomOut}\n          onKeyDown={e => handleKeyDown(e, 'zoomOut')}\n          className='w-10 h-10 bg-white hover:bg-gray-50 border border-gray-300 rounded-lg shadow-md flex items-center justify-center transition-all duration-200 hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-amber-500 focus:ring-offset-2'\n          aria-label='Zoom out on map'\n          title='Zoom out (- key)'\n          tabIndex={0}\n        >\n          <Minus className='w-5 h-5 text-gray-700' />\n        </button>\n      </div>\n\n      {/* Get directions button */}\n      <div className='absolute bottom-4 right-4 z-10'>\n        <button\n          onClick={handleGetDirections}\n          onKeyDown={e => handleKeyDown(e, 'directions')}\n          className='px-4 py-2 bg-[#6b8362] hover:bg-[#5a6f53] text-white rounded-lg shadow-md flex items-center gap-2 transition-all duration-200 hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-amber-500 focus:ring-offset-2 font-medium text-sm'\n          aria-label='Get directions to Pony Club Acheron'\n          title='Get directions to our location'\n          tabIndex={0}\n        >\n          <Navigation className='w-4 h-4' />\n          <span className='hidden sm:inline'>Directions</span>\n        </button>\n      </div>\n    </div>\n  )\n}\n", "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/hamburger-menu.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/hero/HeroSection.tsx", "messages": [{"ruleId": "@typescript-eslint/no-unused-vars", "severity": 1, "message": "'useState' is defined but never used. Allowed unused vars must match /^_/u.", "line": 4, "column": 29, "nodeType": null, "messageId": "unusedVar", "endLine": 4, "endColumn": 37}], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "'use client'\n\nimport { motion, useScroll, useTransform } from 'framer-motion'\nimport { useEffect, useRef, useState } from 'react'\n\nfunction RippleCanvas() {\n  const canvasRef = useRef<HTMLCanvasElement>(null)\n\n  useEffect(() => {\n    const canvas = canvasRef.current\n    if (!canvas) return\n    const ctx = canvas.getContext('2d')\n    if (!ctx) return\n\n    let animationFrameId: number\n    let width = (canvas.width = window.innerWidth)\n    let height = (canvas.height = window.innerHeight)\n\n    // Simple ripple effect parameters\n    const ripples: { x: number; y: number; radius: number; alpha: number }[] = []\n\n    function addRipple() {\n      ripples.push({\n        x: Math.random() * width,\n        y: Math.random() * height,\n        radius: 0,\n        alpha: 0.5,\n      })\n    }\n\n    function draw() {\n      if (!ctx) return\n      ctx.clearRect(0, 0, width, height)\n      ripples.forEach((ripple, i) => {\n        ripple.radius += 0.5\n        ripple.alpha -= 0.005\n        if (ripple.alpha <= 0) {\n          ripples.splice(i, 1)\n        } else {\n          ctx.beginPath()\n          ctx.strokeStyle = `rgba(255, 255, 255, ${ripple.alpha})`\n          ctx.lineWidth = 2\n          ctx.arc(ripple.x, ripple.y, ripple.radius, 0, Math.PI * 2)\n          ctx.stroke()\n        }\n      })\n    }\n\n    function animate() {\n      draw()\n      animationFrameId = requestAnimationFrame(animate)\n    }\n\n    // Add ripples periodically\n    const intervalId = setInterval(addRipple, 800)\n\n    animate()\n\n    // Resize handler\n    function handleResize() {\n      if (!canvas) return // Add null check for canvas\n      width = canvas.width = window.innerWidth\n      height = canvas.height = window.innerHeight\n    }\n    window.addEventListener('resize', handleResize)\n\n    return () => {\n      cancelAnimationFrame(animationFrameId)\n      clearInterval(intervalId)\n      window.removeEventListener('resize', handleResize)\n    }\n  }, [])\n\n  return (\n    <canvas\n      ref={canvasRef}\n      className='absolute inset-0 pointer-events-none mix-blend-soft-light'\n      style={{ zIndex: 25 }}\n    />\n  )\n}\n\nexport default function HeroSection() {\n  const { scrollY } = useScroll()\n  // Parallax title slides in from below as user scrolls down 0 to 300px\n  const y = useTransform(scrollY, [0, 300], [100, 0])\n  const opacity = useTransform(scrollY, [0, 300], [0, 1])\n\n  return (\n    <section className='relative w-full h-[80vh] overflow-hidden'>\n      {/* Video Background */}\n      <video\n        src='/videos/hero-loop.mp4'\n        autoPlay\n        muted\n        loop\n        playsInline\n        preload='auto'\n        className='absolute inset-0 w-full h-full object-cover'\n      />\n      {/* Ripple effect canvas */}\n      <RippleCanvas />\n\n      {/* Overlay gradient */}\n      <div className='absolute inset-0 bg-gradient-to-b from-black/30 via-transparent to-black/40 z-10' />\n\n      {/* Parallax Title */}\n      <motion.div style={{ y, opacity }} className='absolute bottom-20 w-full flex justify-center z-20'>\n        <h1 className='text-5xl md:text-7xl font-extrabold text-white drop-shadow-lg select-none'>\n          The Full Pony Club Experience\n        </h1>\n      </motion.div>\n\n      {/* Scroll Prompt */}\n      <motion.div\n        animate={{ opacity: [0.5, 1, 0.5] }}\n        transition={{ repeat: Infinity, duration: 2 }}\n        className='absolute bottom-6 left-1/2 -translate-x-1/2 z-30 flex flex-col items-center text-white select-none'\n      >\n        <span className='mb-2 text-lg font-semibold'>Adventure begins here</span>\n        <svg\n          className='w-8 h-8 animate-bounce'\n          fill='none'\n          stroke='currentColor'\n          strokeWidth='2'\n          viewBox='0 0 24 24'\n          xmlns='http://www.w3.org/2000/svg'\n          aria-hidden='true'\n        >\n          <path strokeLinecap='round' strokeLinejoin='round' d='M19 9l-7 7-7-7'></path>\n        </svg>\n      </motion.div>\n    </section>\n  )\n}\n", "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/language-selector.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/map-component.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/map-with-no-ssr.tsx", "messages": [{"ruleId": "@typescript-eslint/no-explicit-any", "severity": 1, "message": "Unexpected any. Specify a different type.", "line": 8, "column": 37, "nodeType": "TSAnyKeyword", "messageId": "unexpectedAny", "endLine": 8, "endColumn": 40, "suggestions": [{"messageId": "suggestUnknown", "fix": {"range": [199, 202], "text": "unknown"}, "desc": "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct."}, {"messageId": "suggestNever", "fix": {"range": [199, 202], "text": "never"}, "desc": "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of."}]}, {"ruleId": "no-console", "severity": 1, "message": "Unexpected console statement.", "line": 52, "column": 9, "nodeType": "MemberExpression", "messageId": "unexpected", "endLine": 52, "endColumn": 22, "suggestions": [{"fix": {"range": [1328, 1370], "text": ""}, "messageId": "removeConsole", "data": {"propertyName": "error"}, "desc": "Remove the console.error()."}]}], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "'use client'\n\nimport L from 'leaflet'\nimport { useEffect, useRef, useState } from 'react'\nimport 'leaflet/dist/leaflet.css'\n\n// Fix for default markers in Leaflet\ndelete (L.Icon.Default.prototype as any)._getIconUrl\nL.Icon.Default.mergeOptions({\n  iconRetinaUrl: '/marker-icon.png',\n  iconUrl: '/marker-icon.png',\n  shadowUrl: '/marker-shadow.png',\n})\n\ninterface MapProps {\n  center: [number, number]\n  zoom: number\n  markers?: Array<{\n    position: [number, number]\n    popup?: string\n  }>\n  className?: string\n}\n\nexport default function MapWithNoSSR({ center, zoom, markers = [], className = '' }: MapProps) {\n  const mapRef = useRef<HTMLDivElement>(null)\n  const [mapError, setMapError] = useState(false)\n\n  useEffect(() => {\n    if (!mapRef.current) return\n\n    const loadMap = async () => {\n      try {\n        const map = L.map(mapRef.current!).setView(center, zoom)\n\n        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {\n          attribution: '© OpenStreetMap contributors',\n        }).addTo(map)\n\n        // Add markers\n        markers.forEach(({ position, popup }) => {\n          const marker = L.marker(position).addTo(map)\n          if (popup) {\n            marker.bindPopup(popup)\n          }\n        })\n\n        return () => {\n          map.remove()\n        }\n      } catch (error) {\n        console.error('Error loading map:', error)\n        setMapError(true)\n      }\n    }\n\n    loadMap()\n  }, [center, zoom, markers])\n\n  if (mapError) {\n    return (\n      <div className={`h-96 w-full ${className}`}>\n        <div className='h-full w-full flex items-center justify-center bg-gray-100 rounded-lg'>\n          <div className='text-center p-4'>\n            <p className='text-gray-700 mb-2'>Map could not be loaded</p>\n            <p className='text-sm text-gray-500'>\n              Please visit{' '}\n              <a\n                href='https://www.google.com/maps/search/Acheron+River,+Greece'\n                className='text-blue-500 underline'\n                target='_blank'\n                rel='noopener noreferrer'\n              >\n                Google Maps\n              </a>{' '}\n              to view the location\n            </p>\n          </div>\n        </div>\n      </div>\n    )\n  }\n\n  return <div ref={mapRef} className={`h-96 w-full ${className}`} />\n}\n", "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/responsive-navigation.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/site-header.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/standalone-menu-items.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/theme-provider.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/Container.tsx", "messages": [{"ruleId": "@typescript-eslint/no-unused-vars", "severity": 1, "message": "'ReactNode' is defined but never used. Allowed unused vars must match /^_/u.", "line": 2, "column": 17, "nodeType": null, "messageId": "unusedVar", "endLine": 2, "endColumn": 26}], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "import type { JSX } from 'react'\nimport React, { ReactNode } from 'react' // Added JSX import\n\ninterface ContainerProps {\n  children: React.ReactNode\n  className?: string\n  as?: keyof JSX.IntrinsicElements // This should now find the JSX namespace\n  fluid?: boolean\n}\n\n/**\n * Container component for consistent boxed layout across the site\n *\n * @param children - The content to be displayed within the container\n * @param className - Additional CSS classes to apply\n * @param as - HTML element to render the container as (default: div)\n * @param fluid - Whether the container should have a fluid width on larger screens\n */\nexport function Container({ children, className = '', as: Component = 'div', fluid = false }: ContainerProps) {\n  // Base classes for all containers\n  let containerClasses = 'mx-auto px-4 sm:px-6 md:px-8 lg:px-10'\n\n  // Add max-width constraint if not fluid\n  if (!fluid) {\n    containerClasses += ' max-w-screen-xl' // 1280px max width for desktop\n  }\n\n  return <Component className={`${containerClasses} ${className}`}>{children}</Component>\n}\n", "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/Gallery.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/OptimizedImage.tsx", "messages": [{"ruleId": "complexity", "severity": 1, "message": "Function 'OptimizedImage' has a complexity of 17. Maximum allowed is 15.", "line": 22, "column": 8, "nodeType": "FunctionDeclaration", "messageId": "complex", "endLine": 107, "endColumn": 2}, {"ruleId": "@typescript-eslint/no-explicit-any", "severity": 1, "message": "Unexpected any. Specify a different type.", "line": 38, "column": 66, "nodeType": "TSAnyKeyword", "messageId": "unexpectedAny", "endLine": 38, "endColumn": 69, "suggestions": [{"messageId": "suggestUnknown", "fix": {"range": [1168, 1171], "text": "unknown"}, "desc": "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct."}, {"messageId": "suggestNever", "fix": {"range": [1168, 1171], "text": "never"}, "desc": "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of."}]}, {"ruleId": "jsx-a11y/alt-text", "severity": 1, "message": "Image elements must have an alt prop, either with meaningful text, or an empty string for decorative images.", "line": 75, "column": 9, "nodeType": "JSXOpeningElement", "endLine": 75, "endColumn": 34}, {"ruleId": "jsx-a11y/alt-text", "severity": 1, "message": "Image elements must have an alt prop, either with meaningful text, or an empty string for decorative images.", "line": 106, "column": 10, "nodeType": "JSXOpeningElement", "endLine": 106, "endColumn": 35}], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "'use client'\n\nimport type { ImageProps } from 'next/image'\nimport Image from 'next/image'\nimport React from 'react'\n\nimport { optimizeImageProps, optimizeHeroImage, optimizeGalleryImage } from '@/lib/image-optimization'\n\nexport type OptimizedImageProps = Omit<ImageProps, 'src' | 'alt'> & {\n  src: string\n  alt: string\n  imageType?: 'default' | 'hero' | 'gallery' | 'avatar' | 'logo' | 'thumbnail'\n  index?: number // For gallery images\n  aspectRatio?: string // For setting specific aspect ratios\n  containerClassName?: string // For styling the container\n}\n\n/**\n * OptimizedImage component - A wrapper around Next.js Image component\n * that automatically applies performance optimizations\n */\nexport function OptimizedImage({\n  imageType = 'default',\n  index = 0,\n  aspectRatio,\n  containerClassName,\n  className,\n  ...props\n}: OptimizedImageProps) {\n  // Apply image-type specific optimizations\n  const optimizedProps = (() => {\n    switch (imageType) {\n      case 'hero':\n        return optimizeHeroImage(props)\n      case 'gallery':\n        return optimizeGalleryImage(props, index)\n      default:\n        return optimizeImageProps({ ...props, type: imageType as any })\n    }\n  })()\n\n  // If using fill, we need to wrap in a container with relative positioning\n  if (optimizedProps.fill) {\n    // Apply responsive aspect ratio if provided\n    const aspectRatioClass = aspectRatio ? `aspect-${aspectRatio}` : ''\n\n    // For hero images, make sure we don't have both priority and loading\n    const imageProps =\n      imageType === 'hero'\n        ? {\n            src: props.src,\n            alt: props.alt,\n            fill: true,\n            sizes: optimizedProps.sizes || '100vw',\n            quality: optimizedProps.quality,\n            priority: true,\n            className: `object-cover ${className || ''}`,\n          }\n        : {\n            src: props.src,\n            alt: props.alt,\n            fill: true,\n            sizes: optimizedProps.sizes || '100vw',\n            quality: optimizedProps.quality,\n            priority: optimizedProps.priority,\n            loading: !optimizedProps.priority ? optimizedProps.loading || 'lazy' : undefined,\n            className: `object-cover ${className || ''}`,\n          }\n\n    return (\n      <div\n        className={`relative overflow-hidden ${aspectRatioClass} ${containerClassName || ''}`}\n        style={!aspectRatioClass ? { height: '100%' } : undefined}\n      >\n        <Image {...imageProps} />\n      </div>\n    )\n  }\n\n  // For non-fill images, just render the optimized Image component\n  // Make sure hero images don't have both priority and loading\n  const imageProps =\n    imageType === 'hero'\n      ? {\n          src: props.src,\n          alt: props.alt,\n          width: optimizedProps.width,\n          height: optimizedProps.height,\n          quality: optimizedProps.quality,\n          priority: true,\n          sizes: optimizedProps.sizes,\n          className,\n        }\n      : {\n          src: props.src,\n          alt: props.alt,\n          width: optimizedProps.width,\n          height: optimizedProps.height,\n          quality: optimizedProps.quality,\n          priority: optimizedProps.priority,\n          loading: !optimizedProps.priority ? optimizedProps.loading || 'lazy' : undefined,\n          sizes: optimizedProps.sizes,\n          className,\n        }\n\n  return <Image {...imageProps} />\n}\n\n/**\n * HeroImage component - Specialized for hero images\n */\nexport function HeroImage(props: Omit<OptimizedImageProps, 'imageType'>) {\n  return <OptimizedImage {...props} imageType='hero' />\n}\n\n/**\n * GalleryImage component - Specialized for gallery images\n */\nexport function GalleryImage({ index = 0, ...props }: Omit<OptimizedImageProps, 'imageType'> & { index?: number }) {\n  return <OptimizedImage {...props} imageType='gallery' index={index} />\n}\n\n/**\n * AvatarImage component - Specialized for avatar images\n */\nexport function AvatarImage(props: Omit<OptimizedImageProps, 'imageType'>) {\n  return <OptimizedImage {...props} imageType='avatar' />\n}\n\nexport default OptimizedImage\n", "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/PriceListPopup.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/accordion.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/alert-dialog.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/alert.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/aspect-ratio.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/avatar.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/badge.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/border-beam.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/breadcrumb.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/button.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/calendar.tsx", "messages": [{"ruleId": "@typescript-eslint/no-unused-vars", "severity": 1, "message": "'CustomComponents' is defined but never used. Allowed unused vars must match /^_/u.", "line": 6, "column": 26, "nodeType": null, "messageId": "unusedVar", "endLine": 6, "endColumn": 42}], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "'use client'\n\nimport { ChevronLeft, ChevronRight } from 'lucide-react'\nimport * as React from 'react'\nimport type { SVGProps } from 'react'\nimport { DayPicker, type CustomComponents } from 'react-day-picker' // Import CustomComponents\n\nimport { buttonVariants } from '@/components/ui/button'\nimport { cn } from '@/lib/utils'\n\nexport type CalendarProps = React.ComponentProps<typeof DayPicker>\n\nfunction Calendar({ className, classNames, showOutsideDays = true, ...props }: CalendarProps) {\n  interface ChevronComponentProps extends SVGProps<SVGSVGElement> {\n    orientation?: 'left' | 'right' | 'up' | 'down' // Updated to match DayPicker's expected type\n  }\n\n  const ChevronComponent = ({ orientation, ...restProps }: ChevronComponentProps) => {\n    if (orientation === 'left') {\n      return <ChevronLeft className='h-4 w-4' {...restProps} />\n    }\n    return <ChevronRight className='h-4 w-4' {...restProps} />\n  }\n\n  return (\n    <DayPicker\n      showOutsideDays={showOutsideDays}\n      className={cn('p-3', className)}\n      classNames={{\n        months: 'flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0',\n        month: 'space-y-4',\n        caption: 'flex justify-center pt-1 relative items-center',\n        caption_label: 'text-sm font-medium',\n        nav: 'space-x-1 flex items-center',\n        nav_button: cn(\n          buttonVariants({ variant: 'outline' }),\n          'h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100'\n        ),\n        nav_button_previous: 'absolute left-1',\n        nav_button_next: 'absolute right-1',\n        table: 'w-full border-collapse space-y-1',\n        head_row: 'flex',\n        head_cell: 'text-muted-foreground rounded-md w-9 font-normal text-[0.8rem]',\n        row: 'flex w-full mt-2',\n        cell: 'h-9 w-9 text-center text-sm p-0 relative [&:has([aria-selected].day-range-end)]:rounded-r-md [&:has([aria-selected].day-outside)]:bg-accent/50 [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20',\n        day: cn(buttonVariants({ variant: 'ghost' }), 'h-9 w-9 p-0 font-normal aria-selected:opacity-100'),\n        day_range_end: 'day-range-end',\n        day_selected:\n          'bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground',\n        day_today: 'bg-accent text-accent-foreground',\n        day_outside: 'day-outside text-muted-foreground aria-selected:bg-accent/50 aria-selected:text-muted-foreground',\n        day_disabled: 'text-muted-foreground opacity-50',\n        day_range_middle: 'aria-selected:bg-accent aria-selected:text-accent-foreground',\n        day_hidden: 'invisible',\n        ...classNames,\n      }}\n      components={{\n        Chevron: ChevronComponent,\n      }}\n      {...props}\n    />\n  )\n}\nCalendar.displayName = 'Calendar'\n\nexport { Calendar }\n", "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/card.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/carousel.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/chart.tsx", "messages": [{"ruleId": "@typescript-eslint/no-unused-vars", "severity": 1, "message": "'NameType' is defined but never used. Allowed unused vars must match /^_/u.", "line": 5, "column": 10, "nodeType": null, "messageId": "unusedVar", "endLine": 5, "endColumn": 18}, {"ruleId": "@typescript-eslint/no-unused-vars", "severity": 1, "message": "'Payload' is defined but never used. Allowed unused vars must match /^_/u.", "line": 5, "column": 20, "nodeType": null, "messageId": "unusedVar", "endLine": 5, "endColumn": 27}, {"ruleId": "@typescript-eslint/no-unused-vars", "severity": 1, "message": "'ValueType' is defined but never used. Allowed unused vars must match /^_/u.", "line": 5, "column": 29, "nodeType": null, "messageId": "unusedVar", "endLine": 5, "endColumn": 38}, {"ruleId": "complexity", "severity": 1, "message": "Arrow function has a complexity of 20. Maximum allowed is 15.", "line": 175, "column": 24, "nodeType": "ArrowFunctionExpression", "messageId": "complex", "endLine": 232, "endColumn": 12}], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "'use client'\n\nimport * as React from 'react'\nimport * as RechartsPrimitive from 'recharts'\nimport { NameType, Payload, ValueType } from 'recharts/types/component/DefaultTooltipContent'\n\nimport { cn } from '@/lib/utils'\n\n// Format: { THEME_NAME: CSS_SELECTOR }\nconst THEMES = { light: '', dark: '.dark' } as const\n\nexport type ChartConfig = {\n  [k in string]: {\n    label?: React.ReactNode\n    icon?: React.ComponentType\n  } & ({ color?: string; theme?: never } | { color?: never; theme: Record<keyof typeof THEMES, string> })\n}\n\ntype ChartContextProps = {\n  config: ChartConfig\n}\n\nconst ChartContext = React.createContext<ChartContextProps | null>(null)\n\nfunction useChart() {\n  const context = React.useContext(ChartContext)\n\n  if (!context) {\n    throw new Error('useChart must be used within a <ChartContainer />')\n  }\n\n  return context\n}\n\nconst ChartContainer = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<'div'> & {\n    config: ChartConfig\n    children: React.ComponentProps<typeof RechartsPrimitive.ResponsiveContainer>['children']\n  }\n>(({ id, className, children, config, ...props }, ref) => {\n  const uniqueId = React.useId()\n  const chartId = `chart-${id || uniqueId.replace(/:/g, '')}`\n\n  return (\n    <ChartContext.Provider value={{ config }}>\n      <div\n        data-chart={chartId}\n        ref={ref}\n        className={cn(\n          \"flex aspect-video justify-center text-xs [&_.recharts-cartesian-axis-tick_text]:fill-muted-foreground [&_.recharts-cartesian-grid_line[stroke='#ccc']]:stroke-border/50 [&_.recharts-curve.recharts-tooltip-cursor]:stroke-border [&_.recharts-dot[stroke='#fff']]:stroke-transparent [&_.recharts-layer]:outline-hidden [&_.recharts-polar-grid_[stroke='#ccc']]:stroke-border [&_.recharts-radial-bar-background-sector]:fill-muted [&_.recharts-rectangle.recharts-tooltip-cursor]:fill-muted [&_.recharts-reference-line_[stroke='#ccc']]:stroke-border [&_.recharts-sector[stroke='#fff']]:stroke-transparent [&_.recharts-sector]:outline-hidden [&_.recharts-surface]:outline-hidden\",\n          className\n        )}\n        {...props}\n      >\n        <ChartStyle id={chartId} config={config} />\n        <RechartsPrimitive.ResponsiveContainer>{children}</RechartsPrimitive.ResponsiveContainer>\n      </div>\n    </ChartContext.Provider>\n  )\n})\nChartContainer.displayName = 'Chart'\n\nconst ChartStyle = ({ id, config }: { id: string; config: ChartConfig }) => {\n  const [nonce, setNonce] = React.useState('')\n\n  React.useEffect(() => {\n    // Get nonce from meta tag\n    const metaNonce = document.querySelector('meta[name=\"csp-nonce\"]')?.getAttribute('content') || ''\n    setNonce(metaNonce)\n  }, [])\n\n  const colorConfig = Object.entries(config).filter(([_, config]) => config.theme || config.color)\n\n  if (!colorConfig.length) {\n    return null\n  }\n\n  return (\n    <style\n      nonce={nonce}\n      dangerouslySetInnerHTML={{\n        __html: Object.entries(THEMES)\n          .map(\n            ([theme, prefix]) => `\n${prefix} [data-chart=${id}] {\n${colorConfig\n  .map(([key, itemConfig]) => {\n    const color = itemConfig.theme?.[theme as keyof typeof itemConfig.theme] || itemConfig.color\n    return color ? `  --color-${key}: ${color};` : null\n  })\n  .join('\\n')}\n}\n`\n          )\n          .join('\\n'),\n      }}\n    />\n  )\n}\n\nconst ChartTooltip = RechartsPrimitive.Tooltip\n\nconst ChartTooltipContent = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<typeof RechartsPrimitive.Tooltip> &\n    React.ComponentProps<'div'> & {\n      hideLabel?: boolean\n      hideIndicator?: boolean\n      indicator?: 'line' | 'dot' | 'dashed'\n      nameKey?: string\n      labelKey?: string\n    }\n>(\n  (\n    {\n      active,\n      payload,\n      className,\n      indicator = 'dot',\n      hideLabel = false,\n      hideIndicator = false,\n      label,\n      labelFormatter,\n      labelClassName,\n      formatter,\n      color,\n      nameKey,\n      labelKey,\n    },\n    ref\n  ) => {\n    const { config } = useChart()\n\n    const tooltipLabel = React.useMemo(() => {\n      if (hideLabel || !payload?.length) {\n        return null\n      }\n\n      const [item] = payload\n      const key = `${labelKey || item.dataKey || item.name || 'value'}`\n      const itemConfig = getPayloadConfigFromPayload(config, item, key)\n      const value =\n        !labelKey && typeof label === 'string'\n          ? config[label as keyof typeof config]?.label || label\n          : itemConfig?.label\n\n      if (labelFormatter) {\n        return <div className={cn('font-medium', labelClassName)}>{labelFormatter(value, payload)}</div>\n      }\n\n      if (!value) {\n        return null\n      }\n\n      return <div className={cn('font-medium', labelClassName)}>{value}</div>\n    }, [label, labelFormatter, payload, hideLabel, labelClassName, config, labelKey])\n\n    if (!active || !payload?.length) {\n      return null\n    }\n\n    const nestLabel = payload.length === 1 && indicator !== 'dot'\n\n    return (\n      <div\n        ref={ref}\n        className={cn(\n          'grid min-w-[8rem] items-start gap-1.5 rounded-lg border border-border/50 bg-background px-2.5 py-1.5 text-xs shadow-xl',\n          className\n        )}\n      >\n        {!nestLabel ? tooltipLabel : null}\n        <div className='grid gap-1.5'>\n          {payload.map((item, index) => {\n            const key = `${nameKey || item.name || item.dataKey || 'value'}`\n            const itemConfig = getPayloadConfigFromPayload(config, item, key)\n            const indicatorColor = color || item.payload.fill || item.color\n\n            return (\n              <div\n                key={item.dataKey}\n                className={cn(\n                  'flex w-full flex-wrap items-stretch gap-2 [&>svg]:h-2.5 [&>svg]:w-2.5 [&>svg]:text-muted-foreground',\n                  indicator === 'dot' && 'items-center'\n                )}\n              >\n                {formatter && item?.value !== undefined && item.name ? (\n                  formatter(item.value, item.name, item, index, item.payload)\n                ) : (\n                  <>\n                    {itemConfig?.icon ? (\n                      <itemConfig.icon />\n                    ) : (\n                      !hideIndicator && (\n                        <div\n                          className={cn('shrink-0 rounded-[2px] border-(--color-border) bg-(--color-bg)', {\n                            'h-2.5 w-2.5': indicator === 'dot',\n                            'w-1': indicator === 'line',\n                            'w-0 border-[1.5px] border-dashed bg-transparent': indicator === 'dashed',\n                            'my-0.5': nestLabel && indicator === 'dashed',\n                          })}\n                          style={\n                            {\n                              '--color-bg': indicatorColor,\n                              '--color-border': indicatorColor,\n                            } as React.CSSProperties\n                          }\n                        />\n                      )\n                    )}\n                    <div\n                      className={cn(\n                        'flex flex-1 justify-between leading-none',\n                        nestLabel ? 'items-end' : 'items-center'\n                      )}\n                    >\n                      <div className='grid gap-1.5'>\n                        {nestLabel ? tooltipLabel : null}\n                        <span className='text-muted-foreground'>{itemConfig?.label || item.name}</span>\n                      </div>\n                      {item.value && (\n                        <span className='font-mono font-medium tabular-nums text-foreground'>\n                          {item.value.toLocaleString()}\n                        </span>\n                      )}\n                    </div>\n                  </>\n                )}\n              </div>\n            )\n          })}\n        </div>\n      </div>\n    )\n  }\n)\nChartTooltipContent.displayName = 'ChartTooltip'\n\nconst ChartLegend = RechartsPrimitive.Legend\n\nconst ChartLegendContent = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<'div'> &\n    Pick<RechartsPrimitive.LegendProps, 'payload' | 'verticalAlign'> & {\n      hideIcon?: boolean\n      nameKey?: string\n    }\n>(({ className, hideIcon = false, payload, verticalAlign = 'bottom', nameKey }, ref) => {\n  const { config } = useChart()\n\n  if (!payload?.length) {\n    return null\n  }\n\n  return (\n    <div\n      ref={ref}\n      className={cn('flex items-center justify-center gap-4', verticalAlign === 'top' ? 'pb-3' : 'pt-3', className)}\n    >\n      {payload.map(item => {\n        const key = `${nameKey || item.dataKey || 'value'}`\n        const itemConfig = getPayloadConfigFromPayload(config, item, key)\n\n        return (\n          <div\n            key={item.value}\n            className={cn('flex items-center gap-1.5 [&>svg]:h-3 [&>svg]:w-3 [&>svg]:text-muted-foreground')}\n          >\n            {itemConfig?.icon && !hideIcon ? (\n              <itemConfig.icon />\n            ) : (\n              <div\n                className='h-2 w-2 shrink-0 rounded-[2px]'\n                style={{\n                  backgroundColor: item.color,\n                }}\n              />\n            )}\n            {itemConfig?.label}\n          </div>\n        )\n      })}\n    </div>\n  )\n})\nChartLegendContent.displayName = 'ChartLegend'\n\n// Helper to extract item config from a payload.\nfunction getPayloadConfigFromPayload(config: ChartConfig, payload: unknown, key: string) {\n  if (typeof payload !== 'object' || payload === null) {\n    return undefined\n  }\n\n  const payloadPayload =\n    'payload' in payload && typeof payload.payload === 'object' && payload.payload !== null\n      ? payload.payload\n      : undefined\n\n  let configLabelKey: string = key\n\n  if (key in payload && typeof payload[key as keyof typeof payload] === 'string') {\n    configLabelKey = payload[key as keyof typeof payload] as string\n  } else if (\n    payloadPayload &&\n    key in payloadPayload &&\n    typeof payloadPayload[key as keyof typeof payloadPayload] === 'string'\n  ) {\n    configLabelKey = payloadPayload[key as keyof typeof payloadPayload] as string\n  }\n\n  return configLabelKey in config ? config[configLabelKey] : config[key as keyof typeof config]\n}\n\nexport { ChartContainer, ChartTooltip, ChartTooltipContent, ChartLegend, ChartLegendContent, ChartStyle }\n", "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/checkbox.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/collapsible.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/command.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/context-menu.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/dialog.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/drawer.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/dropdown-menu.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/form.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/grid-pattern.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/hover-card.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/input-otp.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/input.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/label.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/menubar.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/navigation-menu.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/number-ticker.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/pagination.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/popover.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/progress.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/pulsating-button.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/radio-group.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/resizable.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/script-loader.tsx", "messages": [{"ruleId": "@typescript-eslint/no-unused-vars", "severity": 1, "message": "'props' is defined but never used. Allowed unused args must match /^_/u.", "line": 34, "column": 6, "nodeType": null, "messageId": "unusedVar", "endLine": 34, "endColumn": 11}], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "'use client'\n\nimport Script from 'next/script'\nimport { useEffect, useState } from 'react'\n\ninterface ScriptLoaderProps {\n  src: string\n  id?: string\n  strategy?: 'beforeInteractive' | 'afterInteractive' | 'lazyOnload'\n  onLoad?: () => void\n  onError?: () => void\n  dangerouslySetInnerHTML?: { __html: string }\n  inViewport?: boolean // Only load when in viewport\n  dataSrc?: string\n  dataTestId?: string\n  nonce?: string // Allow passing nonce as a prop\n}\n\n/**\n * OptimizedScript component - A wrapper for Next.js Script component\n * with improved loading strategies and performance optimizations\n */\nexport function ScriptLoader({\n  src,\n  id,\n  strategy = 'afterInteractive',\n  onLoad,\n  onError,\n  dangerouslySetInnerHTML,\n  inViewport = false,\n  dataSrc,\n  dataTestId,\n  nonce: propNonce, // Use the prop name\n  ...props\n}: ScriptLoaderProps) {\n  const [shouldLoad, setShouldLoad] = useState(!inViewport)\n  const [effectiveNonce, setEffectiveNonce] = useState(propNonce || '')\n\n  useEffect(() => {\n    if (!propNonce) {\n      // If nonce is not passed as a prop, try to get it from the meta tag\n      const metaNonce = document.querySelector('meta[name=\"csp-nonce\"]')?.getAttribute('content') || ''\n      setEffectiveNonce(metaNonce)\n    }\n  }, [propNonce]) // Re-run if propNonce changes (though unlikely for this use case)\n\n  useEffect(() => {\n    if (inViewport) {\n      // Create intersection observer for viewport detection\n      const observer = new IntersectionObserver(entries => {\n        entries.forEach(entry => {\n          if (entry.isIntersecting) {\n            setShouldLoad(true)\n            observer.disconnect()\n          }\n        })\n      })\n\n      // Create a div element to observe\n      const element = document.createElement('div')\n      element.id = `script-observer-${id || Math.random().toString(36).substring(2)}`\n      element.style.height = '1px'\n      element.style.width = '1px'\n      element.style.position = 'absolute'\n      element.style.bottom = '200px' // Load slightly before scrolling to it\n      element.style.left = '0'\n      document.body.appendChild(element)\n\n      observer.observe(element)\n\n      return () => {\n        observer.disconnect()\n        if (document.body.contains(element)) {\n          document.body.removeChild(element)\n        }\n      }\n    }\n  }, [id, inViewport])\n\n  if (!shouldLoad) {\n    return null\n  }\n\n  return (\n    <Script\n      id={id}\n      src={src}\n      strategy={strategy}\n      onLoad={onLoad}\n      onError={onError}\n      dangerouslySetInnerHTML={dangerouslySetInnerHTML}\n      data-src={dataSrc}\n      data-testid={dataTestId}\n      nonce={dangerouslySetInnerHTML ? effectiveNonce : undefined} // Use effectiveNonce\n    />\n  )\n}\n\nexport default ScriptLoader\n", "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/scroll-area.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/select.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/separator.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/sheet.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/sidebar.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/skeleton.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/slider.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/sonner.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/switch.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/table.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/tabs.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/textarea.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/toast.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/toaster.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/toggle-group.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/toggle.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/tooltip.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/use-mobile.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/use-toast.ts", "messages": [{"ruleId": "@typescript-eslint/no-unused-vars", "severity": 1, "message": "'actionTypes' is assigned a value but only used as a type. Allowed unused vars must match /^_/u.", "line": 18, "column": 7, "nodeType": null, "messageId": "usedOnlyAsType", "endLine": 18, "endColumn": 18}], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "'use client'\n\n// Inspired by react-hot-toast library\nimport * as React from 'react'\n\nimport type { ToastActionElement, ToastProps } from '@/components/ui/toast'\n\nconst TOAST_LIMIT = 1\nconst TOAST_REMOVE_DELAY = 1000000\n\ntype ToasterToast = ToastProps & {\n  id: string\n  title?: React.ReactNode\n  description?: React.ReactNode\n  action?: ToastActionElement\n}\n\nconst actionTypes = {\n  ADD_TOAST: 'ADD_TOAST',\n  UPDATE_TOAST: 'UPDATE_TOAST',\n  DISMISS_TOAST: 'DISMISS_TOAST',\n  REMOVE_TOAST: 'REMOVE_TOAST',\n} as const\n\nlet count = 0\n\nfunction genId() {\n  count = (count + 1) % Number.MAX_SAFE_INTEGER\n  return count.toString()\n}\n\ntype ActionType = typeof actionTypes\n\ntype Action =\n  | {\n      type: ActionType['ADD_TOAST']\n      toast: ToasterToast\n    }\n  | {\n      type: ActionType['UPDATE_TOAST']\n      toast: Partial<ToasterToast>\n    }\n  | {\n      type: ActionType['DISMISS_TOAST']\n      toastId?: ToasterToast['id']\n    }\n  | {\n      type: ActionType['REMOVE_TOAST']\n      toastId?: ToasterToast['id']\n    }\n\ninterface State {\n  toasts: ToasterToast[]\n}\n\nconst toastTimeouts = new Map<string, ReturnType<typeof setTimeout>>()\n\nconst addToRemoveQueue = (toastId: string) => {\n  if (toastTimeouts.has(toastId)) {\n    return\n  }\n\n  const timeout = setTimeout(() => {\n    toastTimeouts.delete(toastId)\n    dispatch({\n      type: 'REMOVE_TOAST',\n      toastId,\n    })\n  }, TOAST_REMOVE_DELAY)\n\n  toastTimeouts.set(toastId, timeout)\n}\n\nexport const reducer = (state: State, action: Action): State => {\n  switch (action.type) {\n    case 'ADD_TOAST':\n      return {\n        ...state,\n        toasts: [action.toast, ...state.toasts].slice(0, TOAST_LIMIT),\n      }\n\n    case 'UPDATE_TOAST':\n      return {\n        ...state,\n        toasts: state.toasts.map(t => (t.id === action.toast.id ? { ...t, ...action.toast } : t)),\n      }\n\n    case 'DISMISS_TOAST': {\n      const { toastId } = action\n\n      // ! Side effects ! - This could be extracted into a dismissToast() action,\n      // but I'll keep it here for simplicity\n      if (toastId) {\n        addToRemoveQueue(toastId)\n      } else {\n        state.toasts.forEach(toast => {\n          addToRemoveQueue(toast.id)\n        })\n      }\n\n      return {\n        ...state,\n        toasts: state.toasts.map(t =>\n          t.id === toastId || toastId === undefined\n            ? {\n                ...t,\n                open: false,\n              }\n            : t\n        ),\n      }\n    }\n    case 'REMOVE_TOAST':\n      if (action.toastId === undefined) {\n        return {\n          ...state,\n          toasts: [],\n        }\n      }\n      return {\n        ...state,\n        toasts: state.toasts.filter(t => t.id !== action.toastId),\n      }\n  }\n}\n\nconst listeners: Array<(state: State) => void> = []\n\nlet memoryState: State = { toasts: [] }\n\nfunction dispatch(action: Action) {\n  memoryState = reducer(memoryState, action)\n  listeners.forEach(listener => {\n    listener(memoryState)\n  })\n}\n\ntype Toast = Omit<ToasterToast, 'id'>\n\nfunction toast({ ...props }: Toast) {\n  const id = genId()\n\n  const update = (props: ToasterToast) =>\n    dispatch({\n      type: 'UPDATE_TOAST',\n      toast: { ...props, id },\n    })\n  const dismiss = () => dispatch({ type: 'DISMISS_TOAST', toastId: id })\n\n  dispatch({\n    type: 'ADD_TOAST',\n    toast: {\n      ...props,\n      id,\n      open: true,\n      onOpenChange: open => {\n        if (!open) dismiss()\n      },\n    },\n  })\n\n  return {\n    id,\n    dismiss,\n    update,\n  }\n}\n\nfunction useToast() {\n  const [state, setState] = React.useState<State>(memoryState)\n\n  React.useEffect(() => {\n    listeners.push(setState)\n    return () => {\n      const index = listeners.indexOf(setState)\n      if (index > -1) {\n        listeners.splice(index, 1)\n      }\n    }\n  }, [state])\n\n  return {\n    ...state,\n    toast,\n    dismiss: (toastId?: string) => dispatch({ type: 'DISMISS_TOAST', toastId }),\n  }\n}\n\nexport { useToast, toast }\n", "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/contexts/gdpr-context.tsx", "messages": [{"ruleId": "no-console", "severity": 1, "message": "Unexpected console statement.", "line": 49, "column": 9, "nodeType": "MemberExpression", "messageId": "unexpected", "endLine": 49, "endColumn": 22, "suggestions": [{"fix": {"range": [1445, 1498], "text": ""}, "messageId": "removeConsole", "data": {"propertyName": "error"}, "desc": "Remove the console.error()."}]}, {"ruleId": "no-console", "severity": 1, "message": "Unexpected console statement.", "line": 157, "column": 3, "nodeType": "MemberExpression", "messageId": "unexpected", "endLine": 157, "endColumn": 14, "suggestions": [{"fix": {"range": [3956, 4003], "text": ""}, "messageId": "removeConsole", "data": {"propertyName": "log"}, "desc": "Remove the console.log()."}]}], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "'use client'\n\nimport React, { createContext, useContext, useEffect, useState } from 'react'\n\nexport interface CookieConsent {\n  necessary: boolean\n  analytics: boolean\n  marketing: boolean\n}\n\ninterface GDPRContextType {\n  consent: CookieConsent | null\n  showBanner: boolean\n  showCustomize: boolean\n  acceptAll: () => void\n  rejectAll: () => void\n  saveCustom: (consent: CookieConsent) => void\n  openCustomize: () => void\n  closeBanner: () => void\n}\n\nconst GDPRContext = createContext<GDPRContextType | undefined>(undefined)\n\nconst CONSENT_COOKIE_NAME = 'ponyclub-cookie-consent'\nconst CONSENT_VERSION = '1.0'\n\nexport function GDPRProvider({ children }: { children: React.ReactNode }) {\n  const [consent, setConsent] = useState<CookieConsent | null>(null)\n  const [showBanner, setShowBanner] = useState(false)\n  const [showCustomize, setShowCustomize] = useState(false)\n\n  // Load consent from localStorage on mount\n  useEffect(() => {\n    if (typeof window === 'undefined') return\n\n    const savedConsent = localStorage.getItem(CONSENT_COOKIE_NAME)\n    if (savedConsent) {\n      try {\n        const parsed = JSON.parse(savedConsent)\n        if (parsed.version === CONSENT_VERSION) {\n          setConsent(parsed.consent)\n          // Apply consent to tracking scripts\n          applyConsent(parsed.consent)\n        } else {\n          // Version mismatch, show banner again\n          setShowBanner(true)\n        }\n      } catch (error) {\n        console.error('Error parsing consent cookie:', error)\n        setShowBanner(true)\n      }\n    } else {\n      // No consent found, show banner\n      setShowBanner(true)\n    }\n  }, [])\n\n  const saveConsent = (newConsent: CookieConsent) => {\n    const consentData = {\n      consent: newConsent,\n      version: CONSENT_VERSION,\n      timestamp: new Date().toISOString(),\n    }\n\n    localStorage.setItem(CONSENT_COOKIE_NAME, JSON.stringify(consentData))\n    setConsent(newConsent)\n    setShowBanner(false)\n    setShowCustomize(false)\n\n    // Apply consent to tracking scripts\n    applyConsent(newConsent)\n\n    // Track consent choice\n    trackConsentChoice(newConsent)\n  }\n\n  const acceptAll = () => {\n    saveConsent({\n      necessary: true,\n      analytics: true,\n      marketing: true,\n    })\n  }\n\n  const rejectAll = () => {\n    saveConsent({\n      necessary: true, // Always true - required for site functionality\n      analytics: false,\n      marketing: false,\n    })\n  }\n\n  const saveCustom = (customConsent: CookieConsent) => {\n    saveConsent({\n      ...customConsent,\n      necessary: true, // Always true - required for site functionality\n    })\n  }\n\n  const openCustomize = () => {\n    setShowCustomize(true)\n  }\n\n  const closeBanner = () => {\n    setShowBanner(false)\n    setShowCustomize(false)\n  }\n\n  return (\n    <GDPRContext.Provider\n      value={{\n        consent,\n        showBanner,\n        showCustomize,\n        acceptAll,\n        rejectAll,\n        saveCustom,\n        openCustomize,\n        closeBanner,\n      }}\n    >\n      {children}\n    </GDPRContext.Provider>\n  )\n}\n\nexport function useGDPR() {\n  const context = useContext(GDPRContext)\n  if (context === undefined) {\n    throw new Error('useGDPR must be used within a GDPRProvider')\n  }\n  return context\n}\n\n// Apply consent to tracking scripts\nfunction applyConsent(consent: CookieConsent) {\n  if (typeof window === 'undefined') return\n\n  // Google Analytics\n  if (window.gtag) {\n    window.gtag('consent', 'update', {\n      analytics_storage: consent.analytics ? 'granted' : 'denied',\n      ad_storage: consent.marketing ? 'granted' : 'denied',\n      ad_user_data: consent.marketing ? 'granted' : 'denied',\n      ad_personalization: consent.marketing ? 'granted' : 'denied',\n    })\n  }\n\n  // Facebook Pixel\n  if (window.fbq && !consent.marketing) {\n    // Disable Facebook Pixel if marketing consent is denied\n    window.fbq('consent', 'revoke')\n  } else if (window.fbq && consent.marketing) {\n    window.fbq('consent', 'grant')\n  }\n\n  console.log('[GDPR] Consent applied:', consent)\n}\n\n// Track consent choice for analytics\nfunction trackConsentChoice(consent: CookieConsent) {\n  if (typeof window === 'undefined') return\n\n  // Only track if analytics consent is given\n  if (consent.analytics && window.gtag) {\n    window.gtag('event', 'consent_update', {\n      event_category: 'GDPR',\n      analytics_consent: consent.analytics,\n      marketing_consent: consent.marketing,\n      consent_method: 'banner',\n    })\n  }\n}\n", "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/contexts/language-context.tsx", "messages": [{"ruleId": "import/order", "severity": 1, "message": "There should be at least one empty line between import groups", "line": 5, "column": 1, "nodeType": "ImportDeclaration", "endLine": 5, "endColumn": 57, "fix": {"range": [151, 151], "text": "\n"}}, {"ruleId": "import/order", "severity": 1, "message": "`next/navigation` import should occur before type import of `react`", "line": 5, "column": 1, "nodeType": "ImportDeclaration", "endLine": 5, "endColumn": 57, "fix": {"range": [14, 152], "text": "import { usePathname, useRouter } from 'next/navigation'\nimport type React from 'react'\nimport { createContext, useContext } from 'react'\n"}}, {"ruleId": "import/order", "severity": 1, "message": "`@/lib/bokun-lang` import should occur before import of `@/lib/translations`", "line": 7, "column": 1, "nodeType": "ImportDeclaration", "endLine": 7, "endColumn": 48, "fix": {"range": [152, 287], "text": "import { bokunLangMap } from '@/lib/bokun-lang'\nimport { type Language, translations, type TranslationKeys } from '@/lib/translations'\n"}}], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 3, "source": "'use client'\n\nimport type React from 'react'\nimport { createContext, useContext } from 'react'\nimport { usePathname, useRouter } from 'next/navigation'\nimport { type Language, translations, type TranslationKeys } from '@/lib/translations'\nimport { bokunLangMap } from '@/lib/bokun-lang'\nimport { useBokunLanguage } from '@/lib/use-bokun-language'\n\ntype LanguageContextType = {\n  language: Language\n  t: TranslationKeys\n  setLanguage: (lang: Language) => void\n}\n\nconst defaultLanguage: Language = 'en'\n\ninterface LanguageProviderProps {\n  children: React.ReactNode\n  initialLang: string // From URL via app/[locale]/layout.tsx\n}\n\nconst LanguageContext = createContext<LanguageContextType | undefined>(undefined)\n\nexport function LanguageProvider({ children, initialLang }: LanguageProviderProps) {\n  const router = useRouter()\n  const pathname = usePathname()\n\n  // The language is now directly determined by the `initialLang` prop from the URL.\n  // This ensures the URL is the single source of truth.\n  const language = initialLang === 'en' || initialLang === 'el' ? initialLang : defaultLanguage\n\n  // This function will be called by the language selector.\n  // It sets a cookie with the new language preference and then navigates\n  // to the new URL, causing the app to re-render with the correct locale.\n  const setLanguage = (newLocale: Language) => {\n    // Set cookie to remember the user's choice\n    document.cookie = `NEXT_LOCALE=${newLocale};path=/;max-age=31536000`\n\n    // Determine the new path\n    const currentLocale = language\n    let newPath\n    if (pathname.startsWith(`/${currentLocale}/`)) {\n      newPath = pathname.replace(`/${currentLocale}/`, `/${newLocale}/`)\n    } else if (pathname === `/${currentLocale}`) {\n      newPath = `/${newLocale}`\n    } else {\n      // Fallback for unexpected path structures\n      newPath = `/${newLocale}${pathname === '/' ? '' : pathname}`\n    }\n\n    // Navigate to the new path, preserving query parameters\n    const search = window.location.search\n    router.push(newPath + search)\n  }\n\n  // Get translations for the current language\n  const t = translations[language]\n\n  // Hook to update Bokun widgets language on change\n  useBokunLanguage(bokunLangMap[language])\n\n  return <LanguageContext.Provider value={{ language, t, setLanguage }}>{children}</LanguageContext.Provider>\n}\n\nexport function useLanguage() {\n  const context = useContext(LanguageContext)\n  if (context === undefined) {\n    throw new Error('useLanguage must be used within a LanguageProvider')\n  }\n  return context\n}\n", "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/contexts/maps-api-context.tsx", "messages": [{"ruleId": "@typescript-eslint/consistent-type-imports", "severity": 2, "message": "Imports \"ReactNode\" are only used as type.", "line": 3, "column": 1, "nodeType": "ImportDeclaration", "messageId": "someImportsAreOnlyTypes", "endLine": 3, "endColumn": 78, "fix": {"range": [14, 76], "text": "import type { ReactNode } from 'react';\nimport React, { createContext, useContext, useState"}}], "suppressedMessages": [], "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 1, "fixableWarningCount": 0, "source": "'use client'\n\nimport React, { createContext, useContext, useState, ReactNode } from 'react'\n\ninterface MapsApiContextType {\n  apiKey: string | null\n  setApiKey: (key: string) => void\n}\n\nconst MapsApiContext = createContext<MapsApiContextType | undefined>(undefined)\n\nexport function MapsApiProvider({ children }: { children: ReactNode }) {\n  const [apiKey, setApiKey] = useState<string | null>('AIzaSyBwaJVGFhnhN-WKtiLn6KSa7PvRrauytHQ')\n\n  return <MapsApiContext.Provider value={{ apiKey, setApiKey }}>{children}</MapsApiContext.Provider>\n}\n\nexport function useMapsApi() {\n  const context = useContext(MapsApiContext)\n  if (context === undefined) {\n    throw new Error('useMapsApi must be used within a MapsApiProvider')\n  }\n  return context\n}\n", "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/hooks/use-mobile.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/hooks/use-toast.ts", "messages": [{"ruleId": "@typescript-eslint/no-unused-vars", "severity": 1, "message": "'actionTypes' is assigned a value but only used as a type. Allowed unused vars must match /^_/u.", "line": 18, "column": 7, "nodeType": null, "messageId": "usedOnlyAsType", "endLine": 18, "endColumn": 18}, {"ruleId": "object-shorthand", "severity": 2, "message": "Expected property shorthand.", "line": 67, "column": 7, "nodeType": "Property", "messageId": "expectedProperty<PERSON><PERSON><PERSON>d", "endLine": 67, "endColumn": 23, "fix": {"range": [1352, 1368], "text": "toastId"}}, {"ruleId": "object-shorthand", "severity": 2, "message": "Expected property shorthand.", "line": 163, "column": 5, "nodeType": "Property", "messageId": "expectedProperty<PERSON><PERSON><PERSON>d", "endLine": 163, "endColumn": 11, "fix": {"range": [3427, 3433], "text": "id"}}], "suppressedMessages": [], "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 2, "fixableWarningCount": 0, "source": "'use client'\n\n// Inspired by react-hot-toast library\nimport * as React from 'react'\n\nimport type { ToastActionElement, ToastProps } from '@/components/ui/toast'\n\nconst TOAST_LIMIT = 1\nconst TOAST_REMOVE_DELAY = 1000000\n\ntype ToasterToast = ToastProps & {\n  id: string\n  title?: React.ReactNode\n  description?: React.ReactNode\n  action?: ToastActionElement\n}\n\nconst actionTypes = {\n  ADD_TOAST: 'ADD_TOAST',\n  UPDATE_TOAST: 'UPDATE_TOAST',\n  DISMISS_TOAST: 'DISMISS_TOAST',\n  REMOVE_TOAST: 'REMOVE_TOAST',\n} as const\n\nlet count = 0\n\nfunction genId() {\n  count = (count + 1) % Number.MAX_SAFE_INTEGER\n  return count.toString()\n}\n\ntype ActionType = typeof actionTypes\n\ntype Action =\n  | {\n      type: ActionType['ADD_TOAST']\n      toast: ToasterToast\n    }\n  | {\n      type: ActionType['UPDATE_TOAST']\n      toast: Partial<ToasterToast>\n    }\n  | {\n      type: ActionType['DISMISS_TOAST']\n      toastId?: ToasterToast['id']\n    }\n  | {\n      type: ActionType['REMOVE_TOAST']\n      toastId?: ToasterToast['id']\n    }\n\ninterface State {\n  toasts: ToasterToast[]\n}\n\nconst toastTimeouts = new Map<string, ReturnType<typeof setTimeout>>()\n\nconst addToRemoveQueue = (toastId: string) => {\n  if (toastTimeouts.has(toastId)) {\n    return\n  }\n\n  const timeout = setTimeout(() => {\n    toastTimeouts.delete(toastId)\n    dispatch({\n      type: 'REMOVE_TOAST',\n      toastId: toastId,\n    })\n  }, TOAST_REMOVE_DELAY)\n\n  toastTimeouts.set(toastId, timeout)\n}\n\nexport const reducer = (state: State, action: Action): State => {\n  switch (action.type) {\n    case 'ADD_TOAST':\n      return {\n        ...state,\n        toasts: [action.toast, ...state.toasts].slice(0, TOAST_LIMIT),\n      }\n\n    case 'UPDATE_TOAST':\n      return {\n        ...state,\n        toasts: state.toasts.map(t => (t.id === action.toast.id ? { ...t, ...action.toast } : t)),\n      }\n\n    case 'DISMISS_TOAST': {\n      const { toastId } = action\n\n      // ! Side effects ! - This could be extracted into a dismissToast() action,\n      // but I'll keep it here for simplicity\n      if (toastId) {\n        addToRemoveQueue(toastId)\n      } else {\n        state.toasts.forEach(toast => {\n          addToRemoveQueue(toast.id)\n        })\n      }\n\n      return {\n        ...state,\n        toasts: state.toasts.map(t =>\n          t.id === toastId || toastId === undefined\n            ? {\n                ...t,\n                open: false,\n              }\n            : t\n        ),\n      }\n    }\n    case 'REMOVE_TOAST':\n      if (action.toastId === undefined) {\n        return {\n          ...state,\n          toasts: [],\n        }\n      }\n      return {\n        ...state,\n        toasts: state.toasts.filter(t => t.id !== action.toastId),\n      }\n  }\n}\n\nconst listeners: Array<(state: State) => void> = []\n\nlet memoryState: State = { toasts: [] }\n\nfunction dispatch(action: Action) {\n  memoryState = reducer(memoryState, action)\n  listeners.forEach(listener => {\n    listener(memoryState)\n  })\n}\n\ntype Toast = Omit<ToasterToast, 'id'>\n\nfunction toast({ ...props }: Toast) {\n  const id = genId()\n\n  const update = (props: ToasterToast) =>\n    dispatch({\n      type: 'UPDATE_TOAST',\n      toast: { ...props, id },\n    })\n  const dismiss = () => dispatch({ type: 'DISMISS_TOAST', toastId: id })\n\n  dispatch({\n    type: 'ADD_TOAST',\n    toast: {\n      ...props,\n      id,\n      open: true,\n      onOpenChange: open => {\n        if (!open) dismiss()\n      },\n    },\n  })\n\n  return {\n    id: id,\n    dismiss,\n    update,\n  }\n}\n\nfunction useToast() {\n  const [state, setState] = React.useState<State>(memoryState)\n\n  React.useEffect(() => {\n    listeners.push(setState)\n    return () => {\n      const index = listeners.indexOf(setState)\n      if (index > -1) {\n        listeners.splice(index, 1)\n      }\n    }\n  }, [state])\n\n  return {\n    ...state,\n    toast,\n    dismiss: (toastId?: string) => dispatch({ type: 'DISMISS_TOAST', toastId }),\n  }\n}\n\nexport { useToast, toast }\n", "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/lib/bokun-lang.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/lib/gallery-data/index.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/lib/gallery-data/kayaking.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/lib/gallery-data/rafting.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/lib/gallery-data/riding.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/lib/gallery-data/river-village.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/lib/gallery-data/trekking.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/lib/image-optimization.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/lib/sitemap-data.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/lib/translations/el.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/lib/translations/en.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/lib/translations/index.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/lib/use-bokun-language.ts", "messages": [{"ruleId": "@typescript-eslint/no-explicit-any", "severity": 1, "message": "Unexpected any. Specify a different type.", "line": 6, "column": 30, "nodeType": "TSAnyKeyword", "messageId": "unexpectedAny", "endLine": 6, "endColumn": 33, "suggestions": [{"messageId": "suggestUnknown", "fix": {"range": [179, 182], "text": "unknown"}, "desc": "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct."}, {"messageId": "suggestNever", "fix": {"range": [179, 182], "text": "never"}, "desc": "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of."}]}], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "import { useEffect } from 'react'\n\nexport function useBokunLanguage(lang: string) {\n  useEffect(() => {\n    if (typeof window === 'undefined') return\n    const bokun = (window as any).BokunWidgets\n    if (!bokun) return\n\n    // Set global default for pop-up checkout (if supported)\n    if (typeof bokun.setLanguage === 'function') {\n      bokun.setLanguage(lang)\n    }\n\n    // Update all widgets and buttons with the new language\n    document.querySelectorAll<HTMLElement>('.bokunWidget, .bokunButton').forEach(el => {\n      // Update data-src URL with lang query param\n      if (el.dataset.src) {\n        const url = new URL(el.dataset.src, window.location.origin)\n        url.searchParams.set('lang', lang)\n        el.dataset.src = url.toString()\n      }\n      // Set data-lang attribute for newer widgets\n      el.setAttribute('data-lang', lang)\n    })\n\n    // Reload widgets to apply language change\n    if (typeof bokun.reload === 'function') {\n      bokun.reload()\n    }\n  }, [lang])\n}\n", "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/lib/utils.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/middleware.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/next-env.d.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/scripts/generate-sitemap-data.js", "messages": [{"ruleId": "@typescript-eslint/no-require-imports", "severity": 2, "message": "A `require()` style import is forbidden.", "line": 3, "column": 22, "nodeType": "CallExpression", "messageId": "noRequireImports", "endLine": 3, "endColumn": 46}, {"ruleId": "@typescript-eslint/no-require-imports", "severity": 2, "message": "A `require()` style import is forbidden.", "line": 4, "column": 12, "nodeType": "CallExpression", "messageId": "noRequireImports", "endLine": 4, "endColumn": 25}, {"ruleId": "@typescript-eslint/no-require-imports", "severity": 2, "message": "A `require()` style import is forbidden.", "line": 5, "column": 14, "nodeType": "CallExpression", "messageId": "noRequireImports", "endLine": 5, "endColumn": 29}, {"ruleId": "@typescript-eslint/no-unused-vars", "severity": 1, "message": "'error' is defined but never used.", "line": 18, "column": 12, "nodeType": null, "messageId": "unusedVar", "endLine": 18, "endColumn": 17}, {"ruleId": "no-console", "severity": 1, "message": "Unexpected console statement.", "line": 19, "column": 5, "nodeType": "MemberExpression", "messageId": "unexpected", "endLine": 19, "endColumn": 17, "suggestions": [{"fix": {"range": [640, 719], "text": ""}, "messageId": "removeConsole", "data": {"propertyName": "warn"}, "desc": "Remove the console.warn()."}]}, {"ruleId": "no-console", "severity": 1, "message": "Unexpected console statement.", "line": 43, "column": 5, "nodeType": "MemberExpression", "messageId": "unexpected", "endLine": 43, "endColumn": 16, "suggestions": [{"fix": {"range": [1626, 1704], "text": ""}, "messageId": "removeConsole", "data": {"propertyName": "log"}, "desc": "Remove the console.log()."}]}, {"ruleId": "no-console", "severity": 1, "message": "Unexpected console statement.", "line": 66, "column": 3, "nodeType": "MemberExpression", "messageId": "unexpected", "endLine": 66, "endColumn": 14, "suggestions": [{"fix": {"range": [2279, 2334], "text": ""}, "messageId": "removeConsole", "data": {"propertyName": "log"}, "desc": "Remove the console.log()."}]}, {"ruleId": "no-console", "severity": 1, "message": "Unexpected console statement.", "line": 67, "column": 3, "nodeType": "MemberExpression", "messageId": "unexpected", "endLine": 67, "endColumn": 14, "suggestions": [{"fix": {"range": [2337, 2376], "text": ""}, "messageId": "removeConsole", "data": {"propertyName": "log"}, "desc": "Remove the console.log()."}]}, {"ruleId": "no-console", "severity": 1, "message": "Unexpected console statement.", "line": 68, "column": 3, "nodeType": "MemberExpression", "messageId": "unexpected", "endLine": 68, "endColumn": 14, "suggestions": [{"fix": {"range": [2379, 2434], "text": ""}, "messageId": "removeConsole", "data": {"propertyName": "log"}, "desc": "Remove the console.log()."}]}], "suppressedMessages": [], "errorCount": 3, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "#!/usr/bin/env node\n\nconst { execSync } = require('child_process')\nconst fs = require('fs')\nconst path = require('path')\n\n/**\n * Get the last Git commit timestamp for a file\n * @param {string} filePath - The path to the file\n * @returns {number} - Unix timestamp of last commit affecting this file\n */\nfunction getGitLastModified(filePath) {\n  try {\n    // Get the timestamp of the last commit that modified this file\n    const gitCmd = `git log -1 --format=\"%ct\" -- \"${filePath}\"`\n    const result = execSync(gitCmd, { encoding: 'utf8' }).trim()\n    return result ? parseInt(result) : Math.floor(Date.now() / 1000)\n  } catch (error) {\n    console.warn(`Could not get Git timestamp for ${filePath}, using current time`)\n    return Math.floor(Date.now() / 1000)\n  }\n}\n\n/**\n * Generate sitemap data for all routes\n */\nfunction generateSitemapData() {\n  // Define the routes and their corresponding file paths\n  const routes = [\n    { route: '', filePath: 'app/[locale]/page.tsx' },\n    { route: '/kayaking', filePath: 'app/[locale]/kayaking/page.tsx' },\n    { route: '/rafting', filePath: 'app/[locale]/rafting/page.tsx' },\n    { route: '/riding', filePath: 'app/[locale]/riding/page.tsx' },\n    { route: '/river-village', filePath: 'app/[locale]/river-village/page.tsx' },\n    { route: '/trekking', filePath: 'app/[locale]/trekking/page.tsx' },\n\n    { route: '/for-schools', filePath: 'app/[locale]/for-schools/page.tsx' },\n    { route: '/kayak-rafting', filePath: 'app/[locale]/kayak-rafting/page.tsx' },\n  ]\n\n  const routeData = routes.map(({ route, filePath }) => {\n    const lastModified = getGitLastModified(filePath)\n    console.log(`${route || '/'}: ${new Date(lastModified * 1000).toISOString()}`)\n    return {\n      route,\n      fileMtime: lastModified,\n    }\n  })\n\n  // Generate the TypeScript content for sitemap data\n  const tsContent = `// Auto-generated sitemap data - Do not edit manually\n// Generated on: ${new Date().toISOString()}\n\nexport interface RouteData {\n  route: string;\n  fileMtime: number;\n}\n\nexport const routeData: RouteData[] = ${JSON.stringify(routeData, null, 2)};\n`\n\n  // Write to a TypeScript file that can be imported by sitemap.ts\n  const outputPath = path.join(__dirname, '../lib/sitemap-data.ts')\n  fs.writeFileSync(outputPath, tsContent)\n\n  console.log(`\\n✅ Sitemap data generated successfully:`)\n  console.log(`📁 Output: ${outputPath}`)\n  console.log(`📊 Routes processed: ${routeData.length}`)\n}\n\n// Run if called directly\nif (require.main === module) {\n  generateSitemapData()\n}\n\nmodule.exports = { generateSitemapData }\n", "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/types/bokun.d.ts", "messages": [{"ruleId": "@typescript-eslint/no-explicit-any", "severity": 1, "message": "Unexpected any. Specify a different type.", "line": 7, "column": 22, "nodeType": "TSAnyKeyword", "messageId": "unexpectedAny", "endLine": 7, "endColumn": 25, "suggestions": [{"messageId": "suggestUnknown", "fix": {"range": [155, 158], "text": "unknown"}, "desc": "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct."}, {"messageId": "suggestNever", "fix": {"range": [155, 158], "text": "never"}, "desc": "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of."}]}], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "// types/bokun.d.ts\nexport {} // Ensure this file is treated as a module.\n\ndeclare global {\n  interface Window {\n    BokunWidgets?: {\n      [key: string]: any\n      init?: () => void\n      reinit?: () => void\n      setLanguage?: (lang: string) => void\n    }\n  }\n}\n", "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/types/esbuild.d.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/types/global.d.ts", "messages": [{"ruleId": "@typescript-eslint/no-explicit-any", "severity": 1, "message": "Unexpected any. Specify a different type.", "line": 10, "column": 24, "nodeType": "TSAnyKeyword", "messageId": "unexpectedAny", "endLine": 10, "endColumn": 27, "suggestions": [{"messageId": "suggestUnknown", "fix": {"range": [277, 280], "text": "unknown"}, "desc": "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct."}, {"messageId": "suggestNever", "fix": {"range": [277, 280], "text": "never"}, "desc": "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of."}]}, {"ruleId": "@typescript-eslint/no-explicit-any", "severity": 1, "message": "Unexpected any. Specify a different type.", "line": 43, "column": 24, "nodeType": "TSAnyKeyword", "messageId": "unexpectedAny", "endLine": 43, "endColumn": 27, "suggestions": [{"messageId": "suggestUnknown", "fix": {"range": [1222, 1225], "text": "unknown"}, "desc": "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct."}, {"messageId": "suggestNever", "fix": {"range": [1222, 1225], "text": "never"}, "desc": "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of."}]}], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "// Global type declarations for tracking and analytics\n\ndeclare global {\n  interface Window {\n    dataLayer?: unknown[]\n    gtag?: (\n      command: 'config' | 'event' | 'js' | 'set' | 'consent',\n      targetId: string | Date | 'update',\n      config?: {\n        [key: string]: any\n        send_to?: string\n        value?: number\n        currency?: string\n        transaction_id?: string\n        event_category?: string\n        event_label?: string\n        package_name?: string\n        package_price?: number\n        button_id?: string\n        page_location?: string\n        page_title?: string\n        analytics_storage?: 'granted' | 'denied'\n        ad_storage?: 'granted' | 'denied'\n        ad_user_data?: 'granted' | 'denied'\n        ad_personalization?: 'granted' | 'denied'\n        analytics_consent?: boolean\n        marketing_consent?: boolean\n        consent_method?: string\n        items?: Array<{\n          item_id?: string\n          item_name?: string\n          item_category?: string\n          price?: number\n          quantity?: number\n        }>\n      }\n    ) => void\n\n    fbq?: (\n      command: 'track' | 'consent',\n      eventName: string | 'grant' | 'revoke',\n      properties?: {\n        [key: string]: any\n        content_name?: string\n        content_category?: string\n        value?: number\n        currency?: string\n      }\n    ) => void\n  }\n}\n\nexport {}\n", "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/types/leaflet-css.d.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/types/pg-protocol.d.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/types/vite.d.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": []}]