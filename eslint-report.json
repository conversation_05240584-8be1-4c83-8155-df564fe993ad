[{"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/app/[locale]/error.tsx", "messages": [{"ruleId": "no-console", "severity": 1, "message": "Unexpected console statement.", "line": 21, "column": 7, "nodeType": "MemberExpression", "messageId": "unexpected", "endLine": 21, "endColumn": 20, "suggestions": [{"fix": {"range": [598, 618], "text": ""}, "messageId": "removeConsole", "data": {"propertyName": "error"}, "desc": "Remove the console.error()."}]}], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "'use client'\n\nimport Link from 'next/link' // Added Link for logo\nimport { useEffect } from 'react'\n\nimport ResponsiveNavigation from '@/components/responsive-navigation'\nimport { OptimizedImage } from '@/components/ui/OptimizedImage'\nimport { useLanguage } from '@/contexts/language-context'\n\ninterface ErrorProps {\n  error: Error\n  reset: () => void\n}\n\nexport default function Error({ error, reset }: ErrorProps) {\n  const { t } = useLanguage()\n\n  useEffect(() => {\n    // Log error in development, send to monitoring service in production\n    if (process.env.NODE_ENV === 'development') {\n      console.error(error)\n    }\n    // In production, you might want to send this to a logging service\n    // like Sentry, LogRocket, etc.\n  }, [error])\n\n  return (\n    <>\n      <header\n        className={`\n          fixed top-0 right-0 left-0 z-40 flex items-center justify-between\n          border-b border-gray-200 bg-[#FAF7F2] px-4 py-3\n          sm:px-6\n          lg:px-8\n        `}\n      >\n        {/* Logo */}\n        <div>\n          <Link href='/' className='flex items-center'>\n            <div\n              className={`\n                relative h-12 w-48\n                md:h-14 md:w-56\n                lg:h-16 lg:w-64\n              `}\n            >\n              <OptimizedImage\n                src='/images/ponyclub_logo.png'\n                alt='Pony Club Logo'\n                fill\n                sizes='(max-width: 768px) 192px, (max-width: 1024px) 224px, 256px'\n                className='object-contain p-1'\n                imageType='logo'\n              />\n            </div>\n          </Link>\n        </div>\n\n        {/* Responsive Navigation */}\n        <div>\n          <ResponsiveNavigation />\n        </div>\n      </header>\n\n      <main\n        className={`\n          flex min-h-screen flex-col items-center justify-center bg-[#f5f0e8]\n          p-4 pt-20 text-center\n        `}\n      >\n        {' '}\n        {/* Added bg color and pt-20 */}\n        <h1\n          className={`\n            mb-4 text-5xl font-bold text-amber-800\n            md:text-6xl\n          `}\n        >\n          {t.error.title}\n        </h1>{' '}\n        {/* Styled heading */}\n        <p className='mb-6 text-lg text-gray-700'>{t.error.message}</p>\n        <button\n          onClick={() => reset()}\n          className={`\n            rounded-lg bg-[#c27a5f] px-6 py-3 font-semibold text-white\n            transition-colors\n            hover:bg-[#b06c50]\n          `} /* Styled button (using a site color) */\n        >\n          {t.error.tryAgain}\n        </button>\n      </main>\n    </>\n  )\n}\n", "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/app/[locale]/for-schools/page.tsx", "messages": [{"ruleId": "better-tailwindcss/multiline", "severity": 1, "message": "Unnecessary line wrapping. Expected\n\n`↵\n······························text-lg·font-semibold·text-gray-800↵\n····························`\n\nto be\n\n`text-lg·font-semibold·text-gray-800`", "line": 641, "column": 42, "nodeType": null, "endLine": 643, "endColumn": 30, "fix": {"range": [23899, 23996], "text": "`text-lg font-semibold text-gray-800`"}}, {"ruleId": "better-tailwindcss/multiline", "severity": 1, "message": "Unnecessary line wrapping. Expected\n\n`↵\n······························text-lg·font-semibold·text-gray-800↵\n····························`\n\nto be\n\n`text-lg·font-semibold·text-gray-800`", "line": 693, "column": 42, "nodeType": null, "endLine": 695, "endColumn": 30, "fix": {"range": [26351, 26448], "text": "`text-lg font-semibold text-gray-800`"}}, {"ruleId": "better-tailwindcss/multiline", "severity": 1, "message": "Unnecessary line wrapping. Expected\n\n`↵\n··························mt-1·h-6·w-6·flex-shrink-0·text-[#6b8362]↵\n························`\n\nto be\n\n`mt-1·h-6·w-6·flex-shrink-0·text-[#6b8362]`", "line": 728, "column": 38, "nodeType": null, "endLine": 730, "endColumn": 26, "fix": {"range": [27908, 28003], "text": "`mt-1 h-6 w-6 flex-shrink-0 text-[#6b8362]`"}}, {"ruleId": "better-tailwindcss/multiline", "severity": 1, "message": "Unnecessary line wrapping. Expected\n\n`↵\n······················flex·items-center·space-x-3↵\n····················`\n\nto be\n\n`flex·items-center·space-x-3`", "line": 948, "column": 34, "nodeType": null, "endLine": 950, "endColumn": 22, "fix": {"range": [36693, 36766], "text": "`flex items-center space-x-3`"}}], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 4, "source": "import {\n  Clock,\n  Users,\n  Target,\n  Waves,\n  Mountain,\n  TreePine,\n  Camera,\n  MapPin,\n  Star,\n  ArrowRight,\n  CheckCircle,\n  Calendar,\n  Euro,\n} from 'lucide-react'\nimport type { Metada<PERSON> } from 'next'\nimport { Roboto_Slab } from 'next/font/google'\nimport Link from 'next/link'\n\nimport ResponsiveNavigation from '@/components/responsive-navigation'\nimport { BorderBeam } from '@/components/ui/border-beam'\nimport { GridPattern } from '@/components/ui/grid-pattern'\nimport { NumberTicker } from '@/components/ui/number-ticker'\nimport { OptimizedImage } from '@/components/ui/OptimizedImage'\nimport { PulsatingButton } from '@/components/ui/pulsating-button'\n\n// Define Roboto Slab font instance\nconst robotoSlab = Roboto_Slab({\n  subsets: ['latin', 'greek'],\n  variable: '--font-roboto-slab',\n  weight: ['400', '700', '900'],\n})\n\nexport const metadata: Metadata = {\n  title: 'Για τα σχολεία | Pony Club',\n  description: 'Προγράμματα εκδρομών για σχολεία στις πηγές του Αχέροντα',\n}\n\nexport default function ForSchoolsPage() {\n  return (\n    <>\n      <header\n        className={`\n          fixed top-0 right-0 left-0 z-40 flex items-center justify-between\n          border-b border-gray-200 bg-[#FAF7F2] px-4 py-3\n          sm:px-6\n          lg:px-8\n        `}\n      >\n        {/* Logo */}\n        <div>\n          <Link href='/' className='flex items-center'>\n            <div\n              className={`\n                relative h-12 w-48\n                md:h-14 md:w-56\n                lg:h-16 lg:w-64\n              `}\n            >\n              <OptimizedImage\n                src='/images/ponyclub_logo.png'\n                alt='Pony Club Logo'\n                fill\n                sizes='(max-width: 768px) 192px, (max-width: 1024px) 224px, 256px'\n                className='object-contain p-1'\n                imageType='logo'\n              />\n            </div>\n          </Link>\n        </div>\n\n        {/* Responsive Navigation */}\n        <div>\n          <ResponsiveNavigation />\n        </div>\n      </header>\n\n      <main\n        className={`\n          relative min-h-screen overflow-hidden bg-gradient-to-br from-[#f5f0e8]\n          via-[#faf7f2] to-[#f0ebe3] pt-20\n        `}\n      >\n        {/* Background Pattern */}\n        <GridPattern\n          width={60}\n          height={60}\n          x={-1}\n          y={-1}\n          className={`\n            absolute inset-0 h-full w-full stroke-[#6b8362]/5 opacity-30\n            [mask-image:radial-gradient(ellipse_at_center,white,transparent_80%)]\n          `}\n        />\n\n        {/* Hero Section */}\n        <div\n          className={`\n            relative h-[60vh] w-full\n            md:h-[70vh]\n            lg:h-[80vh]\n          `}\n        >\n          <div\n            className={`\n              absolute inset-0 m-4 overflow-hidden rounded-3xl border\n              border-amber-200/30 shadow-2xl\n            `}\n          >\n            <OptimizedImage\n              src='/images/Children_In_Lifejackets_Colorful_OutdoorScene_Riverside.jpg'\n              alt='Παιδιά σε σχολική εκδρομή στον Αχέροντα'\n              fill\n              className={`\n                object-cover object-center transition-transform duration-700\n                hover:scale-105\n              `}\n              priority\n              imageType='hero'\n            />\n            <div\n              className={`\n                absolute inset-0 bg-gradient-to-b from-black/20 via-transparent\n                to-black/40\n              `}\n            ></div>\n            <BorderBeam size={250} duration={12} delay={9} />\n          </div>\n\n          {/* Hero Title Box */}\n          <div\n            className={`\n              absolute inset-0 flex items-start justify-center pt-10\n              md:pt-16\n            `}\n          >\n            <div className='group relative'>\n              <div\n                className={`\n                  absolute -inset-1 rounded-3xl bg-gradient-to-r from-amber-600\n                  to-orange-600 opacity-25 blur transition duration-1000\n                  group-hover:opacity-40 group-hover:duration-200\n                `}\n              ></div>\n              <div\n                className={`\n                  relative max-w-4xl transform rounded-3xl border\n                  border-amber-200/30 bg-gradient-to-br from-amber-800/60\n                  to-amber-900/70 px-8 py-6 shadow-2xl backdrop-blur-md\n                  transition-all duration-500\n                  hover:scale-[1.02]\n                `}\n              >\n                <h1\n                  className={`\n                    ${robotoSlab.variable}\n                    px-4 text-center font-roboto-slab text-4xl leading-tight\n                    font-bold text-amber-50\n                    md:text-5xl\n                    lg:text-6xl\n                  `}\n                >\n                  <span\n                    className={`\n                      mb-3 block animate-pulse\n                      drop-shadow-[0_4px_8px_rgba(0,0,0,0.3)]\n                    `}\n                  >\n                    ΓΙΑ ΤΑ ΣΧΟΛΕΙΑ\n                  </span>\n                  <span\n                    className={`\n                      block font-extrabold tracking-wide text-white\n                      drop-shadow-[0_4px_8px_rgba(0,0,0,0.4)]\n                    `}\n                  >\n                    ΕΚΔΡΟΜΕΣ ΣΤΟΝ ΑΧΕΡΟΝΤΑ\n                  </span>\n                </h1>\n                <div className='mt-4 flex justify-center space-x-2'>\n                  <div\n                    className={`\n                      h-2 w-2 animate-bounce rounded-full bg-amber-300\n                    `}\n                  ></div>\n                  <div\n                    className='h-2 w-2 animate-bounce rounded-full bg-amber-300'\n                    style={{ animationDelay: '0.1s' }}\n                  ></div>\n                  <div\n                    className='h-2 w-2 animate-bounce rounded-full bg-amber-300'\n                    style={{ animationDelay: '0.2s' }}\n                  ></div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Hero Bottom Text Banner */}\n        <div className='relative z-20 mx-4 -mt-8'>\n          <div className='group relative'>\n            <div\n              className={`\n                absolute -inset-1 rounded-2xl bg-gradient-to-r from-[#6b8362]\n                to-amber-600 opacity-20 blur transition duration-1000\n                group-hover:opacity-30\n              `}\n            ></div>\n            <div\n              className={`\n                relative mx-auto max-w-4xl rounded-2xl border\n                border-amber-100/50 bg-white/95 px-8 py-6 shadow-xl\n                backdrop-blur-md\n              `}\n            >\n              <div\n                className={`\n                  flex flex-wrap items-center justify-center space-x-4\n                `}\n              >\n                <div className='flex items-center space-x-2'>\n                  <MapPin className='h-5 w-5 text-[#6b8362]' />\n                  <span\n                    className={`\n                      ${robotoSlab.variable}\n                      font-roboto-slab text-lg font-semibold text-[#6b8362]\n                      md:text-xl\n                    `}\n                  >\n                    Πηγές του Αχέροντα\n                  </span>\n                </div>\n                <div\n                  className={`\n                    hidden h-6 w-px bg-amber-300\n                    md:block\n                  `}\n                ></div>\n                <div className='flex items-center space-x-2'>\n                  <Euro className='h-5 w-5 text-amber-600' />\n                  <span\n                    className={`\n                      ${robotoSlab.variable}\n                      font-roboto-slab text-lg font-bold text-amber-800\n                      md:text-xl\n                    `}\n                  >\n                    Από <NumberTicker value={7} /> ευρώ\n                  </span>\n                </div>\n              </div>\n              <p\n                className={`\n                  ${robotoSlab.variable}\n                  mt-2 text-center font-roboto-slab text-base text-gray-700\n                  md:text-lg\n                `}\n              >\n                Ράφτινγκ • Τοξοβολία • Πεζοπορία • Ιππασία\n              </p>\n              <BorderBeam size={250} duration={15} delay={5} />\n            </div>\n          </div>\n        </div>\n\n        {/* Content Section */}\n        <div\n          className={`\n            container mx-auto flex max-w-7xl flex-col gap-12 px-4 py-16\n            sm:px-6\n            lg:px-8\n          `}\n        >\n          {/* Program Schedule Section */}\n          <div className='relative'>\n            <div\n              className={`\n                absolute -inset-1 rounded-3xl bg-gradient-to-r from-[#6b8362]/20\n                to-amber-600/20 opacity-30 blur\n              `}\n            ></div>\n            <div\n              className={`\n                relative rounded-3xl border border-amber-100/50 bg-white/90 p-8\n                shadow-xl backdrop-blur-md\n              `}\n            >\n              <div className='mb-12 text-center'>\n                <div className='mb-4 inline-flex items-center space-x-3'>\n                  <Calendar className='h-8 w-8 text-[#6b8362]' />\n                  <h2\n                    className={`\n                      ${robotoSlab.variable}\n                      font-roboto-slab text-3xl font-bold text-[#6b8362]\n                      md:text-4xl\n                    `}\n                  >\n                    ΠΕΡΙΠΕΤΕΙΑΤΙΚΟ ΠΡΟΓΡΑΜΜΑ\n                  </h2>\n                  <Calendar className='h-8 w-8 text-[#6b8362]' />\n                </div>\n                <div\n                  className={`\n                    mx-auto h-1 w-24 rounded-full bg-gradient-to-r\n                    from-[#6b8362] to-amber-600\n                  `}\n                ></div>\n              </div>\n\n              <div\n                className={`\n                  grid grid-cols-1 items-start gap-12\n                  lg:grid-cols-2\n                `}\n              >\n                {/* Timeline */}\n                <div className='space-y-8'>\n                  {[\n                    {\n                      time: '9:00-10:00',\n                      icon: MapPin,\n                      title: 'Άφιξη & Υποδοχή',\n                      description: 'Άφιξη στον μυστηριώδη χώρο της περιπέτειας και γνωριμία με το περιβάλλον.',\n                    },\n                    {\n                      time: '10:00-10:30',\n                      icon: Users,\n                      title: 'Ομαδοποίηση & Briefing',\n                      description:\n                        'Ενώνουμε τις δυνάμεις μας και αποκτούμε γνώσεις για το ταξίδι μας στις πηγές του ποταμού.',\n                    },\n                    {\n                      time: '10:30-13:00',\n                      icon: Target,\n                      title: 'Δραστηριότητες & Εξερεύνηση',\n                      description: 'Εξερευνούμε τα μυστικά μονοπάτια και πραγματοποιούμε διάφορες αποστολές σε ομάδες.',\n                    },\n                    {\n                      time: '13:00-14:00',\n                      icon: Star,\n                      title: 'Γεύμα & Επιστροφή',\n                      description: 'Απολαμβάνουμε ένα θαυμάσιο γεύμα και επιστρέφουμε με νέες ιστορίες να μοιραστούμε.',\n                    },\n                  ].map((item, index) => (\n                    <div key={index} className='group relative'>\n                      <div className='flex items-start space-x-6'>\n                        <div className='relative'>\n                          <div\n                            className={`\n                              flex h-16 w-16 items-center justify-center\n                              rounded-2xl bg-gradient-to-br from-[#6b8362]\n                              to-[#5a7354] font-bold text-white shadow-lg\n                              transition-transform duration-300\n                              group-hover:scale-110\n                            `}\n                          >\n                            <item.icon className='h-6 w-6' />\n                          </div>\n                          <div\n                            className={`\n                              absolute -inset-1 rounded-2xl bg-gradient-to-br\n                              from-[#6b8362] to-amber-600 opacity-0 blur\n                              transition duration-300\n                              group-hover:opacity-30\n                            `}\n                          ></div>\n                        </div>\n                        <div className='min-w-0 flex-1'>\n                          <div className='mb-2 flex items-center space-x-3'>\n                            <Clock className='h-4 w-4 text-amber-600' />\n                            <span\n                              className={`\n                                rounded-full bg-amber-100 px-3 py-1 text-sm\n                                font-bold text-amber-700\n                              `}\n                            >\n                              {item.time}\n                            </span>\n                          </div>\n                          <h3 className='mb-2 text-xl font-bold text-[#6b8362]'>{item.title}</h3>\n                          <p className='leading-relaxed text-gray-700'>{item.description}</p>\n                        </div>\n                      </div>\n                      {index < 3 && (\n                        <div\n                          className={`\n                            absolute top-16 left-8 h-8 w-px bg-gradient-to-b\n                            from-[#6b8362]/50 to-transparent\n                          `}\n                        ></div>\n                      )}\n                    </div>\n                  ))}\n                </div>\n\n                {/* Program Image */}\n                <div className='group relative'>\n                  <div\n                    className={`\n                      absolute -inset-1 rounded-3xl bg-gradient-to-r\n                      from-amber-600 to-[#6b8362] opacity-20 blur transition\n                      duration-1000\n                      group-hover:opacity-30\n                    `}\n                  ></div>\n                  <div\n                    className={`\n                      relative aspect-4/3 w-full overflow-hidden rounded-3xl\n                      border border-amber-100/50 shadow-2xl\n                    `}\n                  >\n                    <OptimizedImage\n                      src='/images/children_rafting_activity_bright_outdoors.jpg'\n                      alt='Σχολική εκδρομή στον Αχέροντα'\n                      fill\n                      className={`\n                        object-cover transition-transform duration-700\n                        group-hover:scale-110\n                      `}\n                    />\n                    <div\n                      className={`\n                        absolute inset-0 bg-gradient-to-t from-black/20\n                        via-transparent to-transparent\n                      `}\n                    ></div>\n                    <BorderBeam size={250} duration={20} delay={10} />\n                  </div>\n                </div>\n              </div>\n              <BorderBeam size={300} duration={15} delay={0} />\n            </div>\n          </div>\n\n          {/* Activities Section */}\n          <div className='relative'>\n            <div\n              className={`\n                absolute -inset-1 rounded-3xl bg-gradient-to-r from-amber-600/20\n                to-[#6b8362]/20 opacity-30 blur\n              `}\n            ></div>\n            <div\n              className={`\n                relative rounded-3xl border border-amber-100/50 bg-white/90 p-8\n                shadow-xl backdrop-blur-md\n              `}\n            >\n              <div className='mb-12 text-center'>\n                <div className='mb-4 inline-flex items-center space-x-3'>\n                  <Target className='h-8 w-8 text-[#6b8362]' />\n                  <h2\n                    className={`\n                      ${robotoSlab.variable}\n                      font-roboto-slab text-3xl font-bold text-[#6b8362]\n                      md:text-4xl\n                    `}\n                  >\n                    ΟΙ ΔΡΑΣΤΗΡΙΟΤΗΤΕΣ ΠΟΥ ΘΑ ΚΑΝΟΥΜΕ\n                  </h2>\n                  <Target className='h-8 w-8 text-[#6b8362]' />\n                </div>\n                <div\n                  className={`\n                    mx-auto h-1 w-24 rounded-full bg-gradient-to-r\n                    from-[#6b8362] to-amber-600\n                  `}\n                ></div>\n              </div>\n\n              <div\n                className={`\n                  grid grid-cols-1 items-start gap-12\n                  lg:grid-cols-2\n                `}\n              >\n                {/* Hero Image */}\n                <div\n                  className={`\n                    group relative order-2\n                    lg:order-1\n                  `}\n                >\n                  <div\n                    className={`\n                      absolute -inset-1 rounded-3xl bg-gradient-to-r\n                      from-[#6b8362] to-amber-600 opacity-20 blur transition\n                      duration-1000\n                      group-hover:opacity-30\n                    `}\n                  ></div>\n                  <div\n                    className={`\n                      relative aspect-4/3 w-full overflow-hidden rounded-3xl\n                      border border-amber-100/50 shadow-2xl\n                    `}\n                  >\n                    <OptimizedImage\n                      src='/images/Rafting_Group_YellowHelmets_OutdoorRiver.jpg'\n                      alt='Παιδιά σε δραστηριότητες'\n                      fill\n                      className={`\n                        object-cover transition-transform duration-700\n                        group-hover:scale-110\n                      `}\n                    />\n                    <div\n                      className={`\n                        absolute inset-0 bg-gradient-to-t from-black/20\n                        via-transparent to-transparent\n                      `}\n                    ></div>\n                    <BorderBeam size={250} duration={18} delay={7} />\n                  </div>\n                </div>\n\n                {/* Activities List */}\n                <div\n                  className={`\n                    order-1 space-y-8\n                    lg:order-2\n                  `}\n                >\n                  <div\n                    className={`\n                      mb-8 text-center\n                      lg:text-left\n                    `}\n                  >\n                    <h3\n                      className={`\n                        ${robotoSlab.variable}\n                        mb-4 font-roboto-slab text-2xl font-bold text-[#6b8362]\n                        md:text-3xl\n                      `}\n                    >\n                      ΕΞΕΡΕΥΝΗΣΤΕ ΤΟ ΑΠΙΣΤΕΥΤΟ\n                    </h3>\n                    <div\n                      className={`\n                        mx-auto h-1 w-16 rounded-full bg-gradient-to-r\n                        from-[#6b8362] to-amber-600\n                        lg:mx-0\n                      `}\n                    ></div>\n                  </div>\n\n                  {[\n                    {\n                      icon: Waves,\n                      title: 'ΡΑΦΤΙΝΓΚ ΜΕ ΤΗΝ ΟΙΚΟΓΕΝΕΙΑ',\n                      description:\n                        'Κατευθυνθείτε σε μια συναρπαστική περιπέτεια μέσα από ειδικές διαδρομές για μικρούς και μεγάλους!',\n                      color: 'from-blue-500 to-cyan-500',\n                    },\n                    {\n                      icon: TreePine,\n                      title: 'ΑΝΑΚΑΛΥΨΤΕ ΤΙΣ ΠΗΓΕΣ',\n                      description:\n                        'Περπατήστε στα μονοπάτια της φύσης και ανακαλύψτε τις μυστικές πηγές και τους κρυστάλλινους ποταμούς.',\n                      color: 'from-green-500 to-emerald-500',\n                    },\n                    {\n                      icon: Target,\n                      title: 'ΤΟΞΟΒΟΛΙΑ ΣΤΗ ΦΥΣΗ',\n                      description:\n                        'Αφήστε την απόλυτη ελευθερία να σας κατακτήσει σε μια συναρπαστική εμπειρία τοξοβολίας στη φύση.',\n                      color: 'from-amber-500 to-orange-500',\n                    },\n                  ].map((activity, index) => (\n                    <div key={index} className='group relative'>\n                      <div\n                        className={`\n                          absolute -inset-1 rounded-2xl bg-gradient-to-r\n                          from-[#6b8362]/20 to-amber-600/20 opacity-0 blur\n                          transition duration-500\n                          group-hover:opacity-100\n                        `}\n                      ></div>\n                      <div\n                        className={`\n                          relative rounded-2xl border border-amber-100/50\n                          bg-white/80 p-6 shadow-lg backdrop-blur-sm\n                          transition-all duration-300\n                          group-hover:shadow-xl\n                        `}\n                      >\n                        <div className='flex items-start space-x-4'>\n                          <div\n                            className={`\n                              flex h-14 w-14 items-center justify-center\n                              rounded-xl bg-gradient-to-br\n                              ${activity.color}\n                              text-white shadow-lg transition-transform\n                              duration-300\n                              group-hover:scale-110\n                            `}\n                          >\n                            <activity.icon className='h-7 w-7' />\n                          </div>\n                          <div className='min-w-0 flex-1'>\n                            <h4\n                              className={`\n                                mb-2 text-lg font-bold text-[#6b8362]\n                                transition-colors\n                                group-hover:text-[#5a7354]\n                              `}\n                            >\n                              {index + 1}. {activity.title}\n                            </h4>\n                            <p className='leading-relaxed text-gray-700'>{activity.description}</p>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  ))}\n\n                  {/* Pricing Cards */}\n                  <div className='mt-8 space-y-4'>\n                    <div className='group relative'>\n                      <div\n                        className={`\n                          absolute -inset-1 rounded-2xl bg-gradient-to-r\n                          from-[#6b8362] to-amber-600 opacity-20 blur transition\n                          duration-500\n                          group-hover:opacity-30\n                        `}\n                      ></div>\n                      <div\n                        className={`\n                          relative rounded-2xl border border-amber-200/50\n                          bg-gradient-to-br from-amber-50 to-amber-100/50 p-6\n                          shadow-lg\n                        `}\n                      >\n                        <div className='flex items-center justify-between'>\n                          <div className='flex items-center space-x-3'>\n                            <Euro className='h-6 w-6 text-[#6b8362]' />\n                            <span\n                              className={`\n                              text-lg font-semibold text-gray-800\n                            `}\n                            >\n                              Βασικό Πρόγραμμα\n                            </span>\n                          </div>\n                          <div className='text-right'>\n                            <div className='text-2xl font-bold text-[#6b8362]'>\n                              <NumberTicker value={7} /> ευρώ\n                            </div>\n                            <div className='text-sm text-gray-600'>ανά άτομο</div>\n                          </div>\n                        </div>\n                        <div className='mt-4 flex flex-wrap gap-2'>\n                          {['Ράφτινγκ', 'Τοξοβολία', 'Πεζοπορία', 'Εξοπλισμός'].map((item, idx) => (\n                            <span\n                              key={idx}\n                              className={`\n                                inline-flex items-center rounded-full\n                                bg-[#6b8362]/10 px-3 py-1 text-xs font-medium\n                                text-[#6b8362]\n                              `}\n                            >\n                              <CheckCircle className='mr-1 h-3 w-3' />\n                              {item}\n                            </span>\n                          ))}\n                        </div>\n                      </div>\n                    </div>\n\n                    <div className='group relative'>\n                      <div\n                        className={`\n                          absolute -inset-1 rounded-2xl bg-gradient-to-r\n                          from-amber-600 to-orange-600 opacity-20 blur\n                          transition duration-500\n                          group-hover:opacity-30\n                        `}\n                      ></div>\n                      <div\n                        className={`\n                          relative rounded-2xl border border-orange-200/50\n                          bg-gradient-to-br from-orange-50 to-orange-100/50 p-6\n                          shadow-lg\n                        `}\n                      >\n                        <div className='flex items-center justify-between'>\n                          <div className='flex items-center space-x-3'>\n                            <Mountain className='h-6 w-6 text-amber-600' />\n                            <span\n                              className={`\n                              text-lg font-semibold text-gray-800\n                            `}\n                            >\n                              Με Ιππασία\n                            </span>\n                          </div>\n                          <div className='text-right'>\n                            <div className='text-2xl font-bold text-amber-600'>\n                              +<NumberTicker value={5} /> ευρώ\n                            </div>\n                            <div className='text-sm text-gray-600'>προαιρετικά</div>\n                          </div>\n                        </div>\n                        <p className='mt-3 text-sm text-gray-700'>Βόλτα με εκπαιδευμένα άλογα στη φύση</p>\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Additional Info */}\n                  <div className='group relative mt-8'>\n                    <div\n                      className={`\n                        absolute -inset-1 rounded-2xl bg-gradient-to-r\n                        from-[#6b8362]/10 to-amber-600/10 opacity-50 blur\n                      `}\n                    ></div>\n                    <div\n                      className={`\n                        relative rounded-2xl border border-[#6b8362]/20\n                        bg-gradient-to-br from-[#6b8362]/5 to-amber-50/50 p-6\n                      `}\n                    >\n                      <div className='flex items-start space-x-3'>\n                        <Star\n                          className={`\n                          mt-1 h-6 w-6 flex-shrink-0 text-[#6b8362]\n                        `}\n                        />\n                        <div>\n                          <h4 className='mb-2 font-bold text-[#6b8362]'>Γεύματα & Εστίαση</h4>\n                          <p className='leading-relaxed text-gray-700'>\n                            Στον χώρο της εκδρομής, θα βρείτε εστιατόρια με αυθεντική κουζίνα και άριστη υποδομή, όπου\n                            μπορείτε να απολαύσετε γεύματα με θέα στη φύση, με τιμές από{' '}\n                            <span className='font-bold text-[#6b8362]'>\n                              <NumberTicker value={7} /> έως <NumberTicker value={10} /> ευρώ\n                            </span>{' '}\n                            ανά άτομο.\n                          </p>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n              <BorderBeam size={300} duration={20} delay={5} />\n            </div>\n          </div>\n\n          {/* Gallery Section */}\n          <div className='relative'>\n            <div\n              className={`\n                absolute -inset-1 rounded-3xl bg-gradient-to-r from-[#6b8362]/20\n                to-amber-600/20 opacity-30 blur\n              `}\n            ></div>\n            <div\n              className={`\n                relative rounded-3xl border border-amber-100/50 bg-white/90 p-8\n                shadow-xl backdrop-blur-md\n              `}\n            >\n              <div className='mb-12 text-center'>\n                <div className='mb-4 inline-flex items-center space-x-3'>\n                  <Camera className='h-8 w-8 text-[#6b8362]' />\n                  <h2\n                    className={`\n                      ${robotoSlab.variable}\n                      font-roboto-slab text-3xl font-bold text-[#6b8362]\n                      md:text-4xl\n                    `}\n                  >\n                    ΦΩΤΟΓΡΑΦΙΕΣ ΣΧΟΛΙΚΩΝ ΔΡΑΣΤΗΡΙΟΤΗΤΩΝ\n                  </h2>\n                  <Camera className='h-8 w-8 text-[#6b8362]' />\n                </div>\n                <div\n                  className={`\n                    mx-auto h-1 w-24 rounded-full bg-gradient-to-r\n                    from-[#6b8362] to-amber-600\n                  `}\n                ></div>\n              </div>\n\n              <div\n                className={`\n                  grid grid-cols-1 gap-8\n                  md:grid-cols-2\n                  lg:grid-cols-3\n                `}\n              >\n                {[\n                  {\n                    src: '/images/Children_In_Lifejackets_Colorful_OutdoorScene_Riverside.jpg',\n                    alt: 'Παιδιά σε δραστηριότητες ράφτινγκ',\n                    title: 'Ράφτινγκ Περιπέτεια',\n                  },\n                  {\n                    src: '/images/ChildrenRafting_GreenOutdoor_Adventurous_RiverScene.jpg',\n                    alt: 'Παιδιά σε περιπέτεια ράφτινγκ',\n                    title: 'Ομαδική Δραστηριότητα',\n                  },\n                  {\n                    src: '/images/Hiking_Group_Green_Nature_Stream.jpg',\n                    alt: 'Ομάδα σε πεζοπορία στη φύση',\n                    title: 'Εξερεύνηση Φύσης',\n                  },\n                ].map((image, index) => (\n                  <div key={index} className='group relative'>\n                    <div\n                      className={`\n                        absolute -inset-1 rounded-3xl bg-gradient-to-r\n                        from-[#6b8362] to-amber-600 opacity-0 blur transition\n                        duration-700\n                        group-hover:opacity-30\n                      `}\n                    ></div>\n                    <div className='relative'>\n                      <div\n                        className={`\n                          relative aspect-4/3 overflow-hidden rounded-3xl border\n                          border-amber-100/50 shadow-2xl\n                        `}\n                      >\n                        <OptimizedImage\n                          src={image.src}\n                          alt={image.alt}\n                          fill\n                          className={`\n                            object-cover transition-transform duration-700\n                            group-hover:scale-110\n                          `}\n                        />\n                        <div\n                          className={`\n                            absolute inset-0 bg-gradient-to-t from-black/40\n                            via-transparent to-transparent opacity-0\n                            transition-opacity duration-500\n                            group-hover:opacity-100\n                          `}\n                        ></div>\n                        <div\n                          className={`\n                            absolute right-4 bottom-4 left-4 translate-y-4\n                            transform opacity-0 transition-all duration-500\n                            group-hover:translate-y-0 group-hover:opacity-100\n                          `}\n                        >\n                          <h3\n                            className={`\n                              text-lg font-bold text-white drop-shadow-lg\n                            `}\n                          >\n                            {image.title}\n                          </h3>\n                        </div>\n                      </div>\n                      <BorderBeam size={200} duration={15 + index * 3} delay={index * 2} />\n                    </div>\n                  </div>\n                ))}\n              </div>\n              <BorderBeam size={350} duration={25} delay={8} />\n            </div>\n          </div>\n\n          {/* CTA Section */}\n          <div className='relative'>\n            <div\n              className={`\n                absolute -inset-1 rounded-3xl bg-gradient-to-r from-[#6b8362]\n                to-amber-600 opacity-20 blur\n              `}\n            ></div>\n            <div\n              className={`\n                relative overflow-hidden rounded-3xl border border-amber-100/50\n                bg-gradient-to-br from-white/95 to-amber-50/90 p-12 text-center\n                shadow-2xl backdrop-blur-md\n              `}\n            >\n              {/* Background Pattern */}\n              <GridPattern\n                width={40}\n                height={40}\n                x={-1}\n                y={-1}\n                className={`\n                  absolute inset-0 h-full w-full stroke-[#6b8362]/5 opacity-20\n                  [mask-image:radial-gradient(ellipse_at_center,white,transparent_70%)]\n                `}\n              />\n\n              <div className='relative z-10'>\n                <div className='mb-6 inline-flex items-center space-x-3'>\n                  <Star className='h-10 w-10 animate-pulse text-amber-500' />\n                  <h2\n                    className={`\n                      ${robotoSlab.variable}\n                      font-roboto-slab text-3xl font-bold text-[#6b8362]\n                      md:text-4xl\n                      lg:text-5xl\n                    `}\n                  >\n                    Κλείστε τώρα την εκδρομή του σχολείου σας!\n                  </h2>\n                  <Star className='h-10 w-10 animate-pulse text-amber-500' />\n                </div>\n\n                <div\n                  className={`\n                    mx-auto mb-8 h-1 w-32 rounded-full bg-gradient-to-r\n                    from-[#6b8362] to-amber-600\n                  `}\n                ></div>\n\n                <p\n                  className={`\n                    mx-auto mb-12 max-w-4xl text-xl leading-relaxed\n                    text-gray-700\n                    md:text-2xl\n                  `}\n                >\n                  Επικοινωνήστε μαζί μας για περισσότερες πληροφορίες και για να οργανώσουμε μαζί την τέλεια εκδρομή για\n                  τους μαθητές σας.\n                </p>\n\n                <div\n                  className={`\n                    flex flex-col items-center justify-center gap-6\n                    sm:flex-row\n                  `}\n                >\n                  <PulsatingButton\n                    className={`\n                      group relative transform rounded-2xl bg-gradient-to-r\n                      from-[#6b8362] to-[#5a7354] px-8 py-4 font-bold text-white\n                      shadow-xl transition-all duration-300\n                      hover:scale-105 hover:from-[#5a7354] hover:to-[#4a6244]\n                    `}\n                    pulseColor='#6b8362'\n                  >\n                    <Link\n                      href='mailto:<EMAIL>'\n                      className={`\n                      flex items-center space-x-3\n                    `}\n                    >\n                      <span className='text-lg'>Επικοινωνήστε μαζί μας</span>\n                      <ArrowRight\n                        className={`\n                          h-5 w-5 transition-transform\n                          group-hover:translate-x-1\n                        `}\n                      />\n                    </Link>\n                  </PulsatingButton>\n\n                  <div className='flex items-center space-x-4 text-gray-600'>\n                    <div className='flex items-center space-x-2'>\n                      <Clock className='h-5 w-5 text-[#6b8362]' />\n                      <span>Άμεση απάντηση</span>\n                    </div>\n                    <div\n                      className={`\n                        hidden h-6 w-px bg-gray-300\n                        sm:block\n                      `}\n                    ></div>\n                    <div className='flex items-center space-x-2'>\n                      <CheckCircle className='h-5 w-5 text-green-500' />\n                      <span>Δωρεάν προσφορά</span>\n                    </div>\n                  </div>\n                </div>\n\n                {/* Contact Info */}\n                <div\n                  className={`\n                    mx-auto mt-12 max-w-2xl rounded-2xl border\n                    border-amber-100/50 bg-white/60 p-6 backdrop-blur-sm\n                  `}\n                >\n                  <div\n                    className={`\n                      grid grid-cols-1 gap-6 text-center\n                      md:grid-cols-2 md:text-left\n                    `}\n                  >\n                    <div\n                      className={`\n                        flex items-center justify-center space-x-3\n                        md:justify-start\n                      `}\n                    >\n                      <div\n                        className={`\n                          flex h-10 w-10 items-center justify-center\n                          rounded-full bg-[#6b8362]\n                        `}\n                      >\n                        <span className='font-bold text-white'>📧</span>\n                      </div>\n                      <div>\n                        <div className='font-semibold text-[#6b8362]'>Email</div>\n                        <div className='text-gray-700'><EMAIL></div>\n                      </div>\n                    </div>\n                    <div\n                      className={`\n                        flex items-center justify-center space-x-3\n                        md:justify-start\n                      `}\n                    >\n                      <div\n                        className={`\n                          flex h-10 w-10 items-center justify-center\n                          rounded-full bg-amber-600\n                        `}\n                      >\n                        <span className='font-bold text-white'>📞</span>\n                      </div>\n                      <div>\n                        <div className='font-semibold text-amber-700'>Τηλέφωνο</div>\n                        <div className='text-gray-700'>+30 698 661 7090</div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              <BorderBeam size={400} duration={20} delay={0} />\n            </div>\n          </div>\n        </div>\n      </main>\n    </>\n  )\n}\n", "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/app/[locale]/kayak-rafting/page.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/app/[locale]/kayaking/page.tsx", "messages": [{"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: prose", "line": 46, "column": 23, "nodeType": null, "endLine": 46, "endColumn": 28}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: prose-lg", "line": 46, "column": 29, "nodeType": null, "endLine": 46, "endColumn": 37}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: lead", "line": 47, "column": 23, "nodeType": null, "endLine": 47, "endColumn": 27}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: prose", "line": 76, "column": 23, "nodeType": null, "endLine": 76, "endColumn": 28}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: prose-lg", "line": 76, "column": 29, "nodeType": null, "endLine": 76, "endColumn": 37}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: lead", "line": 77, "column": 23, "nodeType": null, "endLine": 77, "endColumn": 27}], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "import type { Metadata } from 'next'\nimport React from 'react'\n\nimport ActivityPageLayout from '@/components/ActivityPageLayout'\nimport DynamicBokunWidget from '@/components/DynamicBokunWidget'\n\ninterface PageProps {\n  params: Promise<{ locale: string }>\n}\n\nexport async function generateMetadata({ params }: PageProps): Promise<Metadata> {\n  const { locale } = await params\n\n  const isGreek = locale === 'el'\n\n  return {\n    title: isGreek\n      ? 'Καγιάκ στον Αχέροντα - Pony Club | Εμπειρία Καγιάκ Γλυκή Θεσπρωτίας'\n      : 'Kayaking in Acheron River - Pony Club | Glyki Thesprotia Experience',\n    description: isGreek\n      ? 'Ζήστε μια αξέχαστη εμπειρία καγιάκ στον κρυστάλλινο ποταμό Αχέροντα. Ασφαλής καγιάκ για όλες τις ηλικίες με έμπειρους οδηγούς στη Γλυκή Θεσπρωτίας.'\n      : 'Experience unforgettable kayaking in the crystal-clear Acheron River. Safe kayaking adventures for all ages with experienced guides in Glyki, Thesprotia.',\n    keywords: isGreek\n      ? 'καγιάκ Αχέροντας, καγιάκ Γλυκή, καγιάκ Θεσπρωτία, καγιάκ Ήπειρος, εκδρομή καγιάκ, ποταμός Αχέροντας'\n      : 'kayaking Acheron, kayaking Glyki, kayaking Thesprotia, kayaking Epirus, Acheron river kayaking, Greece kayaking',\n  }\n}\n\nconst KayakingPage = async ({ params }: PageProps) => {\n  const { locale } = await params\n  const isGreek = locale === 'el'\n\n  // Define the experience ID\n  const bokunExperienceId = '1020669'\n\n  const seoContent = isGreek ? (\n    <div className='mb-8'>\n      <h1\n        className={`\n          mb-6 text-3xl font-bold text-[#3E5A35]\n          md:text-4xl\n        `}\n      >\n        Καγιάκ στον Ποταμό Αχέροντα\n      </h1>\n      <div className='prose prose-lg max-w-none text-gray-700'>\n        <p className='lead mb-4'>\n          Ανακαλύψτε τη μαγεία του ποταμού Αχέροντα με μια συναρπαστική εμπειρία καγιάκ στα κρυστάλλινα νερά της Γλυκής,\n          Θεσπρωτίας. Το Pony Club προσφέρει ασφαλείς και οργανωμένες εκδρομές καγιάκ για όλες τις ηλικίες και επίπεδα\n          εμπειρίας.\n        </p>\n\n        <p>\n          Ο ποταμός Αχέροντας, γνωστός από την αρχαία ελληνική μυθολογία, είναι ένας από τους πιο εντυπωσιακούς\n          προορισμούς για καγιάκ στην Ελλάδα. Τα διαυγή νερά, η πλούσια βλάστηση και η μοναδική φυσική ομορφιά\n          δημιουργούν το ιδανικό περιβάλλον για μια αξέχαστη περιπέτεια στη φύση.\n        </p>\n\n        <p>\n          Με τον έμπειρο εξοπλισμό και τους καταρτισμένους οδηγούς μας, θα απολαύσετε μια ασφαλή και διασκεδαστική\n          εμπειρία καγιάκ διάρκειας 30 λεπτών. Κατάλληλο για αρχάριους και παιδιά, με πλήρη εξοπλισμό ασφαλείας και\n          λεπτομερείς οδηγίες.\n        </p>\n      </div>\n    </div>\n  ) : (\n    <div className='mb-8'>\n      <h1\n        className={`\n          mb-6 text-3xl font-bold text-[#3E5A35]\n          md:text-4xl\n        `}\n      >\n        Kayaking in Acheron River\n      </h1>\n      <div className='prose prose-lg max-w-none text-gray-700'>\n        <p className='lead mb-4'>\n          Discover the magic of Acheron River with an exciting kayaking experience in the crystal-clear waters of Glyki,\n          Thesprotia. Pony Club offers safe and organized kayaking excursions for all ages and experience levels.\n        </p>\n\n        <p>\n          The Acheron River, known from ancient Greek mythology, is one of the most impressive kayaking destinations in\n          Greece. The crystal-clear waters, rich vegetation, and unique natural beauty create the perfect environment\n          for an unforgettable adventure in nature.\n        </p>\n\n        <p>\n          With our professional equipment and trained guides, you'll enjoy a safe and fun 30-minute kayaking experience.\n          Suitable for beginners and children, with complete safety equipment and detailed instructions provided.\n        </p>\n      </div>\n    </div>\n  )\n\n  return (\n    <ActivityPageLayout\n      title='Kayaking'\n      subtitle=''\n      descriptionTitle=''\n      descriptionContent={\n        <>\n          {seoContent}\n          <DynamicBokunWidget experienceId={bokunExperienceId} />\n        </>\n      }\n      detailsTitle=''\n      detailsContent={<></>}\n      pricingTitle=''\n      pricingContent={<></>}\n      showBookingButton={false}\n      fullWidthContent={true}\n    />\n  )\n}\n\nexport default KayakingPage\n", "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/app/[locale]/layout.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/app/[locale]/not-found.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/app/[locale]/page.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/app/[locale]/rafting/page.tsx", "messages": [{"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: prose", "line": 46, "column": 23, "nodeType": null, "endLine": 46, "endColumn": 28}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: prose-lg", "line": 46, "column": 29, "nodeType": null, "endLine": 46, "endColumn": 37}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: lead", "line": 47, "column": 23, "nodeType": null, "endLine": 47, "endColumn": 27}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: prose", "line": 74, "column": 23, "nodeType": null, "endLine": 74, "endColumn": 28}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: prose-lg", "line": 74, "column": 29, "nodeType": null, "endLine": 74, "endColumn": 37}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: lead", "line": 75, "column": 23, "nodeType": null, "endLine": 75, "endColumn": 27}], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "import type { Metadata } from 'next'\nimport React from 'react'\n\nimport ActivityPageLayout from '@/components/ActivityPageLayout'\nimport DynamicBokunWidget from '@/components/DynamicBokunWidget'\n\ninterface PageProps {\n  params: Promise<{ locale: string }>\n}\n\nexport async function generateMetadata({ params }: PageProps): Promise<Metadata> {\n  const { locale } = await params\n\n  const isGreek = locale === 'el'\n\n  return {\n    title: isGreek\n      ? 'Ράφτινγκ στον Αχέροντα - Pony Club | Ράφτινγκ Γλυκή Θεσπρωτίας'\n      : 'Rafting in Acheron River - Pony Club | Glyki Thesprotia Adventure',\n    description: isGreek\n      ? 'Ζήστε συναρπαστικό ράφτινγκ στον ποταμό Αχέροντα με ασφάλεια και διασκέδαση. Κατάλληλο για όλη την οικογένεια στη Γλυκή Θεσπρωτίας.'\n      : 'Experience thrilling rafting in Acheron River with safety and fun. Perfect for the whole family in Glyki, Thesprotia.',\n    keywords: isGreek\n      ? 'ράφτινγκ Αχέροντας, ράφτινγκ Γλυκή, ράφτινγκ Θεσπρωτία, ράφτινγκ Ήπειρος, ποταμός Αχέροντας'\n      : 'rafting Acheron, rafting Glyki, rafting Thesprotia, rafting Epirus, Acheron river rafting, Greece rafting',\n  }\n}\n\nconst RaftingPage = async ({ params }: PageProps) => {\n  const { locale } = await params\n  const isGreek = locale === 'el'\n\n  // Define the experience ID for rafting\n  const bokunExperienceId = '1020611'\n\n  const seoContent = isGreek ? (\n    <div className='mb-8'>\n      <h1\n        className={`\n          mb-6 text-3xl font-bold text-[#3E5A35]\n          md:text-4xl\n        `}\n      >\n        Ράφτινγκ στον Ποταμό Αχέροντα\n      </h1>\n      <div className='prose prose-lg max-w-none text-gray-700'>\n        <p className='lead mb-4'>\n          Ζήστε την απόλυτη περιπέτεια με ράφτινγκ στον μυθικό ποταμό Αχέροντα! Το Pony Club σας προσφέρει μια ασφαλή\n          και συναρπαστική εμπειρία ράφτινγκ στα καθαρά νερά της Γλυκής, Θεσπρωτίας.\n        </p>\n\n        <p>\n          Ο Αχέροντας είναι ένας από τους πιο φημισμένους ποταμούς της Ελλάδας για ράφτινγκ, με ήπια ρεύματα που τον\n          καθιστούν ιδανικό για αρχάριους και οικογένειες. Η διαδρομή διάρκειας 30 λεπτών προσφέρει μοναδικές θέες σε\n          ένα καταπράσινο φυσικό περιβάλλον.\n        </p>\n\n        <p>\n          Οι έμπειροι οδηγοί μας εξασφαλίζουν την ασφάλειά σας και σας καθοδηγούν σε κάθε βήμα, ενώ σας παρέχουν όλο τον\n          απαραίτητο εξοπλισμό ασφαλείας. Κατάλληλο για άτομα άνω των 6 ετών και όλα τα επίπεδα εμπειρίας.\n        </p>\n      </div>\n    </div>\n  ) : (\n    <div className='mb-8'>\n      <h1\n        className={`\n          mb-6 text-3xl font-bold text-[#3E5A35]\n          md:text-4xl\n        `}\n      >\n        Rafting in Acheron River\n      </h1>\n      <div className='prose prose-lg max-w-none text-gray-700'>\n        <p className='lead mb-4'>\n          Experience the ultimate adventure with rafting in the mythical Acheron River! Pony Club offers you a safe and\n          thrilling rafting experience in the clear waters of Glyki, Thesprotia.\n        </p>\n\n        <p>\n          Acheron is one of Greece's most renowned rivers for rafting, with gentle currents that make it perfect for\n          beginners and families. The 30-minute journey offers unique views in a lush natural environment.\n        </p>\n\n        <p>\n          Our experienced guides ensure your safety and guide you every step of the way, while providing all necessary\n          safety equipment. Suitable for people over 6 years old and all experience levels.\n        </p>\n      </div>\n    </div>\n  )\n\n  return (\n    <ActivityPageLayout\n      title='Rafting'\n      subtitle=''\n      descriptionTitle=''\n      descriptionContent={\n        <>\n          {seoContent}\n          <DynamicBokunWidget experienceId={bokunExperienceId} />\n        </>\n      }\n      detailsTitle=''\n      detailsContent={<></>}\n      pricingTitle=''\n      pricingContent={<></>}\n      showBookingButton={false}\n      fullWidthContent={true}\n    />\n  )\n}\n\nexport default RaftingPage\n", "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/app/[locale]/riding/page.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/app/[locale]/river-village/page.tsx", "messages": [{"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: prose", "line": 77, "column": 20, "nodeType": null, "endLine": 77, "endColumn": 25}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: prose", "line": 140, "column": 23, "nodeType": null, "endLine": 140, "endColumn": 28}], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "'use client'\n\nimport { Roboto_Slab } from 'next/font/google'\n\nimport ActivityPageLayout from '@/components/ActivityPageLayout'\nimport { OptimizedImage } from '@/components/ui/OptimizedImage'\nimport { useLanguage } from '@/contexts/language-context'\n\n// Define Roboto Slab font instance\nconst robotoSlab = Roboto_Slab({\n  subsets: ['latin', 'greek'],\n  variable: '--font-roboto-slab',\n  weight: ['400', '700', '900'],\n})\n\nexport default function RiverVillagePage() {\n  const { t, language } = useLanguage()\n\n  // Enhanced description content with glassmorphism effect\n  const descriptionContent = (\n    <div\n      className={`\n        group relative transform overflow-hidden rounded-2xl p-6 shadow-xl\n        transition-all duration-500\n        hover:scale-[1.01]\n      `}\n    >\n      {/* Background layers */}\n      <div\n        className={`\n          absolute inset-0 -z-10 rounded-2xl bg-linear-to-br from-[#f5f0e8]/95\n          via-white/90 to-[#f5f0e8]/95 backdrop-blur-md\n        `}\n      ></div>\n      <div className='absolute inset-0 -z-20 rounded-2xl bg-[#6b8362]/5'></div>\n\n      {/* Decorative effects */}\n      <div\n        className={`\n          pointer-events-none absolute -inset-[3px] -z-10 rounded-2xl opacity-70\n        `}\n      >\n        <div\n          className={`\n            absolute inset-0 rounded-2xl bg-linear-to-tr from-amber-200/30\n            via-transparent to-[#6b8362]/20\n          `}\n        ></div>\n      </div>\n      <div\n        className={`\n          pointer-events-none absolute inset-0 rounded-2xl border\n          border-amber-200/30\n        `}\n      ></div>\n\n      {/* Title with underline effect */}\n      <h2\n        className={`\n          ${robotoSlab.variable}\n          relative mb-6 inline-block font-roboto-slab text-3xl font-bold\n          text-[#3E5A35]\n          md:text-4xl\n        `}\n      >\n        {t.riverVillage.descriptionTitle}\n        <div\n          className={`\n            absolute -bottom-2 left-0 h-[2px] w-full bg-linear-to-r\n            from-transparent via-[#6b8362]/70 to-transparent\n          `}\n        ></div>\n      </h2>\n\n      {/* Content */}\n      <div\n        className='prose max-w-none text-gray-700'\n        dangerouslySetInnerHTML={{ __html: t.riverVillage.descriptionContent }}\n      />\n    </div>\n  )\n\n  // Enhanced details content with Natura 2000 logo\n  const detailsContent = (\n    <div\n      className={`\n        group relative transform overflow-hidden rounded-2xl p-6 shadow-xl\n        transition-all duration-500\n        hover:scale-[1.01]\n      `}\n    >\n      {/* Background layers */}\n      <div\n        className={`\n          absolute inset-0 -z-10 rounded-2xl bg-linear-to-br from-[#f5f0e8]/95\n          via-white/90 to-[#f5f0e8]/95 backdrop-blur-md\n        `}\n      ></div>\n      <div className='absolute inset-0 -z-20 rounded-2xl bg-[#6b8362]/5'></div>\n\n      {/* Decorative effects */}\n      <div\n        className={`\n          pointer-events-none absolute -inset-[3px] -z-10 rounded-2xl opacity-70\n        `}\n      >\n        <div\n          className={`\n            absolute inset-0 rounded-2xl bg-linear-to-tr from-amber-200/30\n            via-transparent to-[#6b8362]/20\n          `}\n        ></div>\n      </div>\n      <div\n        className={`\n          pointer-events-none absolute inset-0 rounded-2xl border\n          border-amber-200/30\n        `}\n      ></div>\n\n      {/* Title with underline effect */}\n      <h2\n        className={`\n          ${robotoSlab.variable}\n          relative mb-6 inline-block font-roboto-slab text-3xl font-bold\n          text-[#3E5A35]\n          md:text-4xl\n        `}\n      >\n        {t.riverVillage.detailsTitle}\n        <div\n          className={`\n            absolute -bottom-2 left-0 h-[2px] w-full bg-linear-to-r\n            from-transparent via-[#6b8362]/70 to-transparent\n          `}\n        ></div>\n      </h2>\n\n      {/* Content */}\n      <div className='prose max-w-none text-gray-700'>\n        <div dangerouslySetInnerHTML={{ __html: t.riverVillage.detailsContent }} />\n\n        {/* Natura 2000 logo with enhanced styling */}\n        <div className='mt-8 flex flex-col items-center'>\n          <p className='mb-3 text-center text-sm text-gray-600 italic'>\n            {language === 'en'\n              ? \"Protected by EU's Natura 2000 program\"\n              : 'Προστατεύεται από το πρόγραμμα Natura 2000 της ΕΕ'}\n          </p>\n          <div\n            className={`\n              relative h-32 w-48 rounded-xl border border-amber-100/70\n              bg-white/80 p-3 shadow-lg transition-shadow duration-300\n              hover:shadow-xl\n            `}\n          >\n            <OptimizedImage\n              src='/images/natura_2000.png'\n              alt='Natura 2000 Logo'\n              fill\n              sizes='192px'\n              className='object-contain'\n              imageType='logo'\n            />\n            <div\n              className={`\n                absolute -inset-[1px] -z-10 rounded-xl bg-linear-to-tr\n                from-amber-200/20 via-white/50 to-[#6b8362]/20 blur-xs\n              `}\n            ></div>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n\n  return (\n    <ActivityPageLayout\n      title={t.riverVillage.pageTitle}\n      subtitle={t.riverVillage.pageSubtitle}\n      descriptionTitle=''\n      descriptionContent={descriptionContent}\n      detailsTitle={t.riverVillage.detailsTitle}\n      detailsContent={detailsContent}\n      pricingTitle=''\n      pricingContent={null}\n      useSingleColumn={true}\n    />\n  )\n}\n", "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/app/[locale]/test-footer/page.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/app/[locale]/trekking/page.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/app/api/csp-violations/route.ts", "messages": [{"ruleId": "no-console", "severity": 1, "message": "Unexpected console statement.", "line": 8, "column": 7, "nodeType": "MemberExpression", "messageId": "unexpected", "endLine": 8, "endColumn": 19, "suggestions": [{"fix": {"range": [236, 274], "text": ""}, "messageId": "removeConsole", "data": {"propertyName": "warn"}, "desc": "Remove the console.warn()."}]}, {"ruleId": "no-console", "severity": 1, "message": "Unexpected console statement.", "line": 16, "column": 7, "nodeType": "MemberExpression", "messageId": "unexpected", "endLine": 16, "endColumn": 20, "suggestions": [{"fix": {"range": [614, 666], "text": ""}, "messageId": "removeConsole", "data": {"propertyName": "error"}, "desc": "Remove the console.error()."}]}], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "import { NextResponse } from 'next/server'\n\nexport async function POST(request: Request) {\n  try {\n    const report = await request.json()\n    // Log CSP violations in development\n    if (process.env.NODE_ENV === 'development') {\n      console.warn('CSP Violation:', report)\n    }\n    // In a real application, you would send this report to a logging service\n    // like Sentry, Report URI, or a custom analytics platform.\n    return NextResponse.json({ message: 'CSP report received' }, { status: 200 })\n  } catch (error) {\n    // Log errors in development\n    if (process.env.NODE_ENV === 'development') {\n      console.error('Error processing CSP report:', error)\n    }\n    return NextResponse.json({ message: 'Error processing report' }, { status: 400 })\n  }\n}\n", "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/app/global-error.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/app/sitemap.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ActivityPageLayout.tsx", "messages": [{"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: prose", "line": 67, "column": 19, "nodeType": null, "endLine": 67, "endColumn": 24}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: prose", "line": 109, "column": 19, "nodeType": null, "endLine": 109, "endColumn": 24}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: prose", "line": 151, "column": 19, "nodeType": null, "endLine": 151, "endColumn": 24}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: prose", "line": 178, "column": 19, "nodeType": null, "endLine": 178, "endColumn": 24}], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "import { Roboto_Slab } from 'next/font/google'\n\n// ResponsiveNavigation and Link/OptimizedImage for logo are no longer needed here directly.\n// SiteHeader is now part of PageLayout.\nimport { Container } from './ui/Container' // Import the Container component\n\n// Define Roboto Slab font instance\nconst robotoSlab = Roboto_Slab({\n  subsets: ['latin', 'greek'],\n  variable: '--font-roboto-slab',\n  weight: ['400', '700', '900'],\n})\n\ninterface ActivityPageLayoutProps {\n  title: string\n  subtitle: string\n  descriptionTitle: string\n  descriptionContent: React.ReactNode\n  detailsTitle: string\n  detailsContent: React.ReactNode\n  pricingTitle: string\n  pricingContent: React.ReactNode\n  showBookingButton?: boolean\n  useSingleColumn?: boolean\n  fullWidthContent?: boolean\n}\n\nexport default function ActivityPageLayout({\n  title: _title,\n  subtitle: _subtitle,\n  descriptionTitle: _descriptionTitle,\n  descriptionContent,\n  detailsTitle,\n  detailsContent,\n  pricingTitle,\n  pricingContent,\n  showBookingButton: _showBookingButton = true,\n  useSingleColumn = false,\n  fullWidthContent: _fullWidthContent = false,\n}: ActivityPageLayoutProps) {\n  // Determine content max-width class based on column preference\n  const contentMaxWidthClass = useSingleColumn ? 'max-w-none' : 'max-w-none'\n\n  return (\n    <>\n      {/*\n        SiteHeader is now rendered by PageLayout.\n        The main PageLayout's <main> tag has pt-20 and the background color.\n        This component's content will be rendered inside that <main> tag.\n        The containerClasses will define the layout for the content of this specific activity page.\n        Adjusting pt-24 to pt-4 in containerClasses to provide a small top padding for the content block itself,\n        relative to the PageLayout's main content area.\n      */}\n      <Container className='py-6'>\n        <div className='flex flex-col gap-8'>\n          {/* Description Section */}\n          {descriptionContent && (\n            <div\n              className={`\n                relative rounded-lg border border-amber-100/70 bg-white/80 p-6\n                shadow-lg backdrop-blur-sm transition-shadow duration-300\n                hover:shadow-xl\n              `}\n            >\n              <div\n                className={`\n                  prose\n                  ${contentMaxWidthClass}\n                  text-gray-700\n                `}\n              >\n                {descriptionContent}\n              </div>\n              <div\n                className={`\n                  absolute -inset-[1px] -z-10 rounded-lg bg-gradient-to-tr\n                  from-amber-200/20 via-white/50 to-[#6b8362]/20 blur-sm\n                `}\n              ></div>\n            </div>\n          )}\n\n          {/* Details Section - Always inside container */}\n          {detailsTitle && detailsContent && (\n            <div\n              className={`\n                relative rounded-lg border border-amber-100/70 bg-white/80 p-6\n                shadow-lg backdrop-blur-sm transition-shadow duration-300\n                hover:shadow-xl\n              `}\n            >\n              <h2\n                className={`\n                  ${robotoSlab.variable}\n                  relative mb-4 inline-block font-roboto-slab text-2xl font-bold\n                  text-amber-800\n                `}\n              >\n                {detailsTitle}\n                <div\n                  className={`\n                    absolute -bottom-1 left-0 h-[2px] w-full bg-gradient-to-r\n                    from-transparent via-amber-500/50 to-transparent\n                  `}\n                ></div>\n              </h2>\n              <div\n                className={`\n                  prose\n                  ${contentMaxWidthClass}\n                  text-gray-700\n                `}\n              >\n                {detailsContent}\n              </div>\n              <div\n                className={`\n                  absolute -inset-[1px] -z-10 rounded-lg bg-gradient-to-tr\n                  from-amber-200/20 via-white/50 to-[#6b8362]/20 blur-sm\n                `}\n              ></div>\n            </div>\n          )}\n\n          {/* Pricing Section */}\n          {pricingTitle && pricingContent && (\n            <div\n              className={`\n                relative rounded-lg border border-amber-100/70 bg-white/80 p-6\n                shadow-lg backdrop-blur-sm transition-shadow duration-300\n                hover:shadow-xl\n              `}\n            >\n              <h2\n                className={`\n                  ${robotoSlab.variable}\n                  relative mb-4 inline-block font-roboto-slab text-2xl font-bold\n                  text-amber-800\n                `}\n              >\n                {pricingTitle}\n                <div\n                  className={`\n                    absolute -bottom-1 left-0 h-[2px] w-full bg-gradient-to-r\n                    from-transparent via-amber-500/50 to-transparent\n                  `}\n                ></div>\n              </h2>\n              <div\n                className={`\n                  prose\n                  ${contentMaxWidthClass}\n                  text-gray-700\n                `}\n              >\n                {pricingContent}\n              </div>\n              <div\n                className={`\n                  absolute -inset-[1px] -z-10 rounded-lg bg-gradient-to-tr\n                  from-amber-200/20 via-white/50 to-[#6b8362]/20 blur-sm\n                `}\n              ></div>\n            </div>\n          )}\n\n          {/* If pricingTitle is not provided but pricingContent is, show just the content */}\n          {!pricingTitle && pricingContent && (\n            <div\n              className={`\n                relative rounded-lg border border-amber-100/70 bg-white/80 p-6\n                shadow-lg backdrop-blur-sm transition-shadow duration-300\n                hover:shadow-xl\n              `}\n            >\n              <div\n                className={`\n                  prose\n                  ${contentMaxWidthClass}\n                  text-gray-700\n                `}\n              >\n                {pricingContent}\n              </div>\n              <div\n                className={`\n                  absolute -inset-[1px] -z-10 rounded-lg bg-gradient-to-tr\n                  from-amber-200/20 via-white/50 to-[#6b8362]/20 blur-sm\n                `}\n              ></div>\n            </div>\n          )}\n        </div>\n      </Container>\n    </>\n  )\n}\n", "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/BokunWidget.tsx", "messages": [{"ruleId": "no-console", "severity": 1, "message": "Unexpected console statement.", "line": 40, "column": 9, "nodeType": "MemberExpression", "messageId": "unexpected", "endLine": 40, "endColumn": 20, "suggestions": [{"fix": {"range": [1195, 1256], "text": ""}, "messageId": "removeConsole", "data": {"propertyName": "log"}, "desc": "Remove the console.log()."}]}, {"ruleId": "no-console", "severity": 1, "message": "Unexpected console statement.", "line": 46, "column": 7, "nodeType": "MemberExpression", "messageId": "unexpected", "endLine": 46, "endColumn": 18, "suggestions": [{"fix": {"range": [1341, 1435], "text": ""}, "messageId": "removeConsole", "data": {"propertyName": "log"}, "desc": "Remove the console.log()."}]}, {"ruleId": "no-console", "severity": 1, "message": "Unexpected console statement.", "line": 59, "column": 11, "nodeType": "MemberExpression", "messageId": "unexpected", "endLine": 59, "endColumn": 22, "suggestions": [{"fix": {"range": [1936, 2035], "text": ""}, "messageId": "removeConsole", "data": {"propertyName": "log"}, "desc": "Remove the console.log()."}]}, {"ruleId": "no-console", "severity": 1, "message": "Unexpected console statement.", "line": 65, "column": 11, "nodeType": "MemberExpression", "messageId": "unexpected", "endLine": 65, "endColumn": 22, "suggestions": [{"fix": {"range": [2234, 2305], "text": ""}, "messageId": "removeConsole", "data": {"propertyName": "log"}, "desc": "Remove the console.log()."}]}, {"ruleId": "no-console", "severity": 1, "message": "Unexpected console statement.", "line": 70, "column": 11, "nodeType": "MemberExpression", "messageId": "unexpected", "endLine": 70, "endColumn": 22, "suggestions": [{"fix": {"range": [2438, 2523], "text": ""}, "messageId": "removeConsole", "data": {"propertyName": "log"}, "desc": "Remove the console.log()."}]}, {"ruleId": "no-console", "severity": 1, "message": "Unexpected console statement.", "line": 92, "column": 9, "nodeType": "MemberExpression", "messageId": "unexpected", "endLine": 92, "endColumn": 20, "suggestions": [{"fix": {"range": [3350, 3537], "text": ""}, "messageId": "removeConsole", "data": {"propertyName": "log"}, "desc": "Remove the console.log()."}]}, {"ruleId": "no-console", "severity": 1, "message": "Unexpected console statement.", "line": 112, "column": 11, "nodeType": "MemberExpression", "messageId": "unexpected", "endLine": 112, "endColumn": 22, "suggestions": [{"fix": {"range": [4206, 4311], "text": ""}, "messageId": "removeConsole", "data": {"propertyName": "log"}, "desc": "Remove the console.log()."}]}, {"ruleId": "no-console", "severity": 1, "message": "Unexpected console statement.", "line": 118, "column": 13, "nodeType": "MemberExpression", "messageId": "unexpected", "endLine": 118, "endColumn": 24, "suggestions": [{"fix": {"range": [4536, 4620], "text": ""}, "messageId": "removeConsole", "data": {"propertyName": "log"}, "desc": "Remove the console.log()."}]}, {"ruleId": "no-console", "severity": 1, "message": "Unexpected console statement.", "line": 122, "column": 13, "nodeType": "MemberExpression", "messageId": "unexpected", "endLine": 122, "endColumn": 26, "suggestions": [{"fix": {"range": [4727, 4827], "text": ""}, "messageId": "removeConsole", "data": {"propertyName": "error"}, "desc": "Remove the console.error()."}]}, {"ruleId": "no-console", "severity": 1, "message": "Unexpected console statement.", "line": 128, "column": 11, "nodeType": "MemberExpression", "messageId": "unexpected", "endLine": 128, "endColumn": 22, "suggestions": [{"fix": {"range": [5082, 5165], "text": ""}, "messageId": "removeConsole", "data": {"propertyName": "log"}, "desc": "Remove the console.log()."}]}, {"ruleId": "no-console", "severity": 1, "message": "Unexpected console statement.", "line": 132, "column": 9, "nodeType": "MemberExpression", "messageId": "unexpected", "endLine": 132, "endColumn": 20, "suggestions": [{"fix": {"range": [5281, 5378], "text": ""}, "messageId": "removeConsole", "data": {"propertyName": "log"}, "desc": "Remove the console.log()."}]}, {"ruleId": "no-console", "severity": 1, "message": "Unexpected console statement.", "line": 137, "column": 7, "nodeType": "MemberExpression", "messageId": "unexpected", "endLine": 137, "endColumn": 18, "suggestions": [{"fix": {"range": [5451, 5548], "text": ""}, "messageId": "removeConsole", "data": {"propertyName": "log"}, "desc": "Remove the console.log()."}]}, {"ruleId": "no-console", "severity": 1, "message": "Unexpected console statement.", "line": 147, "column": 11, "nodeType": "MemberExpression", "messageId": "unexpected", "endLine": 147, "endColumn": 22, "suggestions": [{"fix": {"range": [6113, 6216], "text": ""}, "messageId": "removeConsole", "data": {"propertyName": "log"}, "desc": "Remove the console.log()."}]}, {"ruleId": "no-console", "severity": 1, "message": "Unexpected console statement.", "line": 153, "column": 13, "nodeType": "MemberExpression", "messageId": "unexpected", "endLine": 153, "endColumn": 24, "suggestions": [{"fix": {"range": [6431, 6510], "text": ""}, "messageId": "removeConsole", "data": {"propertyName": "log"}, "desc": "Remove the console.log()."}]}, {"ruleId": "no-console", "severity": 1, "message": "Unexpected console statement.", "line": 159, "column": 13, "nodeType": "MemberExpression", "messageId": "unexpected", "endLine": 159, "endColumn": 26, "suggestions": [{"fix": {"range": [6739, 6834], "text": ""}, "messageId": "removeConsole", "data": {"propertyName": "error"}, "desc": "Remove the console.error()."}]}, {"ruleId": "no-console", "severity": 1, "message": "Unexpected console statement.", "line": 167, "column": 7, "nodeType": "MemberExpression", "messageId": "unexpected", "endLine": 167, "endColumn": 18, "suggestions": [{"fix": {"range": [6998, 7094], "text": ""}, "messageId": "removeConsole", "data": {"propertyName": "log"}, "desc": "Remove the console.log()."}]}, {"ruleId": "no-console", "severity": 1, "message": "Unexpected console statement.", "line": 175, "column": 9, "nodeType": "MemberExpression", "messageId": "unexpected", "endLine": 175, "endColumn": 20, "suggestions": [{"fix": {"range": [7391, 7471], "text": ""}, "messageId": "removeConsole", "data": {"propertyName": "log"}, "desc": "Remove the console.log()."}]}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: bokunWidget", "line": 189, "column": 20, "nodeType": null, "endLine": 189, "endColumn": 31}], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 18, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "'use client'\n\nimport type { IFrameOptions } from 'iframe-resizer'\nimport { iframeResizer } from 'iframe-resizer' // Types for v3/v4\nimport { useEffect, useRef } from 'react'\n\nimport { useLanguage } from '@/contexts/language-context'\nimport { bokunLangMap } from '@/lib/bokun-lang'\n\n// Define proper types for iframe resizer callbacks\ninterface MessageData {\n  iframe: HTMLIFrameElement\n  message: unknown\n}\n\ninterface SizeData {\n  iframe: HTMLIFrameElement\n  height: number\n  width: number\n  type: string\n}\n\ntype BokunWidgetProps = {\n  experienceId: string\n  partialView?: number\n}\n\nexport default function BokunWidget({ experienceId, partialView = 1 }: BokunWidgetProps) {\n  const { language } = useLanguage()\n  const bokunLang = bokunLangMap[language] || 'en'\n  const widgetContainerRef = useRef<HTMLDivElement>(null)\n\n  const baseUrl = 'https://widgets.bokun.io/online-sales/c078b762-6f7f-474f-8edb-bdd1bdb7d12a/experience'\n  const bokunWidgetSrcUrl = `${baseUrl}/${experienceId}?partialView=${partialView}&lang=${bokunLang}`\n\n  useEffect(() => {\n    const containerElement = widgetContainerRef.current\n    if (!containerElement) {\n      if (process.env.NODE_ENV === 'development') {\n        console.log('[Bokun Widget Parent] Container ref not found.')\n      }\n      return\n    }\n\n    if (process.env.NODE_ENV === 'development') {\n      console.log('[Bokun Widget Parent] useEffect triggered. Container element:', containerElement)\n    }\n\n    const options: IFrameOptions & {\n      onMessage?: (messageData: MessageData) => void\n      onResized?: (sizeData: SizeData) => void\n      onInit?: (iFrameEl: HTMLIFrameElement) => void\n    } = {\n      log: process.env.NODE_ENV === 'development', // Enable logs only in dev\n      checkOrigin: false, // Be cautious in production\n      // Updated to use current callback names\n      onMessage: (messageData: MessageData) => {\n        if (process.env.NODE_ENV === 'development') {\n          console.log('[Bokun Widget Parent] Received message from iframe (onMessage):', messageData.message)\n        }\n        // Potentially handle specific messages if needed in the future\n      },\n      onResized: (sizeData: SizeData) => {\n        if (process.env.NODE_ENV === 'development') {\n          console.log('[Bokun Widget Parent] iframeResizer onResized:', sizeData)\n        }\n      },\n      onInit: (iFrameEl: HTMLIFrameElement) => {\n        if (process.env.NODE_ENV === 'development') {\n          console.log('[Bokun Widget Parent] iframeResizer onInit: iframe is ready.', iFrameEl)\n        }\n      },\n    }\n\n    // Ensure window.iFrameResizer exists and has onMessage, onResized, onInit\n    // This is a workaround for the \"onMessage function not defined\" warning\n    // if the Bokun widget's internal iframe-resizer script is looking for a global function.\n    interface WindowWithIFrameResizer extends Window {\n      iFrameResizer?: {\n        onMessage?: (messageData: MessageData) => void\n        onResized?: (sizeData: SizeData) => void\n        onInit?: (iFrameEl: HTMLIFrameElement) => void\n      }\n    }\n\n    const windowWithResizer = window as WindowWithIFrameResizer\n    if (!windowWithResizer.iFrameResizer) {\n      windowWithResizer.iFrameResizer = {}\n    }\n    windowWithResizer.iFrameResizer.onMessage = (messageData: MessageData) => {\n      if (process.env.NODE_ENV === 'development') {\n        console.log(\n          '[Bokun Widget Parent] window.iFrameResizer.onMessage received:',\n          messageData.message,\n          'from iframe:',\n          messageData.iframe.id\n        )\n      }\n      // Call the options.onMessage handler as well\n      if (options.onMessage) {\n        options.onMessage(messageData)\n      }\n    }\n    windowWithResizer.iFrameResizer.onResized = options.onResized\n    windowWithResizer.iFrameResizer.onInit = options.onInit\n\n    // 1. Initialize iframeResizer for the iframe embedded within this component (catalogue view)\n    const catalogueObserver = new MutationObserver((_mutationsList, observerInstance) => {\n      const catalogueIframe = containerElement.querySelector('iframe')\n      if (catalogueIframe && !catalogueIframe.dataset.resizerAttached) {\n        if (process.env.NODE_ENV === 'development') {\n          console.log('[Bokun Widget Parent] Found catalogue iframe, initializing iframeResizer:', catalogueIframe)\n        }\n        try {\n          iframeResizer(options, catalogueIframe)\n          catalogueIframe.dataset.resizerAttached = 'true' // Mark as initialized\n          if (process.env.NODE_ENV === 'development') {\n            console.log('[Bokun Widget Parent] iframeResizer initialized for catalogue iframe.')\n          }\n        } catch (error) {\n          if (process.env.NODE_ENV === 'development') {\n            console.error('[Bokun Widget Parent] Error initializing iframeResizer for catalogue iframe:', error)\n          }\n        }\n        observerInstance.disconnect() // Stop observing once the catalogue iframe is found and initialized\n      } else if (catalogueIframe?.dataset.resizerAttached) {\n        if (process.env.NODE_ENV === 'development') {\n          console.log('[Bokun Widget Parent] Catalogue iframe already has resizer attached.')\n        }\n        observerInstance.disconnect()\n      } else if (process.env.NODE_ENV === 'development') {\n        console.log('[Bokun Widget Parent] Catalogue iframe not found yet in MutationObserver callback.')\n      }\n    })\n\n    if (process.env.NODE_ENV === 'development') {\n      console.log('[Bokun Widget Parent] Starting MutationObserver on container for catalogue iframe.')\n    }\n    catalogueObserver.observe(containerElement, { childList: true, subtree: true })\n\n    // 2. Initialize iframeResizer for the cart iframe injected into the body by Bokun\n    const bodyObserver = new MutationObserver((_mutationsList, _observerInstance) => {\n      const cartIframe = document.getElementById('bokun-widgets-cart') as HTMLIFrameElement | null\n      // Check if the cart iframe exists and hasn't been initialized yet\n      if (cartIframe && !cartIframe.dataset.resizerAttached) {\n        if (process.env.NODE_ENV === 'development') {\n          console.log('[Bokun Widget Parent] Found cart iframe in body, initializing iframeResizer:', cartIframe)\n        }\n        try {\n          iframeResizer(options, cartIframe)\n          cartIframe.dataset.resizerAttached = 'true' // Mark as initialized\n          if (process.env.NODE_ENV === 'development') {\n            console.log('[Bokun Widget Parent] iframeResizer initialized for cart iframe.')\n          }\n          // Optional: Disconnect if you only expect one cart iframe instance\n          // observerInstance.disconnect();\n        } catch (error) {\n          if (process.env.NODE_ENV === 'development') {\n            console.error('[Bokun Widget Parent] Error initializing iframeResizer for cart iframe:', error)\n          }\n        }\n      }\n      // Cart iframe already has resizer attached - no action needed\n    })\n\n    if (process.env.NODE_ENV === 'development') {\n      console.log('[Bokun Widget Parent] Starting MutationObserver on document body for cart iframe.')\n    }\n    // Observe the body for additions/removals, including the cart iframe\n    bodyObserver.observe(document.body, { childList: true, subtree: false }) // Observe direct children of body\n\n    // Cleanup function\n    return () => {\n      if (process.env.NODE_ENV === 'development') {\n        console.log('[Bokun Widget Parent] useEffect cleanup: Disconnecting observers.')\n      }\n      catalogueObserver.disconnect()\n      bodyObserver.disconnect()\n      // Note: iframe-resizer v4 might need manual cleanup if iframes are removed dynamically.\n      // e.g., iframeResizer.iframeResizer.close(iframeElement)\n      // For now, relying on component unmount and observer disconnect.\n    }\n  }, [bokunWidgetSrcUrl]) // Re-run if the URL changes\n\n  return (\n    <>\n      <div\n        ref={widgetContainerRef}\n        className='bokunWidget'\n        data-src={bokunWidgetSrcUrl}\n        data-lang={bokunLang}\n        style={{ width: '100%', minHeight: '500px' }} // Ensure div has dimensions\n      >\n        {/* Bokun's script will inject the iframe here */}\n      </div>\n      <noscript>Please enable javascript in your browser to book</noscript>\n    </>\n  )\n}\n", "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ClientLayout.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/DynamicBokunWidget.tsx", "messages": [{"ruleId": "better-tailwindcss/multiline", "severity": 1, "message": "Unnecessary line wrapping. Expected\n\n`↵\n····h-96·w-full·animate-pulse·rounded-lg·bg-gray-200↵\n··`\n\nto be\n\n`h-96·w-full·animate-pulse·rounded-lg·bg-gray-200`", "line": 11, "column": 18, "nodeType": null, "endLine": 13, "endColumn": 4, "fix": {"range": [309, 367], "text": "`h-96 w-full animate-pulse rounded-lg bg-gray-200`"}}], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": "'use client'\n\nimport dynamic from 'next/dynamic'\nimport { useEffect, useRef, useState } from 'react'\n\n// Dynamically import BokunWidget with ssr: false and a loading placeholder\nconst BokunWidget = dynamic(() => import('@/components/BokunWidget'), {\n  ssr: false,\n  loading: () => (\n    <div\n      className={`\n    h-96 w-full animate-pulse rounded-lg bg-gray-200\n  `}\n    />\n  ),\n})\n\ntype DynamicBokunWidgetProps = {\n  experienceId: string\n  partialView?: number\n}\n\nexport default function DynamicBokunWidget({ experienceId, partialView }: DynamicBokunWidgetProps) {\n  const [shouldLoad, setShouldLoad] = useState(false)\n  const ref = useRef<HTMLDivElement>(null)\n\n  useEffect(() => {\n    const currentRef = ref.current\n    if (!currentRef) return\n\n    const observer = new IntersectionObserver(\n      ([entry]) => {\n        // Load when the placeholder is intersecting or nearly intersecting\n        if (entry.isIntersecting) {\n          setShouldLoad(true)\n          observer.disconnect() // Stop observing once loaded\n        }\n      },\n      {\n        rootMargin: '200px 0px', // Load when 200px away from viewport\n        threshold: 0.01, // Trigger even if only 1% is visible\n      }\n    )\n\n    observer.observe(currentRef)\n\n    // Cleanup observer on component unmount\n    return () => {\n      if (observer && currentRef) {\n        observer.unobserve(currentRef)\n      }\n      observer.disconnect()\n    }\n  }, []) // Empty dependency array ensures this runs only once on mount\n\n  return (\n    <div ref={ref} style={{ minHeight: '384px' }}>\n      {' '}\n      {/* Wrapper div for observer, with min-height matching placeholder */}\n      {shouldLoad ? (\n        <BokunWidget experienceId={experienceId} partialView={partialView} />\n      ) : (\n        // Render the loading placeholder defined in dynamic import\n        <div className='h-96 w-full animate-pulse rounded-lg bg-gray-200' />\n      )}\n    </div>\n  )\n}\n", "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/DynamicContactDetails.tsx", "messages": [{"ruleId": "better-tailwindcss/multiline", "severity": 1, "message": "Unnecessary line wrapping. Expected\n\n`↵\n····h-80·w-full·animate-pulse·rounded-lg·bg-gray-200↵\n··`\n\nto be\n\n`h-80·w-full·animate-pulse·rounded-lg·bg-gray-200`", "line": 11, "column": 18, "nodeType": null, "endLine": 13, "endColumn": 4, "fix": {"range": [292, 350], "text": "`h-80 w-full animate-pulse rounded-lg bg-gray-200`"}}], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": "'use client'\n\nimport dynamic from 'next/dynamic'\nimport React from 'react'\n\n// Dynamically import ContactDetailsEnhanced with ssr: false\nconst ContactDetailsEnhanced = dynamic(() => import('@/components/contact-details-enhanced'), {\n  ssr: false,\n  loading: () => (\n    <div\n      className={`\n    h-80 w-full animate-pulse rounded-lg bg-gray-200\n  `}\n    />\n  ),\n})\n\nexport default function DynamicContactDetails() {\n  return <ContactDetailsEnhanced />\n}\n", "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/DynamicGallery.tsx", "messages": [{"ruleId": "better-tailwindcss/multiline", "severity": 1, "message": "Unnecessary line wrapping. Expected\n\n`↵\n····h-96·w-full·animate-pulse·rounded-lg·bg-gray-200↵\n··`\n\nto be\n\n`h-96·w-full·animate-pulse·rounded-lg·bg-gray-200`", "line": 11, "column": 18, "nodeType": null, "endLine": 13, "endColumn": 4, "fix": {"range": [273, 331], "text": "`h-96 w-full animate-pulse rounded-lg bg-gray-200`"}}], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": "'use client'\n\nimport dynamic from 'next/dynamic'\nimport React from 'react'\n\n// Dynamically import Gallery with ssr: false\nconst Gallery = dynamic(() => import('@/components/ui/Gallery').then(mod => mod.Gallery), {\n  ssr: false,\n  loading: () => (\n    <div\n      className={`\n    h-96 w-full animate-pulse rounded-lg bg-gray-200\n  `}\n    />\n  ),\n})\n\ntype GalleryImage = {\n  src: string\n  alt: string\n}\n\ntype DynamicGalleryProps = {\n  images: GalleryImage[]\n  title: string\n  ariaLabel: string\n}\n\nexport default function DynamicGallery({ images, title, ariaLabel }: DynamicGalleryProps) {\n  return <Gallery images={images} title={title} ariaLabel={ariaLabel} />\n}\n", "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/DynamicGoogleMap.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/EnhancedPackageCard.tsx", "messages": [{"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: mosaic-grid", "line": 161, "column": 15, "nodeType": null, "endLine": 161, "endColumn": 26}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: mosaic-main", "line": 166, "column": 29, "nodeType": null, "endLine": 166, "endColumn": 40}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: mosaic-top", "line": 176, "column": 29, "nodeType": null, "endLine": 176, "endColumn": 39}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: mosaic-bottom", "line": 186, "column": 29, "nodeType": null, "endLine": 186, "endColumn": 42}], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "'use client'\n\nimport { <PERSON><PERSON>now, Sailboat, User, Waves } from 'lucide-react'\nimport { useEffect, useState } from 'react'\n\nimport BookingButton from '@/components/client/BookingButton'\nimport { OptimizedImage } from '@/components/ui/OptimizedImage'\n\ninterface PackageCardProps {\n  title: string\n  badge: string\n  activities: {\n    primary: string\n    riding: string\n    hiking: string\n  }\n  pricing: {\n    adults?: string\n    children?: string\n    perPerson?: string\n  }\n  images: {\n    main: string\n    top: string\n    bottom: string\n  }\n  bookingId: string\n  dataSrc: string\n  bookNowText: string\n  packageName: string\n  packagePrice: string\n  trackingLabel: string\n  variant: 'green' | 'amber'\n}\n\nexport default function EnhancedPackageCard({\n  title,\n  badge,\n  activities,\n  pricing,\n  images,\n  bookingId,\n  dataSrc,\n  bookNowText,\n  packageName,\n  packagePrice,\n  trackingLabel,\n  variant,\n}: PackageCardProps) {\n  const [nonce, setNonce] = useState('')\n\n  useEffect(() => {\n    // Get nonce from meta tag\n    const metaNonce = document.querySelector('meta[name=\"csp-nonce\"]')?.getAttribute('content') || ''\n    setNonce(metaNonce)\n  }, [])\n  const colorTheme = {\n    green: {\n      gradient: 'from-emerald-400 via-teal-500 to-cyan-600',\n      badge: 'bg-yellow-400 text-yellow-900',\n      glass: 'bg-white/10',\n      border: 'border-white/15',\n      icon: 'text-emerald-300',\n      priceBox: 'bg-white/20',\n      button: 'from-emerald-500 to-green-500 hover:from-emerald-600 hover:to-green-600',\n      text: 'text-emerald-50',\n    },\n    amber: {\n      gradient: 'from-amber-400 via-orange-500 to-red-500',\n      badge: 'bg-blue-400 text-blue-900',\n      glass: 'bg-white/10',\n      border: 'border-white/15',\n      icon: 'text-amber-300',\n      priceBox: 'bg-white/20',\n      button: 'from-amber-500 to-orange-500 hover:from-amber-600 hover:to-orange-600',\n      text: 'text-amber-50',\n    },\n  }\n\n  const theme = colorTheme[variant]\n\n  const renderActivityIcon = (index: number) => {\n    if (index === 0) {\n      return variant === 'green' ? (\n        <Waves\n          className={`\n            h-3 w-3 text-white\n            sm:h-4 sm:w-4\n            md:h-5 md:w-5\n          `}\n        />\n      ) : (\n        <Sailboat\n          className={`\n            h-3 w-3 text-white\n            sm:h-4 sm:w-4\n            md:h-5 md:w-5\n          `}\n        />\n      )\n    }\n    return (\n      <MountainSnow\n        className={`\n          h-3 w-3 text-white\n          sm:h-4 sm:w-4\n          md:h-5 md:w-5\n        `}\n      />\n    )\n  }\n\n  return (\n    <div\n      className={`\n        bg-gradient-to-tr\n        ${theme.gradient}\n        mx-auto w-full max-w-md rounded-3xl p-1 shadow-2xl\n      `}\n    >\n      <div\n        className={`\n          ${theme.glass}\n          border backdrop-blur-xl\n          ${theme.border}\n          flex h-full w-full flex-col overflow-hidden rounded-3xl shadow-2xl\n        `}\n      >\n        <div\n          className={`\n            flex h-full min-h-[580px] flex-col p-4\n            sm:min-h-[620px] sm:p-6\n            md:p-8\n          `}\n        >\n          {/* Header */}\n          <div className='mb-4 flex flex-shrink-0 items-start justify-between'>\n            <h2\n              className={`\n                text-2xl font-bold text-white\n                sm:text-3xl\n              `}\n            >\n              {title}\n            </h2>\n            <span\n              className={`\n                ${theme.badge}\n                rounded-full px-3 py-1.5 text-xs font-semibold whitespace-nowrap\n                shadow-md\n                sm:text-sm\n              `}\n            >\n              {badge}\n            </span>\n          </div>\n\n          {/* Image Mosaic */}\n          <div\n            className={`\n              mosaic-grid mb-4 h-44 flex-shrink-0 overflow-hidden rounded-xl\n              border border-white/20\n              sm:mb-6 sm:h-52\n            `}\n          >\n            <div className='mosaic-main relative'>\n              <OptimizedImage\n                src={images.main}\n                alt={`${title} main activity`}\n                fill\n                sizes='(max-width: 512px) 45vw, 240px'\n                className='h-full w-full object-cover'\n                imageType='default'\n              />\n            </div>\n            <div className='mosaic-top relative'>\n              <OptimizedImage\n                src={images.top}\n                alt={`${title} activity 2`}\n                fill\n                sizes='(max-width: 512px) 45vw, 240px'\n                className='h-full w-full object-cover'\n                imageType='default'\n              />\n            </div>\n            <div className='mosaic-bottom relative'>\n              <OptimizedImage\n                src={images.bottom}\n                alt={`${title} activity 3`}\n                fill\n                sizes='(max-width: 512px) 45vw, 240px'\n                className='h-full w-full object-cover'\n                imageType='default'\n              />\n            </div>\n          </div>\n\n          {/* Activities List */}\n          <ul\n            className={`\n              space-y-2\n              sm:space-y-3\n              ${theme.text}\n              mb-6 flex-grow\n              sm:mb-8\n            `}\n          >\n            {[activities.primary, activities.riding, activities.hiking].map((activity, index) => (\n              <li\n                key={index}\n                className={`\n                  flex items-center text-sm\n                  sm:text-base\n                `}\n              >\n                <span\n                  className={`\n                    inline-flex h-6 w-6 items-center justify-center\n                    sm:h-8 sm:w-8\n                    ${theme.icon}\n                    mr-2 flex-shrink-0 rounded-full bg-white/20\n                    sm:mr-3\n                  `}\n                >\n                  {renderActivityIcon(index)}\n                </span>\n                <span className='flex-1'>{activity}</span>\n              </li>\n            ))}\n          </ul>\n\n          {/* Pricing */}\n          <div\n            className={`\n              mb-6 flex-shrink-0 space-y-3\n              sm:mb-8 sm:space-y-4\n            `}\n          >\n            {pricing.adults && (\n              <div\n                className={`\n                  flex items-center justify-between\n                  ${theme.priceBox}\n                  rounded-xl border border-white/10 p-3 shadow-inner\n                  backdrop-blur-sm\n                  sm:p-4\n                `}\n              >\n                <p\n                  className={`\n                    text-base font-semibold text-white\n                    sm:text-lg\n                  `}\n                >\n                  Adults\n                </p>\n                <p\n                  className={`\n                    text-xl font-bold text-white\n                    sm:text-2xl\n                  `}\n                >\n                  {pricing.adults}\n                </p>\n              </div>\n            )}\n\n            {pricing.children && (\n              <div\n                className={`\n                  flex items-center justify-between\n                  ${theme.priceBox}\n                  rounded-xl border border-white/10 p-3 shadow-inner\n                  backdrop-blur-sm\n                  sm:p-4\n                `}\n              >\n                <div>\n                  <p\n                    className={`\n                      text-base font-semibold text-white\n                      sm:text-lg\n                    `}\n                  >\n                    Children\n                  </p>\n                  <p\n                    className={`\n                      text-xs text-white/80\n                      sm:text-sm\n                    `}\n                  >\n                    under 12 years old\n                  </p>\n                </div>\n                <p\n                  className={`\n                    text-xl font-bold text-white\n                    sm:text-2xl\n                  `}\n                >\n                  {pricing.children}\n                </p>\n              </div>\n            )}\n\n            {pricing.perPerson && (\n              <div\n                className={`\n                  flex items-center justify-between\n                  ${theme.priceBox}\n                  rounded-xl border border-white/10 p-3 shadow-inner\n                  backdrop-blur-sm\n                  sm:p-4\n                `}\n              >\n                <div className='flex items-center'>\n                  <User\n                    className={`\n                      mr-2 h-4 w-4 text-white\n                      sm:h-5 sm:w-5\n                    `}\n                  />\n                  <p\n                    className={`\n                      text-base font-semibold text-white\n                      sm:text-lg\n                    `}\n                  >\n                    Per Person\n                  </p>\n                </div>\n                <p\n                  className={`\n                    text-xl font-bold text-white\n                    sm:text-2xl\n                  `}\n                >\n                  {pricing.perPerson}\n                </p>\n              </div>\n            )}\n\n            {/* Add empty space for cards with less pricing info to maintain height */}\n            {!pricing.children && !pricing.perPerson && (\n              <div\n                className={`\n                  h-16\n                  sm:h-20\n                `}\n              ></div>\n            )}\n            {!pricing.adults && !pricing.children && !pricing.perPerson && (\n              <div\n                className={`\n                  h-32\n                  sm:h-40\n                `}\n              ></div>\n            )}\n          </div>\n\n          {/* Book Now Button */}\n          <div className='mt-auto flex-shrink-0'>\n            <BookingButton\n              id={bookingId}\n              dataSrc={dataSrc}\n              className={`\n                w-full bg-gradient-to-r\n                ${theme.button}\n                flex transform items-center justify-center rounded-xl px-4 py-3\n                text-base font-semibold text-white shadow-lg transition\n                duration-300 ease-in-out\n                hover:scale-105 hover:shadow-xl\n                sm:px-6 sm:py-3.5 sm:text-lg\n              `}\n              trackingLabel={trackingLabel}\n              packageName={packageName}\n              packagePrice={packagePrice}\n            >\n              {bookNowText}\n              <svg\n                className={`\n                  ml-2 h-4 w-4\n                  sm:h-5 sm:w-5\n                `}\n                fill='none'\n                stroke='currentColor'\n                viewBox='0 0 24 24'\n              >\n                <path strokeLinecap='round' strokeLinejoin='round' strokeWidth={2} d='M13 7l5 5m0 0l-5 5m5-5H6' />\n              </svg>\n            </BookingButton>\n          </div>\n        </div>\n      </div>\n\n      <style jsx nonce={nonce}>{`\n        .mosaic-grid {\n          display: grid;\n          grid-template-columns: repeat(2, 1fr);\n          grid-template-rows: repeat(2, 1fr);\n          gap: 6px;\n        }\n        .mosaic-main {\n          grid-column: 1 / 2;\n          grid-row: 1 / 3;\n        }\n        .mosaic-top {\n          grid-column: 2 / 3;\n          grid-row: 1 / 2;\n        }\n        .mosaic-bottom {\n          grid-column: 2 / 3;\n          grid-row: 2 / 3;\n        }\n      `}</style>\n    </div>\n  )\n}\n", "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/Footer.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/HomePageContent.tsx", "messages": [{"ruleId": "better-tailwindcss/multiline", "severity": 1, "message": "Incorrect line wrapping. Expected\n\n`absolute·inset-0·z-30·flex·items-center·justify-center`\n\nto be\n\n`↵\n············absolute·inset-0·z-30·flex·items-center·justify-center↵\n··········`", "line": 66, "column": 27, "nodeType": null, "endLine": 66, "endColumn": 83, "fix": {"range": [2066, 2122], "text": "`\n            absolute inset-0 z-30 flex items-center justify-center\n          `"}}], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": "'use client'\n\nimport { Roboto_Slab } from 'next/font/google'\n\nimport BokunStyles from '@/components/client/BokunStyles'\nimport GoogleReviews from '@/components/client/GoogleReviews'\nimport PriceListButton from '@/components/client/PriceListButton'\nimport DynamicContactDetails from '@/components/DynamicContactDetails'\nimport DynamicGoogleMap from '@/components/DynamicGoogleMap'\nimport EnhancedPackageCard from '@/components/EnhancedPackageCard'\nimport { OptimizedImage } from '@/components/ui/OptimizedImage'\nimport { useLanguage } from '@/contexts/language-context'\n\nconst robotoSlab = Roboto_Slab({\n  subsets: ['latin', 'greek'],\n  weight: ['400', '700'],\n  variable: '--font-roboto-slab',\n})\n\nexport default function HomePageContent() {\n  const { t } = useLanguage()\n\n  return (\n    <>\n      <main className='relative min-h-screen overflow-hidden'>\n        {/* Hero Section */}\n        <div\n          className={`\n            relative h-[60vh] w-full\n            md:h-[70vh]\n            lg:h-[80vh]\n          `}\n        >\n          <div\n            className={`\n              absolute inset-0 m-4 overflow-hidden rounded-2xl border\n              border-amber-200/30 shadow-xl\n            `}\n          >\n            {/* OptimizedImage for the poster */}\n            <OptimizedImage\n              src='/images/hero-image.webp'\n              alt='Hero background'\n              fill\n              priority\n              imageType='hero'\n              className='absolute inset-0 h-full w-full object-cover'\n            />\n            <video\n              src='/images/hero-video.mp4'\n              poster='/images/hero-image.webp'\n              autoPlay\n              muted\n              loop\n              playsInline\n              preload='metadata'\n              className='absolute inset-0 z-10 h-full w-full object-cover'\n            />\n            <div\n              className={`\n                absolute inset-0 z-20 bg-linear-to-b from-black/10\n                to-transparent\n              `}\n            ></div>\n          </div>\n          <div className={`absolute inset-0 z-30 flex items-center justify-center`}>\n            <div\n              className={`\n                relative max-w-sm transform rounded-2xl border-2\n                border-amber-200/50 bg-amber-800/40 px-4 py-3 shadow-xl\n                backdrop-blur-xs transition-transform duration-300\n                hover:scale-[1.02]\n                sm:max-w-md sm:px-6 sm:py-4\n                md:max-w-lg\n              `}\n            >\n              <h1\n                className={`\n                  ${robotoSlab.variable}\n                  text-center font-roboto-slab text-2xl leading-tight font-bold\n                  text-amber-50\n                  sm:text-3xl\n                  md:text-4xl\n                `}\n              >\n                <span\n                  className={`\n                    mb-1 block drop-shadow-[0_2px_3px_rgba(0,0,0,0.5)]\n                    sm:mb-2\n                  `}\n                >\n                  {t.hero.title}\n                </span>\n                <span\n                  className={`\n                    block font-extrabold tracking-wide text-white\n                    drop-shadow-[0_2px_3px_rgba(0,0,0,0.5)]\n                  `}\n                >\n                  {t.hero.subtitle}\n                </span>\n              </h1>\n              <div\n                className={`\n                  absolute -inset-[1px] -z-10 rounded-2xl bg-linear-to-b\n                  from-amber-200/20 to-transparent blur-xs\n                `}\n              ></div>\n            </div>\n          </div>\n        </div>\n\n        {/* Circular Images Section - positioned to overlap with hero */}\n        <div\n          className={`\n            relative z-10 mx-auto -mt-12 flex max-w-6xl flex-row items-center\n            justify-center gap-2 px-2\n            xs:gap-3\n            sm:-mt-16 sm:gap-4 sm:px-4\n            md:-mt-24 md:gap-6\n            lg:-mt-32 lg:gap-8\n          `}\n        >\n          {/* Swimming */}\n          <div\n            className={`\n              rotate-2 transform rounded-full bg-white p-1 shadow-lg\n              sm:p-2\n            `}\n          >\n            <div\n              className={`\n                relative h-20 w-20 overflow-hidden rounded-full\n                xs:h-24 xs:w-24\n                sm:h-32 sm:w-32\n                md:h-40 md:w-40\n                lg:h-48 lg:w-48\n              `}\n            >\n              <OptimizedImage\n                src='/images/round1.jpg'\n                alt={t.activities.swimming}\n                fill\n                imageType='thumbnail'\n                sizes='(max-width: 479px) 80px, (max-width: 639px) 96px, (max-width: 767px) 128px, (max-width: 1023px) 160px, 192px'\n                className='object-cover'\n              />\n            </div>\n          </div>\n\n          {/* Horse Riding */}\n          <div\n            className={`\n              -rotate-2 transform rounded-full bg-white p-1 shadow-lg\n              sm:p-2\n            `}\n          >\n            <div\n              className={`\n                relative h-20 w-20 overflow-hidden rounded-full\n                xs:h-24 xs:w-24\n                sm:h-32 sm:w-32\n                md:h-40 md:w-40\n                lg:h-48 lg:w-48\n              `}\n            >\n              <OptimizedImage\n                src='/images/round2.jpg'\n                alt={t.activities.horseRiding}\n                fill\n                imageType='thumbnail'\n                sizes='(max-width: 479px) 80px, (max-width: 639px) 96px, (max-width: 767px) 128px, (max-width: 1023px) 160px, 192px'\n                className='object-cover object-[center_20%]'\n              />\n            </div>\n          </div>\n\n          {/* Kayaking */}\n          <div\n            className={`\n              rotate-3 transform rounded-full bg-white p-1 shadow-lg\n              sm:p-2\n            `}\n          >\n            <div\n              className={`\n                relative h-20 w-20 overflow-hidden rounded-full\n                xs:h-24 xs:w-24\n                sm:h-32 sm:w-32\n                md:h-40 md:w-40\n                lg:h-48 lg:w-48\n              `}\n            >\n              <OptimizedImage\n                src='/images/round3.jpg'\n                alt={t.activities.kayaking}\n                fill\n                imageType='thumbnail'\n                sizes='(max-width: 479px) 80px, (max-width: 639px) 96px, (max-width: 767px) 128px, (max-width: 1023px) 160px, 192px'\n                className='object-cover'\n              />\n            </div>\n          </div>\n        </div>\n\n        {/* Introduction Text Section - adjusted margin top */}\n        <div\n          className={`\n            group relative mx-auto mt-20 max-w-4xl transform overflow-hidden\n            rounded-2xl px-6 py-8 text-center transition-all duration-500\n            hover:scale-[1.01]\n            sm:mt-24\n            md:mt-20 md:px-10 md:py-10\n          `}\n        >\n          {/* Background layers */}\n          <div\n            className={`\n              absolute inset-0 -z-10 rounded-2xl bg-linear-to-br\n              from-[#f5f0e8]/95 via-white/90 to-[#f5f0e8]/95 backdrop-blur-md\n            `}\n          ></div>\n          <div className='absolute inset-0 -z-20 rounded-2xl bg-[#6b8362]/5'></div>\n\n          {/* Decorative effects */}\n          <div\n            className={`\n              pointer-events-none absolute -inset-[3px] -z-10 rounded-2xl\n              opacity-70\n            `}\n          >\n            <div\n              className={`\n                absolute inset-0 rounded-2xl bg-linear-to-tr from-amber-200/30\n                via-transparent to-[#6b8362]/20\n              `}\n            ></div>\n          </div>\n          <div\n            className={`\n              pointer-events-none absolute inset-0 rounded-2xl border\n              border-amber-200/30\n            `}\n          ></div>\n\n          {/* Main content */}\n          <h2\n            className={`\n              ${robotoSlab.variable}\n              relative mb-6 inline-block font-roboto-slab text-3xl font-bold\n              text-[#3E5A35]\n              md:text-4xl\n            `}\n          >\n            {t.introduction.mainTitle}\n            <div\n              className={`\n                absolute -bottom-2 left-0 h-[2px] w-full bg-linear-to-r\n                from-transparent via-[#6b8362]/70 to-transparent\n              `}\n            ></div>\n          </h2>\n\n          <div className='relative'>\n            <p\n              className={`\n                mb-4 text-lg leading-relaxed text-gray-700\n                md:text-xl\n              `}\n            >\n              {t.introduction.mainText}\n            </p>\n            <p\n              className={`\n                mt-2 text-gray-600\n                md:text-lg\n              `}\n            >\n              {t.introduction.seoDescription}\n            </p>\n\n            {/* Subtle decorative corners */}\n            <div\n              className={`\n                absolute -top-1 -left-1 h-10 w-10 rounded-tl-lg border-t-2\n                border-l-2 border-[#6b8362]/30\n              `}\n            ></div>\n            <div\n              className={`\n                absolute -right-1 -bottom-1 h-10 w-10 rounded-br-lg border-r-2\n                border-b-2 border-[#6b8362]/30\n              `}\n            ></div>\n          </div>\n\n          {/* Shadow effect */}\n          <div className='absolute -inset-[1px] -z-30 rounded-2xl shadow-xl'></div>\n        </div>\n\n        {/* SUMMER 2025 OFFERS Title */}\n        <div\n          className={`\n            mt-16 flex flex-col items-center text-center\n            md:mt-20\n          `}\n        >\n          <h2\n            className={`\n              ${robotoSlab.variable}\n              relative mb-6 inline-block font-roboto-slab text-4xl font-bold\n              text-[#3E5A35]\n              md:text-5xl\n            `}\n          >\n            {t.activities.exploreNature}\n            <div\n              className={`\n                absolute -bottom-2 left-0 h-1 w-full bg-linear-to-r\n                from-transparent via-[#6b8362] to-transparent\n              `}\n            ></div>\n          </h2>\n\n          {/* Client-side Price List Button */}\n          <PriceListButton text={t.booking.ourPriceList} />\n        </div>\n\n        {/* Program Cards */}\n        <div\n          className={`\n            mx-auto mt-12 flex max-w-6xl flex-col items-stretch justify-center\n            gap-6 px-4\n            md:mt-16 md:px-8\n            lg:flex-row lg:gap-8\n          `}\n        >\n          {/* Package 1 Card */}\n          <div\n            className={`\n              flex w-full justify-center\n              lg:w-1/2\n            `}\n          >\n            <EnhancedPackageCard\n              title={t.programs.program1.title || 'PACKAGE 1'}\n              badge={t.programs.program1.badge || 'Most Popular'}\n              activities={{\n                primary: t.programs.program1.rafting || 'Rafting: 30 minutes',\n                riding: t.programs.program1.riding || 'Riding: 10-15 minutes',\n                hiking: t.programs.program1.hiking || 'Hiking canyon crossing',\n              }}\n              pricing={{\n                adults: '20 €',\n                children: '10 €',\n              }}\n              images={{\n                main: '/images/Rafting_Group_Blue_Adventure_River.jpg',\n                top: '/images/round2.jpg',\n                bottom: '/images/round1.jpg',\n              }}\n              bookingId='bokun_5b20d531_ca57_4550_94c0_0511c35077a0'\n              dataSrc='https://widgets.bokun.io/online-sales/c078b762-6f7f-474f-8edb-bdd1bdb7d12a/experience/1020598?partialView=1'\n              bookNowText={t.booking.bookNow}\n              packageName='Package 1 - Rafting + Riding + Hiking'\n              packagePrice='20'\n              trackingLabel='Homepage Package 1'\n              variant='green'\n            />\n          </div>\n\n          {/* Package 2 Card */}\n          <div\n            className={`\n              flex w-full justify-center\n              lg:w-1/2\n            `}\n          >\n            <EnhancedPackageCard\n              title={t.programs.program2.title || 'PACKAGE 2'}\n              badge={t.programs.program2.badge || 'New Experience'}\n              activities={{\n                primary: t.programs.program2.kayak || 'Kayak: 30 minutes',\n                riding: t.programs.program2.riding || 'Riding: 10-15 minutes',\n                hiking: t.programs.program2.hiking || 'Hiking canyon crossing',\n              }}\n              pricing={{\n                perPerson: '25 €',\n              }}\n              images={{\n                main: '/images/Kayaker_Red_Adventurous_River.jpg',\n                top: '/images/round3.jpg',\n                bottom: '/images/round2.jpg',\n              }}\n              bookingId='bokun_cfffa70c_61e3_4f58_91f4_e2f6cb562f53'\n              dataSrc='https://widgets.bokun.io/online-sales/c078b762-6f7f-474f-8edb-bdd1bdb7d12a/experience/1020569?partialView=1'\n              bookNowText={t.booking.bookNow}\n              packageName='Package 2 - Kayak + Riding + Hiking'\n              packagePrice='25'\n              trackingLabel='Homepage Package 2'\n              variant='amber'\n            />\n          </div>\n        </div>\n\n        {/* Customer Reviews Section */}\n        <div\n          className={`\n            mt-16 text-center\n            md:mt-20\n          `}\n        >\n          <h2\n            className={`\n              ${robotoSlab.variable}\n              relative mb-12 inline-block font-roboto-slab text-4xl font-bold\n              text-[#3E5A35]\n              md:mb-16 md:text-5xl\n            `}\n          >\n            {t.reviews.title}\n            <div\n              className={`\n                absolute -bottom-2 left-0 h-1 w-full bg-linear-to-r\n                from-transparent via-[#6b8362] to-transparent\n              `}\n            ></div>\n          </h2>\n        </div>\n\n        {/* Reviews Widget */}\n        <GoogleReviews />\n\n        {/* Map and Contact Section */}\n        <div\n          className={`\n            mt-20 mb-20 px-4\n            md:px-8\n          `}\n        >\n          <h2\n            className={`\n              ${robotoSlab.variable}\n              mb-10 text-center font-roboto-slab text-3xl text-[#6b8362]\n              md:text-4xl\n            `}\n          >\n            {t.location.findUs}\n          </h2>\n\n          <div\n            className={`\n              flex flex-col gap-10\n              lg:flex-row\n            `}\n          >\n            <div className='lg:w-3/5'>\n              <DynamicGoogleMap />\n            </div>\n            <div className='lg:w-2/5'>\n              <DynamicContactDetails />\n            </div>\n          </div>\n        </div>\n\n        {/* Global Bokun Button Styles */}\n        <BokunStyles />\n      </main>\n    </>\n  )\n}\n", "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/PageLayout.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/SummerProgramCard.tsx", "messages": [{"ruleId": "max-len", "severity": 1, "message": "This line has a length of 158. Maximum allowed is 120.", "line": 102, "column": 1, "nodeType": "Program", "messageId": "max", "endLine": 102, "endColumn": 159}], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "'use client'\n\nimport { Roboto_Slab } from 'next/font/google'\nimport React from 'react'\n\nimport { BorderBeam } from '@/components/ui/border-beam'\nimport { NumberTicker } from '@/components/ui/number-ticker'\nimport { OptimizedImage } from '@/components/ui/OptimizedImage'\nimport { PulsatingButton } from '@/components/ui/pulsating-button'\nimport { useLanguage } from '@/contexts/language-context' // Assuming this context provides 't' for translations\nimport { cn } from '@/lib/utils'\n\nconst robotoSlab = Roboto_Slab({\n  subsets: ['latin', 'greek'],\n  weight: ['400', '700'],\n  variable: '--font-roboto-slab',\n})\n\ninterface ActivityHighlight {\n  icon: React.ElementType\n  text: string\n}\n\ninterface SummerProgramCardProps {\n  title: string\n  price: number\n  priceSuffix?: string // e.g., \"€ Adults\" or \"€ per person\"\n  priceDetails?: string // e.g., \"10 € children under 12 years old\"\n  videoSrc?: string\n  imageSrc: string // Fallback if video isn't available or for poster\n  highlights: ActivityHighlight[]\n  badgeLabel?: string\n  badgeColor?: string // Tailwind color class e.g., \"bg-green-500\"\n  bookingId: string\n  bokunDataSrc: string\n  onBookNowClick: (event: React.MouseEvent<HTMLButtonElement>) => void\n  isPopular?: boolean // To conditionally apply \"Most Popular\" styling or different BorderBeam\n}\n\nexport function SummerProgramCard({\n  title,\n  price,\n  priceSuffix = '€',\n  priceDetails,\n  videoSrc,\n  imageSrc,\n  highlights,\n  badgeLabel,\n  badgeColor = 'bg-green-500',\n  bookingId,\n  bokunDataSrc,\n  onBookNowClick,\n  isPopular = false,\n}: SummerProgramCardProps) {\n  const { t } = useLanguage() // For \"Book Now\" and other potential translations\n\n  return (\n    <div\n      className={`\n        group relative w-full\n        md:w-1/2\n      `}\n    >\n      <div\n        className={`\n          relative h-[600px] transform overflow-hidden rounded-2xl border-l-4\n          border-[#19563F] shadow-xl transition-all duration-500\n          hover:translate-y-[-8px] hover:scale-[1.03]\n          md:h-[650px]\n        `}\n      >\n        {/* Background: Video or Image */}\n        {videoSrc ? (\n          <video\n            src={videoSrc}\n            autoPlay\n            muted\n            loop\n            playsInline\n            preload='metadata' // Changed from none to metadata for faster first frame\n            className='absolute inset-0 z-0 h-full w-full object-cover'\n            poster={imageSrc} // Use imageSrc as poster\n          />\n        ) : (\n          <OptimizedImage\n            src={imageSrc}\n            alt={title}\n            fill\n            sizes='(max-width: 767px) 100vw, 50vw'\n            className='z-0 object-cover'\n            imageType='default'\n            priority // Consider making this conditional if many cards\n          />\n        )}\n\n        {/* Subtle Topo Lines Background for the entire card section - to be added in parent page.tsx */}\n        {/* <GridPattern\n          width={40}\n          height={40}\n          x={-1}\n          y={-1}\n          className=\"absolute inset-0 -z-10 h-full w-full stroke-gray-500/10 opacity-50 [mask-image:radial-gradient(ellipse_at_center,white,transparent_80%)]\"\n        /> */}\n\n        {/* Glass Overlay / Content Protection */}\n        <div\n          className={`\n            absolute inset-0 z-10 bg-gradient-to-t from-black/70 via-black/40\n            to-transparent\n          `}\n        ></div>\n\n        {/* Card Content Layer */}\n        <div className='absolute inset-0 z-20 flex h-full flex-col p-6'>\n          {/* Top Section - Badge and Title */}\n          <div className='mb-4'>\n            {badgeLabel && (\n              <div\n                className={cn(\n                  `\n                    mb-3 inline-block rounded-full border border-white/20 px-4\n                    py-1.5 text-xs font-semibold text-white shadow-lg\n                    backdrop-blur-sm\n                    sm:text-sm\n                  `,\n                  badgeColor\n                )}\n              >\n                {badgeLabel}\n              </div>\n            )}\n            <h3\n              className={`\n                ${robotoSlab.variable}\n                font-roboto-slab text-3xl leading-tight font-bold text-white\n                drop-shadow-lg\n                sm:text-4xl\n              `}\n            >\n              {title}\n            </h3>\n          </div>\n\n          {/* Center Section - Activity List with \"Breaking\" Icons */}\n          <div className='my-4 grow'>\n            <div\n              className={`\n                max-w-sm rounded-xl border border-white/30 bg-white/80 p-4\n                shadow-lg backdrop-blur-md\n              `}\n            >\n              <ul className='space-y-3 text-gray-800'>\n                {highlights.map((highlight, index) => (\n                  <li key={index} className='flex items-center gap-3'>\n                    <div\n                      className={`\n                        relative -top-8 -left-2 transform transition-transform\n                        duration-300\n                        group-hover:-translate-y-1 group-hover:scale-110\n                      `}\n                    >\n                      <div\n                        className={`\n                          flex h-10 w-10 items-center justify-center\n                          rounded-full bg-[#19563F] p-2.5 shadow-md\n                        `}\n                      >\n                        <highlight.icon className='h-5 w-5 text-white' />\n                      </div>\n                    </div>\n                    <span\n                      className={`\n                        -ml-4 text-sm font-medium\n                        sm:text-base\n                      `}\n                    >\n                      {highlight.text}\n                    </span>\n                  </li>\n                ))}\n              </ul>\n            </div>\n          </div>\n\n          {/* Bottom Section - Price and Button */}\n          <div className='mt-auto'>\n            <div\n              className={`\n                rounded-xl border-t border-white/20 bg-black/50 p-5\n                backdrop-blur-md\n              `}\n            >\n              <div className='flex flex-col items-center text-center'>\n                <div\n                  className={`\n                    mb-1 text-4xl font-bold text-white\n                    sm:text-5xl\n                  `}\n                >\n                  <NumberTicker value={price} className='text-white' />\n                  <span\n                    className={`\n                      text-3xl\n                      sm:text-4xl\n                    `}\n                  >\n                    {priceSuffix}\n                  </span>\n                </div>\n                {priceDetails && (\n                  <p\n                    className={`\n                      mb-4 text-xs text-white/80\n                      sm:text-sm\n                    `}\n                  >\n                    {priceDetails}\n                  </p>\n                )}\n                <PulsatingButton\n                  className={`\n                    w-full rounded-lg bg-[#6b8362] py-3 text-lg font-semibold\n                    text-white shadow-lg transition-all duration-300\n                    hover:bg-[#3E5A35] hover:shadow-xl\n                  `}\n                  pulseColor='rgba(255, 255, 255, 0.5)'\n                  id={bookingId}\n                  data-src={bokunDataSrc}\n                  onClick={onBookNowClick}\n                >\n                  {t.booking.bookNow || 'Book Now'}\n                </PulsatingButton>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Optional Border Beam */}\n        {isPopular ? (\n          <BorderBeam duration={8} size={150} colorFrom='#FFD700' colorTo='#FFA500' /> // Gold/Orange for popular\n        ) : (\n          <BorderBeam duration={10} size={100} colorFrom='#4ade80' colorTo='#3b82f6' /> // Green/Blue for standard\n        )}\n      </div>\n    </div>\n  )\n}\n\n// Default export for lazy loading if needed, or named export for direct use.\nexport default SummerProgramCard\n", "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/activities-menu.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/client/BokunStyles.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/client/BookingButton.tsx", "messages": [{"ruleId": "no-console", "severity": 1, "message": "Unexpected console statement.", "line": 104, "column": 9, "nodeType": "MemberExpression", "messageId": "unexpected", "endLine": 104, "endColumn": 21, "suggestions": [{"fix": {"range": [3581, 3797], "text": ""}, "messageId": "removeConsole", "data": {"propertyName": "warn"}, "desc": "Remove the console.warn()."}]}, {"ruleId": "no-console", "severity": 1, "message": "Unexpected console statement.", "line": 137, "column": 7, "nodeType": "MemberExpression", "messageId": "unexpected", "endLine": 137, "endColumn": 18, "suggestions": [{"fix": {"range": [4657, 4819], "text": ""}, "messageId": "removeConsole", "data": {"propertyName": "log"}, "desc": "Remove the console.log()."}]}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: bokunButton", "line": 156, "column": 9, "nodeType": null, "endLine": 156, "endColumn": 20}], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "'use client'\n\nimport { useCallback, useRef } from 'react'\n\nimport { useGDPR } from '@/contexts/gdpr-context'\n\ninterface BookingButtonProps {\n  id: string\n  dataSrc: string\n  className?: string\n  children: React.ReactNode\n  trackingLabel?: string // For identifying which button was clicked\n  packageName?: string // For enhanced ecommerce tracking\n  packagePrice?: string // For conversion value tracking\n}\n\nexport default function BookingButton({\n  id,\n  dataSrc,\n  className,\n  children,\n  trackingLabel = 'Unknown',\n  packageName = 'Unknown Package',\n  packagePrice = '0',\n}: BookingButtonProps) {\n  const clickedButtonRef = useRef<HTMLButtonElement | null>(null)\n  const bokunReadyAttempts = useRef(0)\n  const { consent } = useGDPR()\n\n  const ensureBokunIsReadyAndOpen = useCallback(() => {\n    if (\n      window.BokunWidgets &&\n      (typeof window.BokunWidgets.init === 'function' || typeof window.BokunWidgets.reinit === 'function')\n    ) {\n      // Check if a modal is already open (very basic check, might need refinement)\n      if (document.querySelector('.bokunModalContainer') || document.querySelector('.bokun-modal-open')) {\n        return\n      }\n\n      if (clickedButtonRef.current) {\n        // It's possible Bokun's scripts have now attached proper listeners.\n        // A direct click might be better than trying to call their internal modal functions.\n        clickedButtonRef.current.click()\n        clickedButtonRef.current = null // Clear after attempting\n      }\n      bokunReadyAttempts.current = 0 // Reset attempts\n    } else if (bokunReadyAttempts.current < 30) {\n      // Try for ~3 seconds\n      bokunReadyAttempts.current++\n      setTimeout(ensureBokunIsReadyAndOpen, 100)\n    } else {\n      bokunReadyAttempts.current = 0 // Reset attempts\n    }\n  }, [])\n\n  // Comprehensive tracking function for GDPR-compliant analytics\n  const trackBookingClick = useCallback(() => {\n    if (typeof window === 'undefined' || !consent) return\n\n    // Extract numeric price for conversion tracking\n    const numericPrice = parseFloat(packagePrice.replace(/[^\\d.]/g, '')) || 0\n\n    // Google Analytics 4 Event Tracking (only if analytics consent given)\n    if (window.gtag && consent.analytics) {\n      // Standard GA4 event\n      window.gtag('event', 'book_now_click', {\n        event_category: 'Booking',\n        event_label: trackingLabel,\n        package_name: packageName,\n        package_price: numericPrice,\n        currency: 'EUR',\n        button_id: id,\n        page_location: window.location.href,\n        page_title: document.title,\n      })\n\n      // Enhanced Ecommerce - Begin Checkout Event\n      window.gtag('event', 'begin_checkout', {\n        currency: 'EUR',\n        value: numericPrice,\n        items: [\n          {\n            item_id: id,\n            item_name: packageName,\n            item_category: 'Adventure Package',\n            price: numericPrice,\n            quantity: 1,\n          },\n        ],\n      })\n\n      // Google Ads Conversion Tracking\n      const googleAdsConversionId = process.env.NEXT_PUBLIC_GOOGLE_ADS_CONVERSION_ID\n      const googleAdsConversionLabel = process.env.NEXT_PUBLIC_GOOGLE_ADS_CONVERSION_LABEL\n\n      if (googleAdsConversionId && googleAdsConversionLabel) {\n        window.gtag('event', 'conversion', {\n          send_to: `${googleAdsConversionId}/${googleAdsConversionLabel}`,\n          value: numericPrice,\n          currency: 'EUR',\n          transaction_id: `booking_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n        })\n      } else if (process.env.NODE_ENV === 'development') {\n        console.warn(\n          '[Booking Tracking] Google Ads conversion tracking not configured. Please set NEXT_PUBLIC_GOOGLE_ADS_CONVERSION_ID and NEXT_PUBLIC_GOOGLE_ADS_CONVERSION_LABEL environment variables.'\n        )\n      }\n    }\n\n    // Vercel Analytics (if available and analytics consent given)\n    if (window.va && consent.analytics) {\n      interface VercelAnalytics {\n        (event: string, data: { name: string; data: Record<string, unknown> }): void\n      }\n      ;(window.va as VercelAnalytics)('event', {\n        name: 'Book Now Click',\n        data: {\n          package: packageName,\n          price: numericPrice,\n          button_id: id,\n          label: trackingLabel,\n        },\n      })\n    }\n\n    // Facebook Pixel (if available and marketing consent given)\n    if (window.fbq && consent.marketing) {\n      window.fbq('track', 'InitiateCheckout', {\n        content_name: packageName,\n        content_category: 'Adventure Package',\n        value: numericPrice,\n        currency: 'EUR',\n      })\n    }\n\n    if (process.env.NODE_ENV === 'development') {\n      console.log(\n        `[Booking Tracking] ${trackingLabel} clicked - Package: ${packageName}, Price: €${numericPrice}`,\n        'Consent:',\n        consent\n      )\n    }\n  }, [trackingLabel, packageName, packagePrice, id, consent])\n\n  const handleBookNowClick = (event: React.MouseEvent<HTMLButtonElement>) => {\n    // Track the click immediately\n    trackBookingClick()\n\n    clickedButtonRef.current = event.currentTarget\n    ensureBokunIsReadyAndOpen() // Direct call as loader is global\n  }\n\n  return (\n    <button\n      className={`\n        bokunButton\n        ${className}\n      `}\n      id={id}\n      data-src={dataSrc}\n      data-testid='widget-book-button'\n      onClick={handleBookNowClick}\n    >\n      {children}\n    </button>\n  )\n}\n", "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/client/GDPRBanner.tsx", "messages": [{"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: animate-in", "line": 213, "column": 9, "nodeType": null, "endLine": 213, "endColumn": 19}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: slide-in-from-bottom", "line": 213, "column": 20, "nodeType": null, "endLine": 213, "endColumn": 40}], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "'use client'\n\nimport { Bar<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Target, X } from 'lucide-react'\nimport { useState } from 'react'\n\nimport { useGDPR, type CookieConsent } from '@/contexts/gdpr-context'\nimport { useLanguage } from '@/contexts/language-context'\n\nexport default function GDPRBanner() {\n  const {\n    consent: _consent,\n    showBanner,\n    showCustomize,\n    acceptAll,\n    rejectAll,\n    saveCustom,\n    openCustomize,\n    closeBanner,\n  } = useGDPR()\n  const { t } = useLanguage()\n  const [customConsent, setCustomConsent] = useState<CookieConsent>({\n    necessary: true,\n    analytics: true,\n    marketing: true,\n  })\n\n  if (!showBanner) return null\n\n  const handleCustomConsentChange = (type: keyof CookieConsent, value: boolean) => {\n    setCustomConsent(prev => ({\n      ...prev,\n      [type]: type === 'necessary' ? true : value, // Necessary cookies always enabled\n    }))\n  }\n\n  if (showCustomize) {\n    return (\n      <div\n        className={`\n          fixed inset-0 z-50 flex items-center justify-center bg-black/50 p-4\n          backdrop-blur-sm\n        `}\n      >\n        <div\n          className={`\n            max-h-[90vh] w-full max-w-2xl overflow-y-auto rounded-2xl bg-white\n            shadow-2xl\n          `}\n        >\n          {/* Header */}\n          <div\n            className={`\n              flex items-center justify-between border-b border-gray-200 p-6\n            `}\n          >\n            <h2\n              className={`\n                flex items-center gap-2 text-2xl font-bold text-[#3E5A35]\n              `}\n            >\n              <Settings className='h-6 w-6' />\n              {t.gdpr.customize}\n            </h2>\n            <button\n              onClick={closeBanner}\n              className={`\n                rounded-full p-2 transition-colors\n                hover:bg-gray-100\n              `}\n              aria-label='Close cookie settings'\n            >\n              <X className='h-5 w-5 text-gray-500' />\n            </button>\n          </div>\n\n          {/* Content */}\n          <div className='space-y-6 p-6'>\n            <p className='leading-relaxed text-gray-600'>{t.gdpr.description}</p>\n\n            {/* Cookie Categories */}\n            <div className='space-y-4'>\n              {/* Necessary Cookies */}\n              <div className='rounded-lg border border-gray-200 p-4'>\n                <div className='mb-2 flex items-center justify-between'>\n                  <div className='flex items-center gap-2'>\n                    <Shield className='h-5 w-5 text-green-600' />\n                    <h3 className='font-semibold text-gray-900'>{t.gdpr.necessary}</h3>\n                  </div>\n                  <div\n                    className={`\n                      rounded-full bg-green-100 px-3 py-1 text-sm font-medium\n                      text-green-800\n                    `}\n                  >\n                    Always Active\n                  </div>\n                </div>\n                <p className='text-sm text-gray-600'>{t.gdpr.necessaryDescription}</p>\n              </div>\n\n              {/* Analytics Cookies */}\n              <div className='rounded-lg border border-gray-200 p-4'>\n                <div className='mb-2 flex items-center justify-between'>\n                  <div className='flex items-center gap-2'>\n                    <BarChart3 className='h-5 w-5 text-blue-600' />\n                    <h3 className='font-semibold text-gray-900'>{t.gdpr.analytics}</h3>\n                  </div>\n                  <label\n                    className={`\n                      relative inline-flex cursor-pointer items-center\n                    `}\n                  >\n                    <input\n                      type='checkbox'\n                      checked={customConsent.analytics}\n                      onChange={e => handleCustomConsentChange('analytics', e.target.checked)}\n                      className='peer sr-only'\n                    />\n                    <div\n                      className={`\n                        peer h-6 w-11 rounded-full bg-gray-200\n                        peer-checked:bg-[#6b8362]\n                        peer-checked:after:translate-x-full\n                        peer-checked:after:border-white\n                        peer-focus:ring-4 peer-focus:ring-[#6b8362]/20\n                        peer-focus:outline-none\n                        after:absolute after:top-[2px] after:left-[2px]\n                        after:h-5 after:w-5 after:rounded-full after:bg-white\n                        after:transition-all after:content-['']\n                      `}\n                    ></div>\n                  </label>\n                </div>\n                <p className='text-sm text-gray-600'>{t.gdpr.analyticsDescription}</p>\n              </div>\n\n              {/* Marketing Cookies */}\n              <div className='rounded-lg border border-gray-200 p-4'>\n                <div className='mb-2 flex items-center justify-between'>\n                  <div className='flex items-center gap-2'>\n                    <Target className='h-5 w-5 text-purple-600' />\n                    <h3 className='font-semibold text-gray-900'>{t.gdpr.marketing}</h3>\n                  </div>\n                  <label\n                    className={`\n                      relative inline-flex cursor-pointer items-center\n                    `}\n                  >\n                    <input\n                      type='checkbox'\n                      checked={customConsent.marketing}\n                      onChange={e => handleCustomConsentChange('marketing', e.target.checked)}\n                      className='peer sr-only'\n                    />\n                    <div\n                      className={`\n                        peer h-6 w-11 rounded-full bg-gray-200\n                        peer-checked:bg-[#6b8362]\n                        peer-checked:after:translate-x-full\n                        peer-checked:after:border-white\n                        peer-focus:ring-4 peer-focus:ring-[#6b8362]/20\n                        peer-focus:outline-none\n                        after:absolute after:top-[2px] after:left-[2px]\n                        after:h-5 after:w-5 after:rounded-full after:bg-white\n                        after:transition-all after:content-['']\n                      `}\n                    ></div>\n                  </label>\n                </div>\n                <p className='text-sm text-gray-600'>{t.gdpr.marketingDescription}</p>\n              </div>\n            </div>\n\n            {/* Action Buttons */}\n            <div\n              className={`\n                flex flex-col gap-3 pt-4\n                sm:flex-row\n              `}\n            >\n              <button\n                onClick={() => saveCustom(customConsent)}\n                className={`\n                  flex-1 rounded-lg bg-[#6b8362] px-6 py-3 font-semibold\n                  text-white shadow-md transition-colors duration-300\n                  hover:bg-[#3E5A35] hover:shadow-lg\n                `}\n                aria-label='Save custom cookie preferences'\n              >\n                {t.gdpr.save}\n              </button>\n              <button\n                onClick={acceptAll}\n                className={`\n                  flex-1 rounded-lg bg-gray-100 px-6 py-3 font-semibold\n                  text-gray-800 transition-colors duration-300\n                  hover:bg-gray-200\n                `}\n                aria-label='Accept all cookies'\n              >\n                {t.gdpr.acceptAll}\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div\n      className={`\n        animate-in slide-in-from-bottom fixed right-0 bottom-0 left-0 z-50\n        border-t border-gray-200 bg-white/95 shadow-2xl backdrop-blur-md\n        duration-500\n      `}\n    >\n      <div\n        className={`\n          mx-auto max-w-7xl p-4\n          sm:p-6\n        `}\n      >\n        <div\n          className={`\n            flex flex-col items-start gap-4\n            lg:flex-row lg:items-center\n          `}\n        >\n          {/* Icon and Content */}\n          <div className='flex flex-1 items-start gap-3'>\n            <div\n              className={`\n                flex h-10 w-10 flex-shrink-0 items-center justify-center\n                rounded-full bg-[#6b8362]/10\n              `}\n            >\n              <Shield className='h-5 w-5 text-[#6b8362]' />\n            </div>\n            <div className='flex-1'>\n              <h3 className='mb-1 font-semibold text-gray-900'>{t.gdpr.title}</h3>\n              <p className='text-sm leading-relaxed text-gray-600'>{t.gdpr.description}</p>\n            </div>\n          </div>\n\n          {/* Action Buttons */}\n          <div\n            className={`\n              flex w-full flex-col gap-3\n              sm:flex-row\n              lg:w-auto lg:flex-shrink-0\n            `}\n          >\n            <button\n              onClick={openCustomize}\n              className={`\n                rounded-lg border border-[#6b8362]/20 px-4 py-2 text-sm\n                font-medium text-[#6b8362] transition-colors duration-300\n                hover:border-[#6b8362]/40 hover:bg-[#6b8362]/5\n                hover:text-[#3E5A35]\n              `}\n              aria-label='Customize cookie settings'\n            >\n              {t.gdpr.customize}\n            </button>\n            <button\n              onClick={rejectAll}\n              className={`\n                rounded-lg px-4 py-2 text-sm font-medium text-gray-600\n                transition-colors duration-300\n                hover:bg-gray-100 hover:text-gray-800\n              `}\n              aria-label='Reject all cookies'\n            >\n              {t.gdpr.rejectAll}\n            </button>\n            <button\n              onClick={acceptAll}\n              className={`\n                rounded-lg bg-[#6b8362] px-6 py-2 text-sm font-semibold\n                text-white shadow-md transition-colors duration-300\n                hover:bg-[#3E5A35] hover:shadow-lg\n              `}\n              aria-label='Accept all cookies'\n            >\n              {t.gdpr.acceptAll}\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n", "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/client/GDPRGoogleAnalytics.tsx", "messages": [{"ruleId": "@typescript-eslint/no-unused-vars", "severity": 1, "message": "'CookieConsent' is defined but never used. Allowed unused vars must match /^_/u.", "line": 6, "column": 19, "nodeType": null, "messageId": "unusedVar", "endLine": 6, "endColumn": 32}, {"ruleId": "no-console", "severity": 1, "message": "Unexpected console statement.", "line": 24, "column": 7, "nodeType": "MemberExpression", "messageId": "unexpected", "endLine": 24, "endColumn": 18, "suggestions": [{"fix": {"range": [885, 935], "text": ""}, "messageId": "removeConsole", "data": {"propertyName": "log"}, "desc": "Remove the console.log()."}]}, {"ruleId": "no-console", "severity": 1, "message": "Unexpected console statement.", "line": 53, "column": 7, "nodeType": "MemberExpression", "messageId": "unexpected", "endLine": 53, "endColumn": 18, "suggestions": [{"fix": {"range": [1717, 1786], "text": ""}, "messageId": "removeConsole", "data": {"propertyName": "log"}, "desc": "Remove the console.log()."}]}, {"ruleId": "no-console", "severity": 1, "message": "Unexpected console statement.", "line": 65, "column": 11, "nodeType": "MemberExpression", "messageId": "unexpected", "endLine": 65, "endColumn": 22, "suggestions": [{"fix": {"range": [2056, 2096], "text": ""}, "messageId": "removeConsole", "data": {"propertyName": "log"}, "desc": "Remove the console.log()."}]}, {"ruleId": "no-console", "severity": 1, "message": "Unexpected console statement.", "line": 68, "column": 11, "nodeType": "MemberExpression", "messageId": "unexpected", "endLine": 68, "endColumn": 24, "suggestions": [{"fix": {"range": [2143, 2193], "text": ""}, "messageId": "removeConsole", "data": {"propertyName": "error"}, "desc": "Remove the console.error()."}]}], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "'use client'\n\nimport Script from 'next/script'\nimport { useEffect } from 'react'\n\nimport { useGDPR, CookieConsent } from '@/contexts/gdpr-context' // Import CookieConsent\n\ninterface GDPRGoogleAnalyticsProps {\n  gaId: string\n}\n\nexport default function GDPRGoogleAnalytics({ gaId }: GDPRGoogleAnalyticsProps) {\n  const { consent } = useGDPR() // Get the consent object\n\n  useEffect(() => {\n    // This effect handles applying consent changes to gtag\n    if (typeof window !== 'undefined' && window.gtag && consent) {\n      window.gtag('consent', 'update', {\n        analytics_storage: consent.analytics ? 'granted' : 'denied',\n        ad_storage: consent.marketing ? 'granted' : 'denied', // Assuming marketing consent maps to ad_storage\n        ad_user_data: consent.marketing ? 'granted' : 'denied',\n        ad_personalization: consent.marketing ? 'granted' : 'denied',\n      })\n      console.log('[GDPR GA] Consent updated:', consent)\n    }\n  }, [consent]) // Re-run when consent object changes\n\n  // Initialize gtag in a CSP-compliant way\n  useEffect(() => {\n    if (typeof window !== 'undefined') {\n      // Initialize dataLayer and gtag function\n      window.dataLayer = window.dataLayer || []\n      function gtag(...args: unknown[]) {\n        window.dataLayer?.push(args)\n      }\n      window.gtag = gtag\n\n      // Set default consent to denied\n      gtag('consent', 'default', {\n        analytics_storage: 'denied',\n        ad_storage: 'denied',\n        ad_user_data: 'denied',\n        ad_personalization: 'denied',\n        wait_for_update: 500,\n      })\n\n      gtag('js', new Date())\n      gtag('config', gaId, {\n        page_title: document.title,\n        page_location: window.location.href,\n      })\n\n      console.log('[GDPR GA] gtag initialized with default denied consent')\n    }\n  }, [gaId])\n\n  return (\n    <>\n      {/* External gtag script - CSP compliant */}\n      <Script\n        id='ga-gtag-script'\n        src={`https://www.googletagmanager.com/gtag/js?id=${gaId}`}\n        strategy='afterInteractive'\n        onLoad={() => {\n          console.log('[GDPR GA] gtag.js loaded.')\n        }}\n        onError={() => {\n          console.error('[GDPR GA] Failed to load gtag.js.')\n        }}\n      />\n    </>\n  )\n}\n", "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/client/GoogleReviews.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/client/PriceListButton.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/contact-details-enhanced.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/desktop-menu.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/hamburger-menu.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/hero/HeroSection.tsx", "messages": [{"ruleId": "@typescript-eslint/no-unused-vars", "severity": 1, "message": "'useState' is defined but never used. Allowed unused vars must match /^_/u.", "line": 4, "column": 29, "nodeType": null, "messageId": "unusedVar", "endLine": 4, "endColumn": 37}, {"ruleId": "better-tailwindcss/multiline", "severity": 1, "message": "Unnecessary line wrapping. Expected\n\n`↵\n········absolute·bottom-20·z-20·flex·w-full·justify-center↵\n······`\n\nto be\n\n`absolute·bottom-20·z-20·flex·w-full·justify-center`", "line": 115, "column": 20, "nodeType": null, "endLine": 117, "endColumn": 8, "fix": {"range": [3005, 3073], "text": "`absolute bottom-20 z-20 flex w-full justify-center`"}}], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": "'use client'\n\nimport { motion, useScroll, useTransform } from 'framer-motion'\nimport { useEffect, useRef, useState } from 'react'\n\nfunction RippleCanvas() {\n  const canvasRef = useRef<HTMLCanvasElement>(null)\n\n  useEffect(() => {\n    const canvas = canvasRef.current\n    if (!canvas) return\n    const ctx = canvas.getContext('2d')\n    if (!ctx) return\n\n    let animationFrameId: number\n    let width = (canvas.width = window.innerWidth)\n    let height = (canvas.height = window.innerHeight)\n\n    // Simple ripple effect parameters\n    const ripples: { x: number; y: number; radius: number; alpha: number }[] = []\n\n    function addRipple() {\n      ripples.push({\n        x: Math.random() * width,\n        y: Math.random() * height,\n        radius: 0,\n        alpha: 0.5,\n      })\n    }\n\n    function draw() {\n      if (!ctx) return\n      ctx.clearRect(0, 0, width, height)\n      ripples.forEach((ripple, i) => {\n        ripple.radius += 0.5\n        ripple.alpha -= 0.005\n        if (ripple.alpha <= 0) {\n          ripples.splice(i, 1)\n        } else {\n          ctx.beginPath()\n          ctx.strokeStyle = `rgba(255, 255, 255, ${ripple.alpha})`\n          ctx.lineWidth = 2\n          ctx.arc(ripple.x, ripple.y, ripple.radius, 0, Math.PI * 2)\n          ctx.stroke()\n        }\n      })\n    }\n\n    function animate() {\n      draw()\n      animationFrameId = requestAnimationFrame(animate)\n    }\n\n    // Add ripples periodically\n    const intervalId = setInterval(addRipple, 800)\n\n    animate()\n\n    // Resize handler\n    function handleResize() {\n      if (!canvas) return // Add null check for canvas\n      width = canvas.width = window.innerWidth\n      height = canvas.height = window.innerHeight\n    }\n    window.addEventListener('resize', handleResize)\n\n    return () => {\n      cancelAnimationFrame(animationFrameId)\n      clearInterval(intervalId)\n      window.removeEventListener('resize', handleResize)\n    }\n  }, [])\n\n  return (\n    <canvas\n      ref={canvasRef}\n      className='pointer-events-none absolute inset-0 mix-blend-soft-light'\n      style={{ zIndex: 25 }}\n    />\n  )\n}\n\nexport default function HeroSection() {\n  const { scrollY } = useScroll()\n  // Parallax title slides in from below as user scrolls down 0 to 300px\n  const y = useTransform(scrollY, [0, 300], [100, 0])\n  const opacity = useTransform(scrollY, [0, 300], [0, 1])\n\n  return (\n    <section className='relative h-[80vh] w-full overflow-hidden'>\n      {/* Video Background */}\n      <video\n        src='/videos/hero-loop.mp4'\n        autoPlay\n        muted\n        loop\n        playsInline\n        preload='auto'\n        className='absolute inset-0 h-full w-full object-cover'\n      />\n      {/* Ripple effect canvas */}\n      <RippleCanvas />\n\n      {/* Overlay gradient */}\n      <div\n        className={`\n          absolute inset-0 z-10 bg-gradient-to-b from-black/30 via-transparent\n          to-black/40\n        `}\n      />\n\n      {/* Parallax Title */}\n      <motion.div\n        style={{ y, opacity }}\n        className={`\n        absolute bottom-20 z-20 flex w-full justify-center\n      `}\n      >\n        <h1\n          className={`\n            text-5xl font-extrabold text-white drop-shadow-lg select-none\n            md:text-7xl\n          `}\n        >\n          The Full Pony Club Experience\n        </h1>\n      </motion.div>\n\n      {/* Scroll Prompt */}\n      <motion.div\n        animate={{ opacity: [0.5, 1, 0.5] }}\n        transition={{ repeat: Infinity, duration: 2 }}\n        className={`\n          absolute bottom-6 left-1/2 z-30 flex -translate-x-1/2 flex-col\n          items-center text-white select-none\n        `}\n      >\n        <span className='mb-2 text-lg font-semibold'>Adventure begins here</span>\n        <svg\n          className='h-8 w-8 animate-bounce'\n          fill='none'\n          stroke='currentColor'\n          strokeWidth='2'\n          viewBox='0 0 24 24'\n          xmlns='http://www.w3.org/2000/svg'\n          aria-hidden='true'\n        >\n          <path strokeLinecap='round' strokeLinejoin='round' d='M19 9l-7 7-7-7'></path>\n        </svg>\n      </motion.div>\n    </section>\n  )\n}\n", "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/language-selector.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/responsive-navigation.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/site-header.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/standalone-menu-items.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/theme-provider.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/Container.tsx", "messages": [{"ruleId": "@typescript-eslint/no-unused-vars", "severity": 1, "message": "'ReactNode' is defined but never used. Allowed unused vars must match /^_/u.", "line": 2, "column": 17, "nodeType": null, "messageId": "unusedVar", "endLine": 2, "endColumn": 26}], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "import type { JSX } from 'react'\nimport React, { ReactNode } from 'react' // Added JSX import\n\ninterface ContainerProps {\n  children: React.ReactNode\n  className?: string\n  as?: keyof JSX.IntrinsicElements // This should now find the JSX namespace\n  fluid?: boolean\n}\n\n/**\n * Container component for consistent boxed layout across the site\n *\n * @param children - The content to be displayed within the container\n * @param className - Additional CSS classes to apply\n * @param as - HTML element to render the container as (default: div)\n * @param fluid - Whether the container should have a fluid width on larger screens\n */\nexport function Container({ children, className = '', as: Component = 'div', fluid = false }: ContainerProps) {\n  // Base classes for all containers\n  let containerClasses = 'mx-auto px-4 sm:px-6 md:px-8 lg:px-10'\n\n  // Add max-width constraint if not fluid\n  if (!fluid) {\n    containerClasses += ' max-w-screen-xl' // 1280px max width for desktop\n  }\n\n  return (\n    <Component\n      className={`\n        ${containerClasses}\n        ${className}\n      `}\n    >\n      {children}\n    </Component>\n  )\n}\n", "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/Gallery.tsx", "messages": [{"ruleId": "better-tailwindcss/multiline", "severity": 1, "message": "Unnecessary line wrapping. Expected\n\n`↵\n··············object-cover↵\n············`\n\nto be\n\n`object-cover`", "line": 68, "column": 26, "nodeType": null, "endLine": 70, "endColumn": 14, "fix": {"range": [1764, 1806], "text": "`object-cover`"}}], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": "'use client'\n\nimport { useState } from 'react'\n\nimport { Dialog, DialogContent } from './dialog'\nimport { GalleryImage, OptimizedImage } from './OptimizedImage'\n\ninterface GalleryProps {\n  images: {\n    src: string\n    alt: string\n    width?: number\n    height?: number\n  }[]\n  title: string\n  ariaLabel?: string\n}\n\nexport function Gallery({ images, title, ariaLabel = 'Photo gallery' }: GalleryProps) {\n  const [selectedImage, setSelectedImage] = useState<number | null>(null)\n\n  return (\n    <div\n      className={`\n        relative rounded-lg border border-amber-100/70 bg-white/80 p-6 shadow-lg\n        backdrop-blur-xs transition-shadow duration-300\n        hover:shadow-xl\n      `}\n    >\n      <h2\n        className={`\n          relative mb-4 inline-block text-2xl font-bold text-amber-800\n        `}\n      >\n        {title}\n        <div\n          className={`\n            absolute -bottom-1 left-0 h-[2px] w-full bg-linear-to-r\n            from-transparent via-amber-500/50 to-transparent\n          `}\n        ></div>\n      </h2>\n\n      <div\n        className={`\n          grid grid-cols-1 gap-4\n          sm:grid-cols-2\n          lg:grid-cols-3\n        `}\n        role='region'\n        aria-label={ariaLabel}\n      >\n        {images.map((image, index) => (\n          <div\n            key={index}\n            className={`\n              relative h-64 cursor-pointer overflow-hidden rounded-xl border\n              border-amber-100/70 shadow-md transition-all duration-300\n              hover:scale-[1.02] hover:shadow-xl\n            `}\n            onClick={() => setSelectedImage(index)}\n          >\n            <GalleryImage\n              src={image.src}\n              alt={image.alt}\n              fill\n              index={index}\n              className={`\n              object-cover\n            `}\n            />\n            <div\n              className={`\n                absolute inset-0 bg-linear-to-b from-black/5 to-transparent\n              `}\n            ></div>\n          </div>\n        ))}\n      </div>\n\n      <div\n        className={`\n          absolute -inset-[1px] -z-10 rounded-lg bg-linear-to-tr\n          from-amber-200/20 via-white/50 to-[#6b8362]/20 blur-xs\n        `}\n      ></div>\n\n      <Dialog open={selectedImage !== null} onOpenChange={() => setSelectedImage(null)}>\n        <DialogContent\n          className={`\n            max-w-5xl overflow-hidden rounded-xl bg-white/90 p-0\n            backdrop-blur-md\n          `}\n        >\n          {selectedImage !== null && (\n            <div className='relative h-[80vh]'>\n              <OptimizedImage // Use OptimizedImage here\n                src={images[selectedImage].src}\n                alt={images[selectedImage].alt}\n                fill\n                sizes='(max-width: 640px) 90vw, (max-width: 1024px) 90vw, 1024px'\n                className='object-contain'\n                priority // Keep priority for LCP candidate\n                // imageType=\"default\" // Or let OptimizedImage decide based on props\n              />\n              <button\n                onClick={() => setSelectedImage(null)}\n                className={`\n                  absolute top-4 right-4 z-10 rounded-full bg-white/80 p-2\n                  backdrop-blur-xs\n                  hover:bg-white\n                `}\n                aria-label='Close image'\n              >\n                <svg\n                  xmlns='http://www.w3.org/2000/svg'\n                  width='24'\n                  height='24'\n                  viewBox='0 0 24 24'\n                  fill='none'\n                  stroke='currentColor'\n                  strokeWidth='2'\n                  strokeLinecap='round'\n                  strokeLinejoin='round'\n                >\n                  <line x1='18' y1='6' x2='6' y2='18'></line>\n                  <line x1='6' y1='6' x2='18' y2='18'></line>\n                </svg>\n              </button>\n            </div>\n          )}\n        </DialogContent>\n      </Dialog>\n    </div>\n  )\n}\n", "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/OptimizedImage.tsx", "messages": [{"ruleId": "complexity", "severity": 1, "message": "Function 'OptimizedImage' has a complexity of 17. Maximum allowed is 15.", "line": 22, "column": 8, "nodeType": "FunctionDeclaration", "messageId": "complex", "endLine": 111, "endColumn": 2}, {"ruleId": "@typescript-eslint/no-explicit-any", "severity": 1, "message": "Unexpected any. Specify a different type.", "line": 38, "column": 66, "nodeType": "TSAnyKeyword", "messageId": "unexpectedAny", "endLine": 38, "endColumn": 69, "suggestions": [{"messageId": "suggestUnknown", "fix": {"range": [1168, 1171], "text": "unknown"}, "desc": "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct."}, {"messageId": "suggestNever", "fix": {"range": [1168, 1171], "text": "never"}, "desc": "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of."}]}], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "'use client'\n\nimport type { ImageProps } from 'next/image'\nimport Image from 'next/image'\nimport React from 'react'\n\nimport { optimizeImageProps, optimizeHeroImage, optimizeGalleryImage } from '@/lib/image-optimization'\n\nexport type OptimizedImageProps = Omit<ImageProps, 'src' | 'alt'> & {\n  src: string\n  alt: string\n  imageType?: 'default' | 'hero' | 'gallery' | 'avatar' | 'logo' | 'thumbnail'\n  index?: number // For gallery images\n  aspectRatio?: string // For setting specific aspect ratios\n  containerClassName?: string // For styling the container\n}\n\n/**\n * OptimizedImage component - A wrapper around Next.js Image component\n * that automatically applies performance optimizations\n */\nexport function OptimizedImage({\n  imageType = 'default',\n  index = 0,\n  aspectRatio,\n  containerClassName,\n  className,\n  ...props\n}: OptimizedImageProps) {\n  // Apply image-type specific optimizations\n  const optimizedProps = (() => {\n    switch (imageType) {\n      case 'hero':\n        return optimizeHeroImage(props)\n      case 'gallery':\n        return optimizeGalleryImage(props, index)\n      default:\n        return optimizeImageProps({ ...props, type: imageType as any })\n    }\n  })()\n\n  // If using fill, we need to wrap in a container with relative positioning\n  if (optimizedProps.fill) {\n    // Apply responsive aspect ratio if provided\n    const aspectRatioClass = aspectRatio ? `aspect-${aspectRatio}` : ''\n\n    // For hero images, make sure we don't have both priority and loading\n    const imageProps =\n      imageType === 'hero'\n        ? {\n            src: props.src,\n            alt: props.alt,\n            fill: true,\n            sizes: optimizedProps.sizes || '100vw',\n            quality: optimizedProps.quality,\n            priority: true,\n            className: `object-cover ${className || ''}`,\n          }\n        : {\n            src: props.src,\n            alt: props.alt,\n            fill: true,\n            sizes: optimizedProps.sizes || '100vw',\n            quality: optimizedProps.quality,\n            priority: optimizedProps.priority,\n            loading: !optimizedProps.priority ? optimizedProps.loading || 'lazy' : undefined,\n            className: `object-cover ${className || ''}`,\n          }\n\n    return (\n      <div\n        className={`\n          relative overflow-hidden\n          ${aspectRatioClass}\n          ${containerClassName || ''}\n        `}\n        style={!aspectRatioClass ? { height: '100%' } : undefined}\n      >\n        <Image {...imageProps} />\n      </div>\n    )\n  }\n\n  // For non-fill images, just render the optimized Image component\n  // Make sure hero images don't have both priority and loading\n  const imageProps =\n    imageType === 'hero'\n      ? {\n          src: props.src,\n          alt: props.alt,\n          width: optimizedProps.width,\n          height: optimizedProps.height,\n          quality: optimizedProps.quality,\n          priority: true,\n          sizes: optimizedProps.sizes,\n          className,\n        }\n      : {\n          src: props.src,\n          alt: props.alt,\n          width: optimizedProps.width,\n          height: optimizedProps.height,\n          quality: optimizedProps.quality,\n          priority: optimizedProps.priority,\n          loading: !optimizedProps.priority ? optimizedProps.loading || 'lazy' : undefined,\n          sizes: optimizedProps.sizes,\n          className,\n        }\n\n  return <Image {...imageProps} />\n}\n\n/**\n * HeroImage component - Specialized for hero images\n */\nexport function HeroImage(props: Omit<OptimizedImageProps, 'imageType'>) {\n  return <OptimizedImage {...props} imageType='hero' />\n}\n\n/**\n * GalleryImage component - Specialized for gallery images\n */\nexport function GalleryImage({ index = 0, ...props }: Omit<OptimizedImageProps, 'imageType'> & { index?: number }) {\n  return <OptimizedImage {...props} imageType='gallery' index={index} />\n}\n\n/**\n * AvatarImage component - Specialized for avatar images\n */\nexport function AvatarImage(props: Omit<OptimizedImageProps, 'imageType'>) {\n  return <OptimizedImage {...props} imageType='avatar' />\n}\n\nexport default OptimizedImage\n", "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/PriceListPopup.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/accordion.tsx", "messages": [{"ruleId": "better-tailwindcss/multiline", "severity": 1, "message": "Unnecessary line wrapping. Expected\n\n`↵\n········h-4·w-4·shrink-0·transition-transform·duration-200↵\n······`\n\nto be\n\n`h-4·w-4·shrink-0·transition-transform·duration-200`", "line": 39, "column": 20, "nodeType": null, "endLine": 41, "endColumn": 8, "fix": {"range": [1183, 1251], "text": "`h-4 w-4 shrink-0 transition-transform duration-200`"}}], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": "'use client'\n\nimport * as AccordionPrimitive from '@radix-ui/react-accordion'\nimport { ChevronDown } from 'lucide-react'\nimport * as React from 'react'\n\nimport { cn } from '@/lib/utils'\n\nconst Accordion = AccordionPrimitive.Root\n\nconst AccordionItem = React.forwardRef<\n  React.ElementRef<typeof AccordionPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Item>\n>(({ className, ...props }, ref) => (\n  <AccordionPrimitive.Item ref={ref} className={cn('border-b', className)} {...props} />\n))\nAccordionItem.displayName = 'AccordionItem'\n\nconst AccordionTrigger = React.forwardRef<\n  React.ElementRef<typeof AccordionPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Trigger>\n>(({ className, children, ...props }, ref) => (\n  <AccordionPrimitive.Header className='flex'>\n    <AccordionPrimitive.Trigger\n      ref={ref}\n      className={cn(\n        `\n          flex flex-1 items-center justify-between py-4 font-medium\n          transition-all\n          hover:underline\n          [&[data-state=open]>svg]:rotate-180\n        `,\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <ChevronDown\n        className={`\n        h-4 w-4 shrink-0 transition-transform duration-200\n      `}\n      />\n    </AccordionPrimitive.Trigger>\n  </AccordionPrimitive.Header>\n))\nAccordionTrigger.displayName = AccordionPrimitive.Trigger.displayName\n\nconst AccordionContent = React.forwardRef<\n  React.ElementRef<typeof AccordionPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Content>\n>(({ className, children, ...props }, ref) => (\n  <AccordionPrimitive.Content\n    ref={ref}\n    className={`\n      overflow-hidden text-sm transition-all\n      data-[state=closed]:animate-accordion-up\n      data-[state=open]:animate-accordion-down\n    `}\n    {...props}\n  >\n    <div className={cn('pt-0 pb-4', className)}>{children}</div>\n  </AccordionPrimitive.Content>\n))\n\nAccordionContent.displayName = AccordionPrimitive.Content.displayName\n\nexport { Accordion, AccordionItem, AccordionTrigger, AccordionContent }\n", "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/alert-dialog.tsx", "messages": [{"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=open]:animate-in", "line": 22, "column": 9, "nodeType": null, "endLine": 22, "endColumn": 37}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=open]:fade-in-0", "line": 22, "column": 38, "nodeType": null, "endLine": 22, "endColumn": 65}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=closed]:animate-out", "line": 23, "column": 9, "nodeType": null, "endLine": 23, "endColumn": 40}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=closed]:fade-out-0", "line": 23, "column": 41, "nodeType": null, "endLine": 23, "endColumn": 71}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=open]:animate-in", "line": 44, "column": 11, "nodeType": null, "endLine": 44, "endColumn": 39}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=open]:fade-in-0", "line": 44, "column": 40, "nodeType": null, "endLine": 44, "endColumn": 67}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=open]:zoom-in-95", "line": 45, "column": 11, "nodeType": null, "endLine": 45, "endColumn": 39}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=open]:slide-in-from-left-1/2", "line": 45, "column": 40, "nodeType": null, "endLine": 45, "endColumn": 80}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=open]:slide-in-from-top-[48%]", "line": 46, "column": 11, "nodeType": null, "endLine": 46, "endColumn": 52}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=closed]:animate-out", "line": 47, "column": 11, "nodeType": null, "endLine": 47, "endColumn": 42}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=closed]:fade-out-0", "line": 47, "column": 43, "nodeType": null, "endLine": 47, "endColumn": 73}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=closed]:zoom-out-95", "line": 48, "column": 11, "nodeType": null, "endLine": 48, "endColumn": 42}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=closed]:slide-out-to-left-1/2", "line": 49, "column": 11, "nodeType": null, "endLine": 49, "endColumn": 52}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=closed]:slide-out-to-top-[48%]", "line": 50, "column": 11, "nodeType": null, "endLine": 50, "endColumn": 53}, {"ruleId": "better-tailwindcss/multiline", "severity": 1, "message": "Unnecessary line wrapping. Expected\n\n`↵\n····text-sm·text-muted-foreground↵\n··`\n\nto be\n\n`text-sm·text-muted-foreground`", "line": 107, "column": 7, "nodeType": null, "endLine": 109, "endColumn": 4, "fix": {"range": [3368, 3407], "text": "`text-sm text-muted-foreground`"}}], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 15, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": "'use client'\n\nimport * as AlertDialogPrimitive from '@radix-ui/react-alert-dialog'\nimport * as React from 'react'\n\nimport { buttonVariants } from '@/components/ui/button'\nimport { cn } from '@/lib/utils'\n\nconst AlertDialog = AlertDialogPrimitive.Root\n\nconst AlertDialogTrigger = AlertDialogPrimitive.Trigger\n\nconst AlertDialogPortal = AlertDialogPrimitive.Portal\n\nconst AlertDialogOverlay = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Overlay>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Overlay>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPrimitive.Overlay\n    className={cn(\n      `\n        data-[state=open]:animate-in data-[state=open]:fade-in-0\n        data-[state=closed]:animate-out data-[state=closed]:fade-out-0\n        fixed inset-0 z-50 bg-black/80\n      `,\n      className\n    )}\n    {...props}\n    ref={ref}\n  />\n))\nAlertDialogOverlay.displayName = AlertDialogPrimitive.Overlay.displayName\n\nconst AlertDialogContent = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Content>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPortal>\n    <AlertDialogOverlay />\n    <AlertDialogPrimitive.Content\n      ref={ref}\n      className={cn(\n        `\n          data-[state=open]:animate-in data-[state=open]:fade-in-0\n          data-[state=open]:zoom-in-95 data-[state=open]:slide-in-from-left-1/2\n          data-[state=open]:slide-in-from-top-[48%]\n          data-[state=closed]:animate-out data-[state=closed]:fade-out-0\n          data-[state=closed]:zoom-out-95\n          data-[state=closed]:slide-out-to-left-1/2\n          data-[state=closed]:slide-out-to-top-[48%]\n          fixed top-[50%] left-[50%] z-50 grid w-full max-w-lg\n          translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6\n          shadow-lg duration-200\n          sm:rounded-lg\n        `,\n        className\n      )}\n      {...props}\n    />\n  </AlertDialogPortal>\n))\nAlertDialogContent.displayName = AlertDialogPrimitive.Content.displayName\n\nconst AlertDialogHeader = ({ className, ...props }: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      `\n        flex flex-col space-y-2 text-center\n        sm:text-left\n      `,\n      className\n    )}\n    {...props}\n  />\n)\nAlertDialogHeader.displayName = 'AlertDialogHeader'\n\nconst AlertDialogFooter = ({ className, ...props }: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      `\n        flex flex-col-reverse\n        sm:flex-row sm:justify-end sm:space-x-2\n      `,\n      className\n    )}\n    {...props}\n  />\n)\nAlertDialogFooter.displayName = 'AlertDialogFooter'\n\nconst AlertDialogTitle = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Title>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Title>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPrimitive.Title ref={ref} className={cn('text-lg font-semibold', className)} {...props} />\n))\nAlertDialogTitle.displayName = AlertDialogPrimitive.Title.displayName\n\nconst AlertDialogDescription = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Description>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Description>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPrimitive.Description\n    ref={ref}\n    className={cn(\n      `\n    text-sm text-muted-foreground\n  `,\n      className\n    )}\n    {...props}\n  />\n))\nAlertDialogDescription.displayName = AlertDialogPrimitive.Description.displayName\n\nconst AlertDialogAction = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Action>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Action>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPrimitive.Action ref={ref} className={cn(buttonVariants(), className)} {...props} />\n))\nAlertDialogAction.displayName = AlertDialogPrimitive.Action.displayName\n\nconst AlertDialogCancel = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Cancel>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Cancel>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPrimitive.Cancel\n    ref={ref}\n    className={cn(\n      buttonVariants({ variant: 'outline' }),\n      `\n        mt-2\n        sm:mt-0\n      `,\n      className\n    )}\n    {...props}\n  />\n))\nAlertDialogCancel.displayName = AlertDialogPrimitive.Cancel.displayName\n\nexport {\n  AlertDialog,\n  AlertDialogPortal,\n  AlertDialogOverlay,\n  AlertDialogTrigger,\n  AlertDialogContent,\n  AlertDialogHeader,\n  AlertDialogFooter,\n  AlertDialogTitle,\n  AlertDialogDescription,\n  AlertDialogAction,\n  AlertDialogCancel,\n}\n", "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/alert.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/aspect-ratio.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/avatar.tsx", "messages": [{"ruleId": "better-tailwindcss/multiline", "severity": 1, "message": "Unnecessary line wrapping. Expected\n\n`↵\n······relative·flex·h-10·w-10·shrink-0·overflow-hidden·rounded-full↵\n····`\n\nto be\n\n`relative·flex·h-10·w-10·shrink-0·overflow-hidden·rounded-full`", "line": 18, "column": 7, "nodeType": null, "endLine": 20, "endColumn": 6, "fix": {"range": [532, 607], "text": "`relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full`"}}, {"ruleId": "max-len", "severity": 1, "message": "This line has a length of 146. Maximum allowed is 120.", "line": 33, "column": 1, "nodeType": "Program", "messageId": "max", "endLine": 33, "endColumn": 147}, {"ruleId": "better-tailwindcss/multiline", "severity": 1, "message": "Unnecessary line wrapping. Expected\n\n`↵\n······flex·h-full·w-full·items-center·justify-center·rounded-full·bg-muted↵\n····`\n\nto be\n\n`flex·h-full·w-full·items-center·justify-center·rounded-full·bg-muted`", "line": 54, "column": 7, "nodeType": null, "endLine": 56, "endColumn": 6, "fix": {"range": [2110, 2192], "text": "`flex h-full w-full items-center justify-center rounded-full bg-muted`"}}], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 2, "source": "'use client'\n\nimport * as AvatarPrimitive from '@radix-ui/react-avatar'\nimport * as React from 'react'\n\n// Import the specialized AvatarImage variant from OptimizedImage.tsx\nimport { AvatarImage as OptimizedAvatarImageVariant } from './OptimizedImage'\n\nimport { cn } from '@/lib/utils'\n\nconst Avatar = React.forwardRef<\n  React.ElementRef<typeof AvatarPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Root>\n>(({ className, ...props }, ref) => (\n  <AvatarPrimitive.Root\n    ref={ref}\n    className={cn(\n      `\n      relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full\n    `,\n      className\n    )}\n    {...props}\n  />\n))\nAvatar.displayName = AvatarPrimitive.Root.displayName\n\n// Redefine AvatarImage as a simple functional component using the OptimizedAvatarImageVariant\n// This removes ref forwarding capability from this specific AvatarImage component.\n// Props are derived from OptimizedAvatarImageVariant's expected props.\n// OptimizedAvatarImageVariant expects Omit<OptimizedImageProps, 'imageType'>\n// OptimizedImageProps is Omit<ImageProps, 'src' | 'alt'> & { src: string; alt: string; ... }\n// So, props will be Omit<ImageProps, 'src' | 'alt' | 'imageType'> & { src: string; alt: string; ... other OptimizedImageProps without imageType }\n// For simplicity, let's use ComponentPropsWithoutRef from the imported variant.\ntype AvatarImageProps = React.ComponentPropsWithoutRef<typeof OptimizedAvatarImageVariant> & {\n  className?: string\n}\n\nconst AvatarImage = ({ className, ...props }: AvatarImageProps) => (\n  <OptimizedAvatarImageVariant\n    className={cn('aspect-square h-full w-full', className)}\n    {...props} // Spread remaining props, src and alt must be provided by the user\n  />\n)\nAvatarImage.displayName = AvatarPrimitive.Image.displayName // Or a new name like \"PonyClubOptimizedAvatarImage\"\n\nconst AvatarFallback = React.forwardRef<\n  React.ElementRef<typeof AvatarPrimitive.Fallback>,\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Fallback>\n>(({ className, ...props }, ref) => (\n  <AvatarPrimitive.Fallback\n    ref={ref}\n    className={cn(\n      `\n      flex h-full w-full items-center justify-center rounded-full bg-muted\n    `,\n      className\n    )}\n    {...props}\n  />\n))\nAvatarFallback.displayName = AvatarPrimitive.Fallback.displayName\n\nexport { Avatar, AvatarImage, AvatarFallback }\n", "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/badge.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/border-beam.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/breadcrumb.tsx", "messages": [{"ruleId": "better-tailwindcss/multiline", "severity": 1, "message": "Unnecessary line wrapping. Expected\n\n`↵\n····[&>svg]:h-3.5·[&>svg]:w-3.5↵\n··`\n\nto be\n\n`[&>svg]:h-3.5·[&>svg]:w-3.5`", "line": 83, "column": 7, "nodeType": null, "endLine": 85, "endColumn": 4, "fix": {"range": [2129, 2166], "text": "`[&>svg]:h-3.5 [&>svg]:w-3.5`"}}], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": "import { Slot } from '@radix-ui/react-slot'\nimport { ChevronRight, MoreHorizontal } from 'lucide-react'\nimport * as React from 'react'\n\nimport { cn } from '@/lib/utils'\n\nconst Breadcrumb = React.forwardRef<\n  HTMLElement,\n  React.ComponentPropsWithoutRef<'nav'> & {\n    separator?: React.ReactNode\n  }\n>(({ ...props }, ref) => <nav ref={ref} aria-label='breadcrumb' {...props} />)\nBreadcrumb.displayName = 'Breadcrumb'\n\nconst BreadcrumbList = React.forwardRef<HTMLOListElement, React.ComponentPropsWithoutRef<'ol'>>(\n  ({ className, ...props }, ref) => (\n    <ol\n      ref={ref}\n      className={cn(\n        `\n          flex flex-wrap items-center gap-1.5 text-sm break-words\n          text-muted-foreground\n          sm:gap-2.5\n        `,\n        className\n      )}\n      {...props}\n    />\n  )\n)\nBreadcrumbList.displayName = 'BreadcrumbList'\n\nconst BreadcrumbItem = React.forwardRef<HTMLLIElement, React.ComponentPropsWithoutRef<'li'>>(\n  ({ className, ...props }, ref) => (\n    <li ref={ref} className={cn('inline-flex items-center gap-1.5', className)} {...props} />\n  )\n)\nBreadcrumbItem.displayName = 'BreadcrumbItem'\n\nconst BreadcrumbLink = React.forwardRef<\n  HTMLAnchorElement,\n  React.ComponentPropsWithoutRef<'a'> & {\n    asChild?: boolean\n  }\n>(({ asChild, className, ...props }, ref) => {\n  const Comp = asChild ? Slot : 'a'\n\n  return (\n    <Comp\n      ref={ref}\n      className={cn(\n        `\n          transition-colors\n          hover:text-foreground\n        `,\n        className\n      )}\n      {...props}\n    />\n  )\n})\nBreadcrumbLink.displayName = 'BreadcrumbLink'\n\nconst BreadcrumbPage = React.forwardRef<HTMLSpanElement, React.ComponentPropsWithoutRef<'span'>>(\n  ({ className, ...props }, ref) => (\n    <span\n      ref={ref}\n      role='link'\n      aria-disabled='true'\n      aria-current='page'\n      className={cn('font-normal text-foreground', className)}\n      {...props}\n    />\n  )\n)\nBreadcrumbPage.displayName = 'BreadcrumbPage'\n\nconst BreadcrumbSeparator = ({ children, className, ...props }: React.ComponentProps<'li'>) => (\n  <li\n    role='presentation'\n    aria-hidden='true'\n    className={cn(\n      `\n    [&>svg]:h-3.5 [&>svg]:w-3.5\n  `,\n      className\n    )}\n    {...props}\n  >\n    {children ?? <ChevronRight />}\n  </li>\n)\nBreadcrumbSeparator.displayName = 'BreadcrumbSeparator'\n\nconst BreadcrumbEllipsis = ({ className, ...props }: React.ComponentProps<'span'>) => (\n  <span\n    role='presentation'\n    aria-hidden='true'\n    className={cn('flex h-9 w-9 items-center justify-center', className)}\n    {...props}\n  >\n    <MoreHorizontal className='h-4 w-4' />\n    <span className='sr-only'>More</span>\n  </span>\n)\nBreadcrumbEllipsis.displayName = 'BreadcrumbElipssis'\n\nexport {\n  Breadcrumb,\n  BreadcrumbList,\n  BreadcrumbItem,\n  BreadcrumbLink,\n  BreadcrumbPage,\n  BreadcrumbSeparator,\n  BreadcrumbEllipsis,\n}\n", "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/button.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/calendar.tsx", "messages": [{"ruleId": "@typescript-eslint/no-unused-vars", "severity": 1, "message": "'CustomComponents' is defined but never used. Allowed unused vars must match /^_/u.", "line": 6, "column": 26, "nodeType": null, "messageId": "unusedVar", "endLine": 6, "endColumn": 42}], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "'use client'\n\nimport { ChevronLeft, ChevronRight } from 'lucide-react'\nimport * as React from 'react'\nimport type { SVGProps } from 'react'\nimport { DayPicker, type CustomComponents } from 'react-day-picker' // Import CustomComponents\n\nimport { buttonVariants } from '@/components/ui/button'\nimport { cn } from '@/lib/utils'\n\nexport type CalendarProps = React.ComponentProps<typeof DayPicker>\n\nfunction Calendar({ className, classNames, showOutsideDays = true, ...props }: CalendarProps) {\n  interface ChevronComponentProps extends SVGProps<SVGSVGElement> {\n    orientation?: 'left' | 'right' | 'up' | 'down' // Updated to match DayPicker's expected type\n  }\n\n  const ChevronComponent = ({ orientation, ...restProps }: ChevronComponentProps) => {\n    if (orientation === 'left') {\n      return <ChevronLeft className='h-4 w-4' {...restProps} />\n    }\n    return <ChevronRight className='h-4 w-4' {...restProps} />\n  }\n\n  return (\n    <DayPicker\n      showOutsideDays={showOutsideDays}\n      className={cn('p-3', className)}\n      classNames={{\n        months: 'flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0',\n        month: 'space-y-4',\n        caption: 'flex justify-center pt-1 relative items-center',\n        caption_label: 'text-sm font-medium',\n        nav: 'space-x-1 flex items-center',\n        nav_button: cn(\n          buttonVariants({ variant: 'outline' }),\n          `\n            h-7 w-7 bg-transparent p-0 opacity-50\n            hover:opacity-100\n          `\n        ),\n        nav_button_previous: 'absolute left-1',\n        nav_button_next: 'absolute right-1',\n        table: 'w-full border-collapse space-y-1',\n        head_row: 'flex',\n        head_cell: 'text-muted-foreground rounded-md w-9 font-normal text-[0.8rem]',\n        row: 'flex w-full mt-2',\n        cell: 'h-9 w-9 text-center text-sm p-0 relative [&:has([aria-selected].day-range-end)]:rounded-r-md [&:has([aria-selected].day-outside)]:bg-accent/50 [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20',\n        day: cn(\n          buttonVariants({ variant: 'ghost' }),\n          `\n            h-9 w-9 p-0 font-normal\n            aria-selected:opacity-100\n          `\n        ),\n        day_range_end: 'day-range-end',\n        day_selected:\n          'bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground',\n        day_today: 'bg-accent text-accent-foreground',\n        day_outside: 'day-outside text-muted-foreground aria-selected:bg-accent/50 aria-selected:text-muted-foreground',\n        day_disabled: 'text-muted-foreground opacity-50',\n        day_range_middle: 'aria-selected:bg-accent aria-selected:text-accent-foreground',\n        day_hidden: 'invisible',\n        ...classNames,\n      }}\n      components={{\n        Chevron: ChevronComponent,\n      }}\n      {...props}\n    />\n  )\n}\nCalendar.displayName = 'Calendar'\n\nexport { Calendar }\n", "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/card.tsx", "messages": [{"ruleId": "better-tailwindcss/multiline", "severity": 1, "message": "Unnecessary line wrapping. Expected\n\n`↵\n····rounded-lg·border·bg-card·text-card-foreground·shadow-xs↵\n··`\n\nto be\n\n`rounded-lg·border·bg-card·text-card-foreground·shadow-xs`", "line": 9, "column": 7, "nodeType": null, "endLine": 11, "endColumn": 4, "fix": {"range": [232, 298], "text": "`rounded-lg border bg-card text-card-foreground shadow-xs`"}}, {"ruleId": "better-tailwindcss/multiline", "severity": 1, "message": "Unnecessary line wrapping. Expected\n\n`↵\n······text-2xl·leading-none·font-semibold·tracking-tight↵\n····`\n\nto be\n\n`text-2xl·leading-none·font-semibold·tracking-tight`", "line": 31, "column": 9, "nodeType": null, "endLine": 33, "endColumn": 6, "fix": {"range": [821, 885], "text": "`text-2xl leading-none font-semibold tracking-tight`"}}, {"ruleId": "better-tailwindcss/multiline", "severity": 1, "message": "Unnecessary line wrapping. Expected\n\n`↵\n····flex·items-center·p-6·pt-0↵\n··`\n\nto be\n\n`flex·items-center·p-6·pt-0`", "line": 59, "column": 9, "nodeType": null, "endLine": 61, "endColumn": 4, "fix": {"range": [1683, 1719], "text": "`flex items-center p-6 pt-0`"}}], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 3, "source": "import * as React from 'react'\n\nimport { cn } from '@/lib/utils'\n\nconst Card = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      `\n    rounded-lg border bg-card text-card-foreground shadow-xs\n  `,\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = 'Card'\n\nconst CardHeader = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => (\n    <div ref={ref} className={cn('flex flex-col space-y-1.5 p-6', className)} {...props} />\n  )\n)\nCardHeader.displayName = 'CardHeader'\n\nconst CardTitle = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn(\n        `\n      text-2xl leading-none font-semibold tracking-tight\n    `,\n        className\n      )}\n      {...props}\n    />\n  )\n)\nCardTitle.displayName = 'CardTitle'\n\nconst CardDescription = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => (\n    <div ref={ref} className={cn('text-sm text-muted-foreground', className)} {...props} />\n  )\n)\nCardDescription.displayName = 'CardDescription'\n\nconst CardContent = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => <div ref={ref} className={cn('p-6 pt-0', className)} {...props} />\n)\nCardContent.displayName = 'CardContent'\n\nconst CardFooter = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn(\n        `\n    flex items-center p-6 pt-0\n  `,\n        className\n      )}\n      {...props}\n    />\n  )\n)\nCardFooter.displayName = 'CardFooter'\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n", "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/carousel.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/chart.tsx", "messages": [{"ruleId": "@typescript-eslint/no-unused-vars", "severity": 1, "message": "'NameType' is defined but never used. Allowed unused vars must match /^_/u.", "line": 5, "column": 10, "nodeType": null, "messageId": "unusedVar", "endLine": 5, "endColumn": 18}, {"ruleId": "@typescript-eslint/no-unused-vars", "severity": 1, "message": "'Payload' is defined but never used. Allowed unused vars must match /^_/u.", "line": 5, "column": 20, "nodeType": null, "messageId": "unusedVar", "endLine": 5, "endColumn": 27}, {"ruleId": "@typescript-eslint/no-unused-vars", "severity": 1, "message": "'ValueType' is defined but never used. Allowed unused vars must match /^_/u.", "line": 5, "column": 29, "nodeType": null, "messageId": "unusedVar", "endLine": 5, "endColumn": 38}, {"ruleId": "complexity", "severity": 1, "message": "Arrow function has a complexity of 20. Maximum allowed is 15.", "line": 192, "column": 24, "nodeType": "ArrowFunctionExpression", "messageId": "complex", "endLine": 262, "endColumn": 12}], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "'use client'\n\nimport * as React from 'react'\nimport * as RechartsPrimitive from 'recharts'\nimport { NameType, Payload, ValueType } from 'recharts/types/component/DefaultTooltipContent'\n\nimport { cn } from '@/lib/utils'\n\n// Format: { THEME_NAME: CSS_SELECTOR }\nconst THEMES = { light: '', dark: '.dark' } as const\n\nexport type ChartConfig = {\n  [k in string]: {\n    label?: React.ReactNode\n    icon?: React.ComponentType\n  } & ({ color?: string; theme?: never } | { color?: never; theme: Record<keyof typeof THEMES, string> })\n}\n\ntype ChartContextProps = {\n  config: ChartConfig\n}\n\nconst ChartContext = React.createContext<ChartContextProps | null>(null)\n\nfunction useChart() {\n  const context = React.useContext(ChartContext)\n\n  if (!context) {\n    throw new Error('useChart must be used within a <ChartContainer />')\n  }\n\n  return context\n}\n\nconst ChartContainer = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<'div'> & {\n    config: ChartConfig\n    children: React.ComponentProps<typeof RechartsPrimitive.ResponsiveContainer>['children']\n  }\n>(({ id, className, children, config, ...props }, ref) => {\n  const uniqueId = React.useId()\n  const chartId = `chart-${id || uniqueId.replace(/:/g, '')}`\n\n  return (\n    <ChartContext.Provider value={{ config }}>\n      <div\n        data-chart={chartId}\n        ref={ref}\n        className={cn(\n          `\n            flex aspect-video justify-center text-xs\n            [&_.recharts-cartesian-axis-tick_text]:fill-muted-foreground\n            [&_.recharts-cartesian-grid_line[stroke='#ccc']]:stroke-border/50\n            [&_.recharts-curve.recharts-tooltip-cursor]:stroke-border\n            [&_.recharts-dot[stroke='#fff']]:stroke-transparent\n            [&_.recharts-layer]:outline-hidden\n            [&_.recharts-polar-grid_[stroke='#ccc']]:stroke-border\n            [&_.recharts-radial-bar-background-sector]:fill-muted\n            [&_.recharts-rectangle.recharts-tooltip-cursor]:fill-muted\n            [&_.recharts-reference-line_[stroke='#ccc']]:stroke-border\n            [&_.recharts-sector]:outline-hidden\n            [&_.recharts-sector[stroke='#fff']]:stroke-transparent\n            [&_.recharts-surface]:outline-hidden\n          `,\n          className\n        )}\n        {...props}\n      >\n        <ChartStyle id={chartId} config={config} />\n        <RechartsPrimitive.ResponsiveContainer>{children}</RechartsPrimitive.ResponsiveContainer>\n      </div>\n    </ChartContext.Provider>\n  )\n})\nChartContainer.displayName = 'Chart'\n\nconst ChartStyle = ({ id, config }: { id: string; config: ChartConfig }) => {\n  const [nonce, setNonce] = React.useState('')\n\n  React.useEffect(() => {\n    // Get nonce from meta tag\n    const metaNonce = document.querySelector('meta[name=\"csp-nonce\"]')?.getAttribute('content') || ''\n    setNonce(metaNonce)\n  }, [])\n\n  const colorConfig = Object.entries(config).filter(([_, config]) => config.theme || config.color)\n\n  if (!colorConfig.length) {\n    return null\n  }\n\n  return (\n    <style\n      nonce={nonce}\n      dangerouslySetInnerHTML={{\n        __html: Object.entries(THEMES)\n          .map(\n            ([theme, prefix]) => `\n${prefix} [data-chart=${id}] {\n${colorConfig\n  .map(([key, itemConfig]) => {\n    const color = itemConfig.theme?.[theme as keyof typeof itemConfig.theme] || itemConfig.color\n    return color ? `  --color-${key}: ${color};` : null\n  })\n  .join('\\n')}\n}\n`\n          )\n          .join('\\n'),\n      }}\n    />\n  )\n}\n\nconst ChartTooltip = RechartsPrimitive.Tooltip\n\nconst ChartTooltipContent = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<typeof RechartsPrimitive.Tooltip> &\n    React.ComponentProps<'div'> & {\n      hideLabel?: boolean\n      hideIndicator?: boolean\n      indicator?: 'line' | 'dot' | 'dashed'\n      nameKey?: string\n      labelKey?: string\n    }\n>(\n  (\n    {\n      active,\n      payload,\n      className,\n      indicator = 'dot',\n      hideLabel = false,\n      hideIndicator = false,\n      label,\n      labelFormatter,\n      labelClassName,\n      formatter,\n      color,\n      nameKey,\n      labelKey,\n    },\n    ref\n  ) => {\n    const { config } = useChart()\n\n    const tooltipLabel = React.useMemo(() => {\n      if (hideLabel || !payload?.length) {\n        return null\n      }\n\n      const [item] = payload\n      const key = `${labelKey || item.dataKey || item.name || 'value'}`\n      const itemConfig = getPayloadConfigFromPayload(config, item, key)\n      const value =\n        !labelKey && typeof label === 'string'\n          ? config[label as keyof typeof config]?.label || label\n          : itemConfig?.label\n\n      if (labelFormatter) {\n        return <div className={cn('font-medium', labelClassName)}>{labelFormatter(value, payload)}</div>\n      }\n\n      if (!value) {\n        return null\n      }\n\n      return <div className={cn('font-medium', labelClassName)}>{value}</div>\n    }, [label, labelFormatter, payload, hideLabel, labelClassName, config, labelKey])\n\n    if (!active || !payload?.length) {\n      return null\n    }\n\n    const nestLabel = payload.length === 1 && indicator !== 'dot'\n\n    return (\n      <div\n        ref={ref}\n        className={cn(\n          `\n            grid min-w-[8rem] items-start gap-1.5 rounded-lg border\n            border-border/50 bg-background px-2.5 py-1.5 text-xs shadow-xl\n          `,\n          className\n        )}\n      >\n        {!nestLabel ? tooltipLabel : null}\n        <div className='grid gap-1.5'>\n          {payload.map((item, index) => {\n            const key = `${nameKey || item.name || item.dataKey || 'value'}`\n            const itemConfig = getPayloadConfigFromPayload(config, item, key)\n            const indicatorColor = color || item.payload.fill || item.color\n\n            return (\n              <div\n                key={item.dataKey}\n                className={cn(\n                  `\n                    flex w-full flex-wrap items-stretch gap-2\n                    [&>svg]:h-2.5 [&>svg]:w-2.5 [&>svg]:text-muted-foreground\n                  `,\n                  indicator === 'dot' && 'items-center'\n                )}\n              >\n                {formatter && item?.value !== undefined && item.name ? (\n                  formatter(item.value, item.name, item, index, item.payload)\n                ) : (\n                  <>\n                    {itemConfig?.icon ? (\n                      <itemConfig.icon />\n                    ) : (\n                      !hideIndicator && (\n                        <div\n                          className={cn(\n                            `\n                              shrink-0 rounded-[2px] border-(--color-border)\n                              bg-(--color-bg)\n                            `,\n                            {\n                              'h-2.5 w-2.5': indicator === 'dot',\n                              'w-1': indicator === 'line',\n                              'w-0 border-[1.5px] border-dashed bg-transparent': indicator === 'dashed',\n                              'my-0.5': nestLabel && indicator === 'dashed',\n                            }\n                          )}\n                          style={\n                            {\n                              '--color-bg': indicatorColor,\n                              '--color-border': indicatorColor,\n                            } as React.CSSProperties\n                          }\n                        />\n                      )\n                    )}\n                    <div\n                      className={cn(\n                        'flex flex-1 justify-between leading-none',\n                        nestLabel ? 'items-end' : 'items-center'\n                      )}\n                    >\n                      <div className='grid gap-1.5'>\n                        {nestLabel ? tooltipLabel : null}\n                        <span className='text-muted-foreground'>{itemConfig?.label || item.name}</span>\n                      </div>\n                      {item.value && (\n                        <span\n                          className={`\n                            font-mono font-medium text-foreground tabular-nums\n                          `}\n                        >\n                          {item.value.toLocaleString()}\n                        </span>\n                      )}\n                    </div>\n                  </>\n                )}\n              </div>\n            )\n          })}\n        </div>\n      </div>\n    )\n  }\n)\nChartTooltipContent.displayName = 'ChartTooltip'\n\nconst ChartLegend = RechartsPrimitive.Legend\n\nconst ChartLegendContent = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<'div'> &\n    Pick<RechartsPrimitive.LegendProps, 'payload' | 'verticalAlign'> & {\n      hideIcon?: boolean\n      nameKey?: string\n    }\n>(({ className, hideIcon = false, payload, verticalAlign = 'bottom', nameKey }, ref) => {\n  const { config } = useChart()\n\n  if (!payload?.length) {\n    return null\n  }\n\n  return (\n    <div\n      ref={ref}\n      className={cn('flex items-center justify-center gap-4', verticalAlign === 'top' ? 'pb-3' : 'pt-3', className)}\n    >\n      {payload.map(item => {\n        const key = `${nameKey || item.dataKey || 'value'}`\n        const itemConfig = getPayloadConfigFromPayload(config, item, key)\n\n        return (\n          <div\n            key={item.value}\n            className={cn(`\n              flex items-center gap-1.5\n              [&>svg]:h-3 [&>svg]:w-3 [&>svg]:text-muted-foreground\n            `)}\n          >\n            {itemConfig?.icon && !hideIcon ? (\n              <itemConfig.icon />\n            ) : (\n              <div\n                className='h-2 w-2 shrink-0 rounded-[2px]'\n                style={{\n                  backgroundColor: item.color,\n                }}\n              />\n            )}\n            {itemConfig?.label}\n          </div>\n        )\n      })}\n    </div>\n  )\n})\nChartLegendContent.displayName = 'ChartLegend'\n\n// Helper to extract item config from a payload.\nfunction getPayloadConfigFromPayload(config: ChartConfig, payload: unknown, key: string) {\n  if (typeof payload !== 'object' || payload === null) {\n    return undefined\n  }\n\n  const payloadPayload =\n    'payload' in payload && typeof payload.payload === 'object' && payload.payload !== null\n      ? payload.payload\n      : undefined\n\n  let configLabelKey: string = key\n\n  if (key in payload && typeof payload[key as keyof typeof payload] === 'string') {\n    configLabelKey = payload[key as keyof typeof payload] as string\n  } else if (\n    payloadPayload &&\n    key in payloadPayload &&\n    typeof payloadPayload[key as keyof typeof payloadPayload] === 'string'\n  ) {\n    configLabelKey = payloadPayload[key as keyof typeof payloadPayload] as string\n  }\n\n  return configLabelKey in config ? config[configLabelKey] : config[key as keyof typeof config]\n}\n\nexport { ChartContainer, ChartTooltip, ChartTooltipContent, ChartLegend, ChartLegendContent, ChartStyle }\n", "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/checkbox.tsx", "messages": [{"ruleId": "better-tailwindcss/multiline", "severity": 1, "message": "Unnecessary line wrapping. Expected\n\n`↵\n······flex·items-center·justify-center·text-current↵\n····`\n\nto be\n\n`flex·items-center·justify-center·text-current`", "line": 30, "column": 21, "nodeType": null, "endLine": 32, "endColumn": 6, "fix": {"range": [899, 958], "text": "`flex items-center justify-center text-current`"}}], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": "'use client'\n\nimport * as CheckboxPrimitive from '@radix-ui/react-checkbox'\nimport { Check } from 'lucide-react'\nimport * as React from 'react'\n\nimport { cn } from '@/lib/utils'\n\nconst Checkbox = React.forwardRef<\n  React.ElementRef<typeof CheckboxPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof CheckboxPrimitive.Root>\n>(({ className, ...props }, ref) => (\n  <CheckboxPrimitive.Root\n    ref={ref}\n    className={cn(\n      `\n        peer h-4 w-4 shrink-0 rounded-sm border border-primary\n        ring-offset-background\n        focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\n        focus-visible:outline-hidden\n        disabled:cursor-not-allowed disabled:opacity-50\n        data-[state=checked]:bg-primary\n        data-[state=checked]:text-primary-foreground\n      `,\n      className\n    )}\n    {...props}\n  >\n    <CheckboxPrimitive.Indicator\n      className={cn(`\n      flex items-center justify-center text-current\n    `)}\n    >\n      <Check className='h-4 w-4' />\n    </CheckboxPrimitive.Indicator>\n  </CheckboxPrimitive.Root>\n))\nCheckbox.displayName = CheckboxPrimitive.Root.displayName\n\nexport { Checkbox }\n", "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/collapsible.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/command.tsx", "messages": [{"ruleId": "better-tailwindcss/multiline", "severity": 1, "message": "Unnecessary line wrapping. Expected\n\n`↵\n··py-6·text-center·text-sm↵\n`\n\nto be\n\n`py-6·text-center·text-sm`", "line": 94, "column": 16, "nodeType": null, "endLine": 96, "endColumn": 2, "fix": {"range": [2855, 2885], "text": "`py-6 text-center text-sm`"}}, {"ruleId": "better-tailwindcss/multiline", "severity": 1, "message": "Unnecessary line wrapping. Expected\n\n`↵\n····ml-auto·text-xs·tracking-widest·text-muted-foreground↵\n··`\n\nto be\n\n`ml-auto·text-xs·tracking-widest·text-muted-foreground`", "line": 159, "column": 9, "nodeType": null, "endLine": 161, "endColumn": 4, "fix": {"range": [4861, 4924], "text": "`ml-auto text-xs tracking-widest text-muted-foreground`"}}], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 2, "source": "'use client'\n\nimport { type DialogProps } from '@radix-ui/react-dialog'\nimport { Command as CommandPrimitive } from 'cmdk'\nimport { Search } from 'lucide-react'\nimport * as React from 'react'\n\nimport { Dialog, DialogContent } from '@/components/ui/dialog'\nimport { cn } from '@/lib/utils'\n\nconst Command = React.forwardRef<\n  React.ElementRef<typeof CommandPrimitive>,\n  React.ComponentPropsWithoutRef<typeof CommandPrimitive>\n>(({ className, ...props }, ref) => (\n  <CommandPrimitive\n    ref={ref}\n    className={cn(\n      `\n        flex h-full w-full flex-col overflow-hidden rounded-md bg-popover\n        text-popover-foreground\n      `,\n      className\n    )}\n    {...props}\n  />\n))\nCommand.displayName = CommandPrimitive.displayName\n\nconst CommandDialog = ({ children, ...props }: DialogProps) => {\n  return (\n    <Dialog {...props}>\n      <DialogContent className='overflow-hidden p-0 shadow-lg'>\n        <Command\n          className={`\n            [&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:font-medium\n            [&_[cmdk-group-heading]]:text-muted-foreground\n            [&_[cmdk-group]]:px-2\n            [&_[cmdk-group]:not([hidden])_~[cmdk-group]]:pt-0\n            [&_[cmdk-input-wrapper]_svg]:h-5 [&_[cmdk-input-wrapper]_svg]:w-5\n            [&_[cmdk-input]]:h-12\n            [&_[cmdk-item]]:px-2 [&_[cmdk-item]]:py-3\n            [&_[cmdk-item]_svg]:h-5 [&_[cmdk-item]_svg]:w-5\n          `}\n        >\n          {children}\n        </Command>\n      </DialogContent>\n    </Dialog>\n  )\n}\n\nconst CommandInput = React.forwardRef<\n  React.ElementRef<typeof CommandPrimitive.Input>,\n  React.ComponentPropsWithoutRef<typeof CommandPrimitive.Input>\n>(({ className, ...props }, ref) => (\n  <div className='flex items-center border-b px-3' cmdk-input-wrapper=''>\n    <Search className='mr-2 h-4 w-4 shrink-0 opacity-50' />\n    <CommandPrimitive.Input\n      ref={ref}\n      className={cn(\n        `\n          flex h-11 w-full rounded-md bg-transparent py-3 text-sm outline-hidden\n          placeholder:text-muted-foreground\n          disabled:cursor-not-allowed disabled:opacity-50\n        `,\n        className\n      )}\n      {...props}\n    />\n  </div>\n))\n\nCommandInput.displayName = CommandPrimitive.Input.displayName\n\nconst CommandList = React.forwardRef<\n  React.ElementRef<typeof CommandPrimitive.List>,\n  React.ComponentPropsWithoutRef<typeof CommandPrimitive.List>\n>(({ className, ...props }, ref) => (\n  <CommandPrimitive.List\n    ref={ref}\n    className={cn('max-h-[300px] overflow-x-hidden overflow-y-auto', className)}\n    {...props}\n  />\n))\n\nCommandList.displayName = CommandPrimitive.List.displayName\n\nconst CommandEmpty = React.forwardRef<\n  React.ElementRef<typeof CommandPrimitive.Empty>,\n  React.ComponentPropsWithoutRef<typeof CommandPrimitive.Empty>\n>((props, ref) => (\n  <CommandPrimitive.Empty\n    ref={ref}\n    className={`\n  py-6 text-center text-sm\n`}\n    {...props}\n  />\n))\n\nCommandEmpty.displayName = CommandPrimitive.Empty.displayName\n\nconst CommandGroup = React.forwardRef<\n  React.ElementRef<typeof CommandPrimitive.Group>,\n  React.ComponentPropsWithoutRef<typeof CommandPrimitive.Group>\n>(({ className, ...props }, ref) => (\n  <CommandPrimitive.Group\n    ref={ref}\n    className={cn(\n      `\n        overflow-hidden p-1 text-foreground\n        [&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:py-1.5\n        [&_[cmdk-group-heading]]:text-xs [&_[cmdk-group-heading]]:font-medium\n        [&_[cmdk-group-heading]]:text-muted-foreground\n      `,\n      className\n    )}\n    {...props}\n  />\n))\n\nCommandGroup.displayName = CommandPrimitive.Group.displayName\n\nconst CommandSeparator = React.forwardRef<\n  React.ElementRef<typeof CommandPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof CommandPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <CommandPrimitive.Separator ref={ref} className={cn('-mx-1 h-px bg-border', className)} {...props} />\n))\nCommandSeparator.displayName = CommandPrimitive.Separator.displayName\n\nconst CommandItem = React.forwardRef<\n  React.ElementRef<typeof CommandPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof CommandPrimitive.Item>\n>(({ className, ...props }, ref) => (\n  <CommandPrimitive.Item\n    ref={ref}\n    className={cn(\n      `\n        relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5\n        text-sm outline-hidden select-none\n        data-[disabled=true]:pointer-events-none data-[disabled=true]:opacity-50\n        data-[selected='true']:bg-accent\n        data-[selected=true]:text-accent-foreground\n        [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\n      `,\n      className\n    )}\n    {...props}\n  />\n))\n\nCommandItem.displayName = CommandPrimitive.Item.displayName\n\nconst CommandShortcut = ({ className, ...props }: React.HTMLAttributes<HTMLSpanElement>) => {\n  return (\n    <span\n      className={cn(\n        `\n    ml-auto text-xs tracking-widest text-muted-foreground\n  `,\n        className\n      )}\n      {...props}\n    />\n  )\n}\nCommandShortcut.displayName = 'CommandShortcut'\n\nexport {\n  Command,\n  CommandDialog,\n  CommandInput,\n  CommandList,\n  CommandEmpty,\n  CommandGroup,\n  CommandItem,\n  CommandShortcut,\n  CommandSeparator,\n}\n", "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/context-menu.tsx", "messages": [{"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=open]:animate-in", "line": 55, "column": 9, "nodeType": null, "endLine": 55, "endColumn": 37}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=open]:fade-in-0", "line": 55, "column": 38, "nodeType": null, "endLine": 55, "endColumn": 65}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=open]:zoom-in-95", "line": 56, "column": 9, "nodeType": null, "endLine": 56, "endColumn": 37}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=closed]:animate-out", "line": 57, "column": 9, "nodeType": null, "endLine": 57, "endColumn": 40}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=closed]:fade-out-0", "line": 57, "column": 41, "nodeType": null, "endLine": 57, "endColumn": 71}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=closed]:zoom-out-95", "line": 58, "column": 9, "nodeType": null, "endLine": 58, "endColumn": 40}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[side=bottom]:slide-in-from-top-2", "line": 59, "column": 9, "nodeType": null, "endLine": 59, "endColumn": 47}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[side=left]:slide-in-from-right-2", "line": 60, "column": 9, "nodeType": null, "endLine": 60, "endColumn": 47}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[side=right]:slide-in-from-left-2", "line": 61, "column": 9, "nodeType": null, "endLine": 61, "endColumn": 47}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[side=top]:slide-in-from-bottom-2", "line": 62, "column": 9, "nodeType": null, "endLine": 62, "endColumn": 47}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: animate-in", "line": 82, "column": 11, "nodeType": null, "endLine": 82, "endColumn": 21}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: fade-in-80", "line": 82, "column": 22, "nodeType": null, "endLine": 82, "endColumn": 32}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=open]:animate-in", "line": 84, "column": 11, "nodeType": null, "endLine": 84, "endColumn": 39}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=open]:fade-in-0", "line": 84, "column": 40, "nodeType": null, "endLine": 84, "endColumn": 67}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=open]:zoom-in-95", "line": 85, "column": 11, "nodeType": null, "endLine": 85, "endColumn": 39}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=closed]:animate-out", "line": 86, "column": 11, "nodeType": null, "endLine": 86, "endColumn": 42}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=closed]:fade-out-0", "line": 86, "column": 43, "nodeType": null, "endLine": 86, "endColumn": 73}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=closed]:zoom-out-95", "line": 87, "column": 11, "nodeType": null, "endLine": 87, "endColumn": 42}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[side=bottom]:slide-in-from-top-2", "line": 88, "column": 11, "nodeType": null, "endLine": 88, "endColumn": 49}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[side=left]:slide-in-from-right-2", "line": 89, "column": 11, "nodeType": null, "endLine": 89, "endColumn": 49}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[side=right]:slide-in-from-left-2", "line": 90, "column": 11, "nodeType": null, "endLine": 90, "endColumn": 49}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[side=top]:slide-in-from-bottom-2", "line": 91, "column": 11, "nodeType": null, "endLine": 91, "endColumn": 49}, {"ruleId": "better-tailwindcss/multiline", "severity": 1, "message": "Unnecessary line wrapping. Expected\n\n`↵\n······absolute·left-2·flex·h-3.5·w-3.5·items-center·justify-center↵\n····`\n\nto be\n\n`absolute·left-2·flex·h-3.5·w-3.5·items-center·justify-center`", "line": 143, "column": 18, "nodeType": null, "endLine": 145, "endColumn": 6, "fix": {"range": [4725, 4799], "text": "`absolute left-2 flex h-3.5 w-3.5 items-center justify-center`"}}, {"ruleId": "better-tailwindcss/multiline", "severity": 1, "message": "Unnecessary line wrapping. Expected\n\n`↵\n······absolute·left-2·flex·h-3.5·w-3.5·items-center·justify-center↵\n····`\n\nto be\n\n`absolute·left-2·flex·h-3.5·w-3.5·items-center·justify-center`", "line": 174, "column": 18, "nodeType": null, "endLine": 176, "endColumn": 6, "fix": {"range": [5706, 5780], "text": "`absolute left-2 flex h-3.5 w-3.5 items-center justify-center`"}}, {"ruleId": "better-tailwindcss/multiline", "severity": 1, "message": "Unnecessary line wrapping. Expected\n\n`↵\n····-mx-1·my-1·h-px·bg-border↵\n··`\n\nto be\n\n`-mx-1·my-1·h-px·bg-border`", "line": 208, "column": 7, "nodeType": null, "endLine": 210, "endColumn": 4, "fix": {"range": [6838, 6873], "text": "`-mx-1 my-1 h-px bg-border`"}}, {"ruleId": "better-tailwindcss/multiline", "severity": 1, "message": "Unnecessary line wrapping. Expected\n\n`↵\n····ml-auto·text-xs·tracking-widest·text-muted-foreground↵\n··`\n\nto be\n\n`ml-auto·text-xs·tracking-widest·text-muted-foreground`", "line": 222, "column": 9, "nodeType": null, "endLine": 224, "endColumn": 4, "fix": {"range": [7148, 7211], "text": "`ml-auto text-xs tracking-widest text-muted-foreground`"}}], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 26, "fixableErrorCount": 0, "fixableWarningCount": 4, "source": "'use client'\n\nimport * as ContextMenuPrimitive from '@radix-ui/react-context-menu'\nimport { Check, ChevronRight, Circle } from 'lucide-react'\nimport * as React from 'react'\n\nimport { cn } from '@/lib/utils'\n\nconst ContextMenu = ContextMenuPrimitive.Root\n\nconst ContextMenuTrigger = ContextMenuPrimitive.Trigger\n\nconst ContextMenuGroup = ContextMenuPrimitive.Group\n\nconst ContextMenuPortal = ContextMenuPrimitive.Portal\n\nconst ContextMenuSub = ContextMenuPrimitive.Sub\n\nconst ContextMenuRadioGroup = ContextMenuPrimitive.RadioGroup\n\nconst ContextMenuSubTrigger = React.forwardRef<\n  React.ElementRef<typeof ContextMenuPrimitive.SubTrigger>,\n  React.ComponentPropsWithoutRef<typeof ContextMenuPrimitive.SubTrigger> & {\n    inset?: boolean\n  }\n>(({ className, inset, children, ...props }, ref) => (\n  <ContextMenuPrimitive.SubTrigger\n    ref={ref}\n    className={cn(\n      `\n        flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm\n        outline-hidden select-none\n        focus:bg-accent focus:text-accent-foreground\n        data-[state=open]:bg-accent data-[state=open]:text-accent-foreground\n      `,\n      inset && 'pl-8',\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <ChevronRight className='ml-auto h-4 w-4' />\n  </ContextMenuPrimitive.SubTrigger>\n))\nContextMenuSubTrigger.displayName = ContextMenuPrimitive.SubTrigger.displayName\n\nconst ContextMenuSubContent = React.forwardRef<\n  React.ElementRef<typeof ContextMenuPrimitive.SubContent>,\n  React.ComponentPropsWithoutRef<typeof ContextMenuPrimitive.SubContent>\n>(({ className, ...props }, ref) => (\n  <ContextMenuPrimitive.SubContent\n    ref={ref}\n    className={cn(\n      `\n        data-[state=open]:animate-in data-[state=open]:fade-in-0\n        data-[state=open]:zoom-in-95\n        data-[state=closed]:animate-out data-[state=closed]:fade-out-0\n        data-[state=closed]:zoom-out-95\n        data-[side=bottom]:slide-in-from-top-2\n        data-[side=left]:slide-in-from-right-2\n        data-[side=right]:slide-in-from-left-2\n        data-[side=top]:slide-in-from-bottom-2\n        z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1\n        text-popover-foreground shadow-md\n      `,\n      className\n    )}\n    {...props}\n  />\n))\nContextMenuSubContent.displayName = ContextMenuPrimitive.SubContent.displayName\n\nconst ContextMenuContent = React.forwardRef<\n  React.ElementRef<typeof ContextMenuPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof ContextMenuPrimitive.Content>\n>(({ className, ...props }, ref) => (\n  <ContextMenuPrimitive.Portal>\n    <ContextMenuPrimitive.Content\n      ref={ref}\n      className={cn(\n        `\n          animate-in fade-in-80 z-50 min-w-[8rem] overflow-hidden rounded-md\n          border bg-popover p-1 text-popover-foreground shadow-md\n          data-[state=open]:animate-in data-[state=open]:fade-in-0\n          data-[state=open]:zoom-in-95\n          data-[state=closed]:animate-out data-[state=closed]:fade-out-0\n          data-[state=closed]:zoom-out-95\n          data-[side=bottom]:slide-in-from-top-2\n          data-[side=left]:slide-in-from-right-2\n          data-[side=right]:slide-in-from-left-2\n          data-[side=top]:slide-in-from-bottom-2\n        `,\n        className\n      )}\n      {...props}\n    />\n  </ContextMenuPrimitive.Portal>\n))\nContextMenuContent.displayName = ContextMenuPrimitive.Content.displayName\n\nconst ContextMenuItem = React.forwardRef<\n  React.ElementRef<typeof ContextMenuPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof ContextMenuPrimitive.Item> & {\n    inset?: boolean\n  }\n>(({ className, inset, ...props }, ref) => (\n  <ContextMenuPrimitive.Item\n    ref={ref}\n    className={cn(\n      `\n        relative flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm\n        outline-hidden select-none\n        focus:bg-accent focus:text-accent-foreground\n        data-disabled:pointer-events-none data-disabled:opacity-50\n      `,\n      inset && 'pl-8',\n      className\n    )}\n    {...props}\n  />\n))\nContextMenuItem.displayName = ContextMenuPrimitive.Item.displayName\n\nconst ContextMenuCheckboxItem = React.forwardRef<\n  React.ElementRef<typeof ContextMenuPrimitive.CheckboxItem>,\n  React.ComponentPropsWithoutRef<typeof ContextMenuPrimitive.CheckboxItem>\n>(({ className, children, checked, ...props }, ref) => (\n  <ContextMenuPrimitive.CheckboxItem\n    ref={ref}\n    className={cn(\n      `\n        relative flex cursor-default items-center rounded-sm py-1.5 pr-2 pl-8\n        text-sm outline-hidden select-none\n        focus:bg-accent focus:text-accent-foreground\n        data-disabled:pointer-events-none data-disabled:opacity-50\n      `,\n      className\n    )}\n    checked={checked}\n    {...props}\n  >\n    <span\n      className={`\n      absolute left-2 flex h-3.5 w-3.5 items-center justify-center\n    `}\n    >\n      <ContextMenuPrimitive.ItemIndicator>\n        <Check className='h-4 w-4' />\n      </ContextMenuPrimitive.ItemIndicator>\n    </span>\n    {children}\n  </ContextMenuPrimitive.CheckboxItem>\n))\nContextMenuCheckboxItem.displayName = ContextMenuPrimitive.CheckboxItem.displayName\n\nconst ContextMenuRadioItem = React.forwardRef<\n  React.ElementRef<typeof ContextMenuPrimitive.RadioItem>,\n  React.ComponentPropsWithoutRef<typeof ContextMenuPrimitive.RadioItem>\n>(({ className, children, ...props }, ref) => (\n  <ContextMenuPrimitive.RadioItem\n    ref={ref}\n    className={cn(\n      `\n        relative flex cursor-default items-center rounded-sm py-1.5 pr-2 pl-8\n        text-sm outline-hidden select-none\n        focus:bg-accent focus:text-accent-foreground\n        data-disabled:pointer-events-none data-disabled:opacity-50\n      `,\n      className\n    )}\n    {...props}\n  >\n    <span\n      className={`\n      absolute left-2 flex h-3.5 w-3.5 items-center justify-center\n    `}\n    >\n      <ContextMenuPrimitive.ItemIndicator>\n        <Circle className='h-2 w-2 fill-current' />\n      </ContextMenuPrimitive.ItemIndicator>\n    </span>\n    {children}\n  </ContextMenuPrimitive.RadioItem>\n))\nContextMenuRadioItem.displayName = ContextMenuPrimitive.RadioItem.displayName\n\nconst ContextMenuLabel = React.forwardRef<\n  React.ElementRef<typeof ContextMenuPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof ContextMenuPrimitive.Label> & {\n    inset?: boolean\n  }\n>(({ className, inset, ...props }, ref) => (\n  <ContextMenuPrimitive.Label\n    ref={ref}\n    className={cn('px-2 py-1.5 text-sm font-semibold text-foreground', inset && 'pl-8', className)}\n    {...props}\n  />\n))\nContextMenuLabel.displayName = ContextMenuPrimitive.Label.displayName\n\nconst ContextMenuSeparator = React.forwardRef<\n  React.ElementRef<typeof ContextMenuPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof ContextMenuPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <ContextMenuPrimitive.Separator\n    ref={ref}\n    className={cn(\n      `\n    -mx-1 my-1 h-px bg-border\n  `,\n      className\n    )}\n    {...props}\n  />\n))\nContextMenuSeparator.displayName = ContextMenuPrimitive.Separator.displayName\n\nconst ContextMenuShortcut = ({ className, ...props }: React.HTMLAttributes<HTMLSpanElement>) => {\n  return (\n    <span\n      className={cn(\n        `\n    ml-auto text-xs tracking-widest text-muted-foreground\n  `,\n        className\n      )}\n      {...props}\n    />\n  )\n}\nContextMenuShortcut.displayName = 'ContextMenuShortcut'\n\nexport {\n  ContextMenu,\n  ContextMenuTrigger,\n  ContextMenuContent,\n  ContextMenuItem,\n  ContextMenuCheckboxItem,\n  ContextMenuRadioItem,\n  ContextMenuLabel,\n  ContextMenuSeparator,\n  ContextMenuShortcut,\n  ContextMenuGroup,\n  ContextMenuPortal,\n  ContextMenuSub,\n  ContextMenuSubContent,\n  ContextMenuSubTrigger,\n  ContextMenuRadioGroup,\n}\n", "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/dialog.tsx", "messages": [{"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=open]:animate-in", "line": 25, "column": 9, "nodeType": null, "endLine": 25, "endColumn": 37}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=open]:fade-in-0", "line": 25, "column": 38, "nodeType": null, "endLine": 25, "endColumn": 65}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=closed]:animate-out", "line": 26, "column": 9, "nodeType": null, "endLine": 26, "endColumn": 40}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=closed]:fade-out-0", "line": 26, "column": 41, "nodeType": null, "endLine": 26, "endColumn": 71}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=open]:animate-in", "line": 46, "column": 11, "nodeType": null, "endLine": 46, "endColumn": 39}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=open]:fade-in-0", "line": 46, "column": 40, "nodeType": null, "endLine": 46, "endColumn": 67}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=open]:zoom-in-95", "line": 47, "column": 11, "nodeType": null, "endLine": 47, "endColumn": 39}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=open]:slide-in-from-left-1/2", "line": 47, "column": 40, "nodeType": null, "endLine": 47, "endColumn": 80}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=open]:slide-in-from-top-[48%]", "line": 48, "column": 11, "nodeType": null, "endLine": 48, "endColumn": 52}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=closed]:animate-out", "line": 49, "column": 11, "nodeType": null, "endLine": 49, "endColumn": 42}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=closed]:fade-out-0", "line": 49, "column": 43, "nodeType": null, "endLine": 49, "endColumn": 73}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=closed]:zoom-out-95", "line": 50, "column": 11, "nodeType": null, "endLine": 50, "endColumn": 42}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=closed]:slide-out-to-left-1/2", "line": 51, "column": 11, "nodeType": null, "endLine": 51, "endColumn": 52}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=closed]:slide-out-to-top-[48%]", "line": 52, "column": 11, "nodeType": null, "endLine": 52, "endColumn": 53}], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "'use client'\n\nimport * as DialogPrimitive from '@radix-ui/react-dialog'\nimport { X } from 'lucide-react'\nimport * as React from 'react'\n\nimport { cn } from '@/lib/utils'\n\nconst Dialog = DialogPrimitive.Root\n\nconst DialogTrigger = DialogPrimitive.Trigger\n\nconst DialogPortal = DialogPrimitive.Portal\n\nconst DialogClose = DialogPrimitive.Close\n\nconst DialogOverlay = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Overlay>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Overlay\n    ref={ref}\n    className={cn(\n      `\n        data-[state=open]:animate-in data-[state=open]:fade-in-0\n        data-[state=closed]:animate-out data-[state=closed]:fade-out-0\n        fixed inset-0 z-50 bg-black/80 backdrop-blur-xs\n      `,\n      className\n    )}\n    {...props}\n  />\n))\nDialogOverlay.displayName = DialogPrimitive.Overlay.displayName\n\nconst DialogContent = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>\n>(({ className, children, ...props }, ref) => (\n  <DialogPortal>\n    <DialogOverlay />\n    <DialogPrimitive.Content\n      ref={ref}\n      className={cn(\n        `\n          data-[state=open]:animate-in data-[state=open]:fade-in-0\n          data-[state=open]:zoom-in-95 data-[state=open]:slide-in-from-left-1/2\n          data-[state=open]:slide-in-from-top-[48%]\n          data-[state=closed]:animate-out data-[state=closed]:fade-out-0\n          data-[state=closed]:zoom-out-95\n          data-[state=closed]:slide-out-to-left-1/2\n          data-[state=closed]:slide-out-to-top-[48%]\n          fixed top-[50%] left-[50%] z-50 grid w-full max-w-lg\n          translate-x-[-50%] translate-y-[-50%] gap-4 border border-amber-100\n          bg-white p-6 shadow-lg duration-200\n          sm:rounded-lg\n        `,\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <DialogPrimitive.Close\n        className={`\n          absolute top-4 right-4 rounded-sm opacity-70 ring-offset-background\n          transition-opacity\n          hover:opacity-100\n          focus:ring-2 focus:ring-ring focus:ring-offset-2 focus:outline-hidden\n          disabled:pointer-events-none\n          data-[state=open]:bg-accent data-[state=open]:text-muted-foreground\n        `}\n      >\n        <X className='h-4 w-4' />\n        <span className='sr-only'>Close</span>\n      </DialogPrimitive.Close>\n    </DialogPrimitive.Content>\n  </DialogPortal>\n))\nDialogContent.displayName = DialogPrimitive.Content.displayName\n\nconst DialogHeader = ({ className, ...props }: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      `\n        flex flex-col space-y-1.5 text-center\n        sm:text-left\n      `,\n      className\n    )}\n    {...props}\n  />\n)\nDialogHeader.displayName = 'DialogHeader'\n\nconst DialogFooter = ({ className, ...props }: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      `\n        flex flex-col-reverse\n        sm:flex-row sm:justify-end sm:space-x-2\n      `,\n      className\n    )}\n    {...props}\n  />\n)\nDialogFooter.displayName = 'DialogFooter'\n\nconst DialogTitle = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Title>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Title>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Title\n    ref={ref}\n    className={cn('text-lg leading-none font-semibold tracking-tight', className)}\n    {...props}\n  />\n))\nDialogTitle.displayName = DialogPrimitive.Title.displayName\n\nconst DialogDescription = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Description>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Description>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Description ref={ref} className={cn('text-sm text-gray-500', className)} {...props} />\n))\nDialogDescription.displayName = DialogPrimitive.Description.displayName\n\nexport {\n  Dialog,\n  DialogPortal,\n  DialogOverlay,\n  DialogClose,\n  DialogTrigger,\n  DialogContent,\n  DialogHeader,\n  DialogFooter,\n  DialogTitle,\n  DialogDescription,\n}\n", "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/drawer.tsx", "messages": [{"ruleId": "better-tailwindcss/multiline", "severity": 1, "message": "Unnecessary line wrapping. Expected\n\n`↵\n····fixed·inset-0·z-50·bg-black/80↵\n··`\n\nto be\n\n`fixed·inset-0·z-50·bg-black/80`", "line": 26, "column": 7, "nodeType": null, "endLine": 28, "endColumn": 4, "fix": {"range": [758, 798], "text": "`fixed inset-0 z-50 bg-black/80`"}}, {"ruleId": "better-tailwindcss/multiline", "severity": 1, "message": "Unnecessary line wrapping. Expected\n\n`↵\n····text-sm·text-muted-foreground↵\n··`\n\nto be\n\n`text-sm·text-muted-foreground`", "line": 98, "column": 7, "nodeType": null, "endLine": 100, "endColumn": 4, "fix": {"range": [2764, 2803], "text": "`text-sm text-muted-foreground`"}}], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 2, "source": "'use client'\n\nimport * as React from 'react'\nimport { Drawer as DrawerPrimitive } from 'vaul'\n\nimport { cn } from '@/lib/utils'\n\nconst Drawer = ({ shouldScaleBackground = true, ...props }: React.ComponentProps<typeof DrawerPrimitive.Root>) => (\n  <DrawerPrimitive.Root shouldScaleBackground={shouldScaleBackground} {...props} />\n)\nDrawer.displayName = 'Drawer'\n\nconst DrawerTrigger = DrawerPrimitive.Trigger\n\nconst DrawerPortal = DrawerPrimitive.Portal\n\nconst DrawerClose = DrawerPrimitive.Close\n\nconst DrawerOverlay = React.forwardRef<\n  React.ElementRef<typeof DrawerPrimitive.Overlay>,\n  React.ComponentPropsWithoutRef<typeof DrawerPrimitive.Overlay>\n>(({ className, ...props }, ref) => (\n  <DrawerPrimitive.Overlay\n    ref={ref}\n    className={cn(\n      `\n    fixed inset-0 z-50 bg-black/80\n  `,\n      className\n    )}\n    {...props}\n  />\n))\nDrawerOverlay.displayName = DrawerPrimitive.Overlay.displayName\n\nconst DrawerContent = React.forwardRef<\n  React.ElementRef<typeof DrawerPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof DrawerPrimitive.Content>\n>(({ className, children, ...props }, ref) => (\n  <DrawerPortal>\n    <DrawerOverlay />\n    <DrawerPrimitive.Content\n      ref={ref}\n      className={cn(\n        `\n          fixed inset-x-0 bottom-0 z-50 mt-24 flex h-auto flex-col\n          rounded-t-[10px] border bg-background\n        `,\n        className\n      )}\n      {...props}\n    >\n      <div className='mx-auto mt-4 h-2 w-[100px] rounded-full bg-muted' />\n      {children}\n    </DrawerPrimitive.Content>\n  </DrawerPortal>\n))\nDrawerContent.displayName = 'DrawerContent'\n\nconst DrawerHeader = ({ className, ...props }: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      `\n        grid gap-1.5 p-4 text-center\n        sm:text-left\n      `,\n      className\n    )}\n    {...props}\n  />\n)\nDrawerHeader.displayName = 'DrawerHeader'\n\nconst DrawerFooter = ({ className, ...props }: React.HTMLAttributes<HTMLDivElement>) => (\n  <div className={cn('mt-auto flex flex-col gap-2 p-4', className)} {...props} />\n)\nDrawerFooter.displayName = 'DrawerFooter'\n\nconst DrawerTitle = React.forwardRef<\n  React.ElementRef<typeof DrawerPrimitive.Title>,\n  React.ComponentPropsWithoutRef<typeof DrawerPrimitive.Title>\n>(({ className, ...props }, ref) => (\n  <DrawerPrimitive.Title\n    ref={ref}\n    className={cn('text-lg leading-none font-semibold tracking-tight', className)}\n    {...props}\n  />\n))\nDrawerTitle.displayName = DrawerPrimitive.Title.displayName\n\nconst DrawerDescription = React.forwardRef<\n  React.ElementRef<typeof DrawerPrimitive.Description>,\n  React.ComponentPropsWithoutRef<typeof DrawerPrimitive.Description>\n>(({ className, ...props }, ref) => (\n  <DrawerPrimitive.Description\n    ref={ref}\n    className={cn(\n      `\n    text-sm text-muted-foreground\n  `,\n      className\n    )}\n    {...props}\n  />\n))\nDrawerDescription.displayName = DrawerPrimitive.Description.displayName\n\nexport {\n  Drawer,\n  DrawerPortal,\n  DrawerOverlay,\n  DrawerTrigger,\n  DrawerClose,\n  DrawerContent,\n  DrawerHeader,\n  DrawerFooter,\n  DrawerTitle,\n  DrawerDescription,\n}\n", "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/dropdown-menu.tsx", "messages": [{"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=open]:animate-in", "line": 56, "column": 9, "nodeType": null, "endLine": 56, "endColumn": 37}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=open]:fade-in-0", "line": 56, "column": 38, "nodeType": null, "endLine": 56, "endColumn": 65}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=open]:zoom-in-95", "line": 57, "column": 9, "nodeType": null, "endLine": 57, "endColumn": 37}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=closed]:animate-out", "line": 58, "column": 9, "nodeType": null, "endLine": 58, "endColumn": 40}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=closed]:fade-out-0", "line": 58, "column": 41, "nodeType": null, "endLine": 58, "endColumn": 71}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=closed]:zoom-out-95", "line": 59, "column": 9, "nodeType": null, "endLine": 59, "endColumn": 40}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[side=bottom]:slide-in-from-top-2", "line": 60, "column": 9, "nodeType": null, "endLine": 60, "endColumn": 47}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[side=left]:slide-in-from-right-2", "line": 61, "column": 9, "nodeType": null, "endLine": 61, "endColumn": 47}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[side=right]:slide-in-from-left-2", "line": 62, "column": 9, "nodeType": null, "endLine": 62, "endColumn": 47}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[side=top]:slide-in-from-bottom-2", "line": 63, "column": 9, "nodeType": null, "endLine": 63, "endColumn": 47}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=open]:animate-in", "line": 84, "column": 11, "nodeType": null, "endLine": 84, "endColumn": 39}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=open]:fade-in-0", "line": 84, "column": 40, "nodeType": null, "endLine": 84, "endColumn": 67}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=open]:zoom-in-95", "line": 85, "column": 11, "nodeType": null, "endLine": 85, "endColumn": 39}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=closed]:animate-out", "line": 86, "column": 11, "nodeType": null, "endLine": 86, "endColumn": 42}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=closed]:fade-out-0", "line": 86, "column": 43, "nodeType": null, "endLine": 86, "endColumn": 73}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=closed]:zoom-out-95", "line": 87, "column": 11, "nodeType": null, "endLine": 87, "endColumn": 42}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[side=bottom]:slide-in-from-top-2", "line": 88, "column": 11, "nodeType": null, "endLine": 88, "endColumn": 49}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[side=left]:slide-in-from-right-2", "line": 89, "column": 11, "nodeType": null, "endLine": 89, "endColumn": 49}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[side=right]:slide-in-from-left-2", "line": 90, "column": 11, "nodeType": null, "endLine": 90, "endColumn": 49}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[side=top]:slide-in-from-bottom-2", "line": 91, "column": 11, "nodeType": null, "endLine": 91, "endColumn": 49}, {"ruleId": "better-tailwindcss/multiline", "severity": 1, "message": "Unnecessary line wrapping. Expected\n\n`↵\n······absolute·left-2·flex·h-3.5·w-3.5·items-center·justify-center↵\n····`\n\nto be\n\n`absolute·left-2·flex·h-3.5·w-3.5·items-center·justify-center`", "line": 146, "column": 18, "nodeType": null, "endLine": 148, "endColumn": 6, "fix": {"range": [4900, 4974], "text": "`absolute left-2 flex h-3.5 w-3.5 items-center justify-center`"}}, {"ruleId": "better-tailwindcss/multiline", "severity": 1, "message": "Unnecessary line wrapping. Expected\n\n`↵\n······absolute·left-2·flex·h-3.5·w-3.5·items-center·justify-center↵\n····`\n\nto be\n\n`absolute·left-2·flex·h-3.5·w-3.5·items-center·justify-center`", "line": 177, "column": 18, "nodeType": null, "endLine": 179, "endColumn": 6, "fix": {"range": [5908, 5982], "text": "`absolute left-2 flex h-3.5 w-3.5 items-center justify-center`"}}, {"ruleId": "better-tailwindcss/multiline", "severity": 1, "message": "Unnecessary line wrapping. Expected\n\n`↵\n····-mx-1·my-1·h-px·bg-muted↵\n··`\n\nto be\n\n`-mx-1·my-1·h-px·bg-muted`", "line": 211, "column": 7, "nodeType": null, "endLine": 213, "endColumn": 4, "fix": {"range": [7039, 7073], "text": "`-mx-1 my-1 h-px bg-muted`"}}], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 23, "fixableErrorCount": 0, "fixableWarningCount": 3, "source": "'use client'\n\nimport * as DropdownMenuPrimitive from '@radix-ui/react-dropdown-menu'\nimport { Check, ChevronRight, Circle } from 'lucide-react'\nimport * as React from 'react'\n\nimport { cn } from '@/lib/utils'\n\nconst DropdownMenu = DropdownMenuPrimitive.Root\n\nconst DropdownMenuTrigger = DropdownMenuPrimitive.Trigger\n\nconst DropdownMenuGroup = DropdownMenuPrimitive.Group\n\nconst DropdownMenuPortal = DropdownMenuPrimitive.Portal\n\nconst DropdownMenuSub = DropdownMenuPrimitive.Sub\n\nconst DropdownMenuRadioGroup = DropdownMenuPrimitive.RadioGroup\n\nconst DropdownMenuSubTrigger = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.SubTrigger>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubTrigger> & {\n    inset?: boolean\n  }\n>(({ className, inset, children, ...props }, ref) => (\n  <DropdownMenuPrimitive.SubTrigger\n    ref={ref}\n    className={cn(\n      `\n        flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm\n        outline-hidden select-none\n        focus:bg-accent\n        data-[state=open]:bg-accent\n        [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\n      `,\n      inset && 'pl-8',\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <ChevronRight className='ml-auto' />\n  </DropdownMenuPrimitive.SubTrigger>\n))\nDropdownMenuSubTrigger.displayName = DropdownMenuPrimitive.SubTrigger.displayName\n\nconst DropdownMenuSubContent = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.SubContent>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubContent>\n>(({ className, ...props }, ref) => (\n  <DropdownMenuPrimitive.SubContent\n    ref={ref}\n    className={cn(\n      `\n        data-[state=open]:animate-in data-[state=open]:fade-in-0\n        data-[state=open]:zoom-in-95\n        data-[state=closed]:animate-out data-[state=closed]:fade-out-0\n        data-[state=closed]:zoom-out-95\n        data-[side=bottom]:slide-in-from-top-2\n        data-[side=left]:slide-in-from-right-2\n        data-[side=right]:slide-in-from-left-2\n        data-[side=top]:slide-in-from-bottom-2\n        z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1\n        text-popover-foreground shadow-lg\n      `,\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuSubContent.displayName = DropdownMenuPrimitive.SubContent.displayName\n\nconst DropdownMenuContent = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Content>\n>(({ className, sideOffset = 4, ...props }, ref) => (\n  <DropdownMenuPrimitive.Portal>\n    <DropdownMenuPrimitive.Content\n      ref={ref}\n      sideOffset={sideOffset}\n      className={cn(\n        `\n          data-[state=open]:animate-in data-[state=open]:fade-in-0\n          data-[state=open]:zoom-in-95\n          data-[state=closed]:animate-out data-[state=closed]:fade-out-0\n          data-[state=closed]:zoom-out-95\n          data-[side=bottom]:slide-in-from-top-2\n          data-[side=left]:slide-in-from-right-2\n          data-[side=right]:slide-in-from-left-2\n          data-[side=top]:slide-in-from-bottom-2\n          z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1\n          text-popover-foreground shadow-md\n        `,\n        className\n      )}\n      {...props}\n    />\n  </DropdownMenuPrimitive.Portal>\n))\nDropdownMenuContent.displayName = DropdownMenuPrimitive.Content.displayName\n\nconst DropdownMenuItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Item> & {\n    inset?: boolean\n  }\n>(({ className, inset, ...props }, ref) => (\n  <DropdownMenuPrimitive.Item\n    ref={ref}\n    className={cn(\n      `\n        relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5\n        text-sm outline-hidden transition-colors select-none\n        focus:bg-accent focus:text-accent-foreground\n        data-disabled:pointer-events-none data-disabled:opacity-50\n        [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\n      `,\n      inset && 'pl-8',\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuItem.displayName = DropdownMenuPrimitive.Item.displayName\n\nconst DropdownMenuCheckboxItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.CheckboxItem>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.CheckboxItem>\n>(({ className, children, checked, ...props }, ref) => (\n  <DropdownMenuPrimitive.CheckboxItem\n    ref={ref}\n    className={cn(\n      `\n        relative flex cursor-default items-center rounded-sm py-1.5 pr-2 pl-8\n        text-sm outline-hidden transition-colors select-none\n        focus:bg-accent focus:text-accent-foreground\n        data-disabled:pointer-events-none data-disabled:opacity-50\n      `,\n      className\n    )}\n    checked={checked}\n    {...props}\n  >\n    <span\n      className={`\n      absolute left-2 flex h-3.5 w-3.5 items-center justify-center\n    `}\n    >\n      <DropdownMenuPrimitive.ItemIndicator>\n        <Check className='h-4 w-4' />\n      </DropdownMenuPrimitive.ItemIndicator>\n    </span>\n    {children}\n  </DropdownMenuPrimitive.CheckboxItem>\n))\nDropdownMenuCheckboxItem.displayName = DropdownMenuPrimitive.CheckboxItem.displayName\n\nconst DropdownMenuRadioItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.RadioItem>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.RadioItem>\n>(({ className, children, ...props }, ref) => (\n  <DropdownMenuPrimitive.RadioItem\n    ref={ref}\n    className={cn(\n      `\n        relative flex cursor-default items-center rounded-sm py-1.5 pr-2 pl-8\n        text-sm outline-hidden transition-colors select-none\n        focus:bg-accent focus:text-accent-foreground\n        data-disabled:pointer-events-none data-disabled:opacity-50\n      `,\n      className\n    )}\n    {...props}\n  >\n    <span\n      className={`\n      absolute left-2 flex h-3.5 w-3.5 items-center justify-center\n    `}\n    >\n      <DropdownMenuPrimitive.ItemIndicator>\n        <Circle className='h-2 w-2 fill-current' />\n      </DropdownMenuPrimitive.ItemIndicator>\n    </span>\n    {children}\n  </DropdownMenuPrimitive.RadioItem>\n))\nDropdownMenuRadioItem.displayName = DropdownMenuPrimitive.RadioItem.displayName\n\nconst DropdownMenuLabel = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Label> & {\n    inset?: boolean\n  }\n>(({ className, inset, ...props }, ref) => (\n  <DropdownMenuPrimitive.Label\n    ref={ref}\n    className={cn('px-2 py-1.5 text-sm font-semibold', inset && 'pl-8', className)}\n    {...props}\n  />\n))\nDropdownMenuLabel.displayName = DropdownMenuPrimitive.Label.displayName\n\nconst DropdownMenuSeparator = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <DropdownMenuPrimitive.Separator\n    ref={ref}\n    className={cn(\n      `\n    -mx-1 my-1 h-px bg-muted\n  `,\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuSeparator.displayName = DropdownMenuPrimitive.Separator.displayName\n\nconst DropdownMenuShortcut = ({ className, ...props }: React.HTMLAttributes<HTMLSpanElement>) => {\n  return <span className={cn('ml-auto text-xs tracking-widest opacity-60', className)} {...props} />\n}\nDropdownMenuShortcut.displayName = 'DropdownMenuShortcut'\n\nexport {\n  DropdownMenu,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioItem,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuGroup,\n  DropdownMenuPortal,\n  DropdownMenuSub,\n  DropdownMenuSubContent,\n  DropdownMenuSubTrigger,\n  DropdownMenuRadioGroup,\n}\n", "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/form.tsx", "messages": [{"ruleId": "better-tailwindcss/multiline", "severity": 1, "message": "Unnecessary line wrapping. Expected\n\n`↵\n······text-sm·text-muted-foreground↵\n····`\n\nto be\n\n`text-sm·text-muted-foreground`", "line": 114, "column": 11, "nodeType": null, "endLine": 116, "endColumn": 6, "fix": {"range": [3331, 3374], "text": "`text-sm text-muted-foreground`"}}, {"ruleId": "better-tailwindcss/multiline", "severity": 1, "message": "Unnecessary line wrapping. Expected\n\n`↵\n········text-sm·font-medium·text-destructive↵\n······`\n\nto be\n\n`text-sm·font-medium·text-destructive`", "line": 140, "column": 11, "nodeType": null, "endLine": 142, "endColumn": 8, "fix": {"range": [3902, 3956], "text": "`text-sm font-medium text-destructive`"}}], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 2, "source": "'use client'\n\nimport type * as LabelPrimitive from '@radix-ui/react-label'\nimport { Slot } from '@radix-ui/react-slot'\nimport * as React from 'react'\nimport type { ControllerProps, FieldPath, FieldValues } from 'react-hook-form'\nimport { Controller, FormProvider, useFormContext } from 'react-hook-form'\n\nimport { Label } from '@/components/ui/label'\nimport { cn } from '@/lib/utils'\n\nconst Form = FormProvider\n\ntype FormFieldContextValue<\n  TFieldValues extends FieldValues = FieldValues,\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\n> = {\n  name: TName\n}\n\nconst FormFieldContext = React.createContext<FormFieldContextValue>({} as FormFieldContextValue)\n\nconst FormField = <\n  TFieldValues extends FieldValues = FieldValues,\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\n>({\n  ...props\n}: ControllerProps<TFieldValues, TName>) => {\n  return (\n    <FormFieldContext.Provider value={{ name: props.name }}>\n      <Controller {...props} />\n    </FormFieldContext.Provider>\n  )\n}\n\nconst useFormField = () => {\n  const fieldContext = React.useContext(FormFieldContext)\n  const itemContext = React.useContext(FormItemContext)\n  const { getFieldState, formState } = useFormContext()\n\n  const fieldState = getFieldState(fieldContext.name, formState)\n\n  if (!fieldContext) {\n    throw new Error('useFormField should be used within <FormField>')\n  }\n\n  const { id } = itemContext\n\n  return {\n    id,\n    name: fieldContext.name,\n    formItemId: `${id}-form-item`,\n    formDescriptionId: `${id}-form-item-description`,\n    formMessageId: `${id}-form-item-message`,\n    ...fieldState,\n  }\n}\n\ntype FormItemContextValue = {\n  id: string\n}\n\nconst FormItemContext = React.createContext<FormItemContextValue>({} as FormItemContextValue)\n\nconst FormItem = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => {\n    const id = React.useId()\n\n    return (\n      <FormItemContext.Provider value={{ id }}>\n        <div ref={ref} className={cn('space-y-2', className)} {...props} />\n      </FormItemContext.Provider>\n    )\n  }\n)\nFormItem.displayName = 'FormItem'\n\nconst FormLabel = React.forwardRef<\n  React.ElementRef<typeof LabelPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root>\n>(({ className, ...props }, ref) => {\n  const { error, formItemId } = useFormField()\n\n  return <Label ref={ref} className={cn(error && 'text-destructive', className)} htmlFor={formItemId} {...props} />\n})\nFormLabel.displayName = 'FormLabel'\n\nconst FormControl = React.forwardRef<React.ElementRef<typeof Slot>, React.ComponentPropsWithoutRef<typeof Slot>>(\n  ({ ...props }, ref) => {\n    const { error, formItemId, formDescriptionId, formMessageId } = useFormField()\n\n    return (\n      <Slot\n        ref={ref}\n        id={formItemId}\n        aria-describedby={!error ? `${formDescriptionId}` : `${formDescriptionId} ${formMessageId}`}\n        aria-invalid={!!error}\n        {...props}\n      />\n    )\n  }\n)\nFormControl.displayName = 'FormControl'\n\nconst FormDescription = React.forwardRef<HTMLParagraphElement, React.HTMLAttributes<HTMLParagraphElement>>(\n  ({ className, ...props }, ref) => {\n    const { formDescriptionId } = useFormField()\n\n    return (\n      <p\n        ref={ref}\n        id={formDescriptionId}\n        className={cn(\n          `\n      text-sm text-muted-foreground\n    `,\n          className\n        )}\n        {...props}\n      />\n    )\n  }\n)\nFormDescription.displayName = 'FormDescription'\n\nconst FormMessage = React.forwardRef<HTMLParagraphElement, React.HTMLAttributes<HTMLParagraphElement>>(\n  ({ className, children, ...props }, ref) => {\n    const { error, formMessageId } = useFormField()\n    const body = error ? String(error?.message) : children\n\n    if (!body) {\n      return null\n    }\n\n    return (\n      <p\n        ref={ref}\n        id={formMessageId}\n        className={cn(\n          `\n        text-sm font-medium text-destructive\n      `,\n          className\n        )}\n        {...props}\n      >\n        {body}\n      </p>\n    )\n  }\n)\nFormMessage.displayName = 'FormMessage'\n\nexport { useFormField, Form, FormItem, FormLabel, FormControl, FormDescription, FormMessage, FormField }\n", "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/grid-pattern.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/hover-card.tsx", "messages": [{"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=open]:animate-in", "line": 22, "column": 9, "nodeType": null, "endLine": 22, "endColumn": 37}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=open]:fade-in-0", "line": 22, "column": 38, "nodeType": null, "endLine": 22, "endColumn": 65}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=open]:zoom-in-95", "line": 23, "column": 9, "nodeType": null, "endLine": 23, "endColumn": 37}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=closed]:animate-out", "line": 24, "column": 9, "nodeType": null, "endLine": 24, "endColumn": 40}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=closed]:fade-out-0", "line": 24, "column": 41, "nodeType": null, "endLine": 24, "endColumn": 71}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=closed]:zoom-out-95", "line": 25, "column": 9, "nodeType": null, "endLine": 25, "endColumn": 40}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[side=bottom]:slide-in-from-top-2", "line": 26, "column": 9, "nodeType": null, "endLine": 26, "endColumn": 47}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[side=left]:slide-in-from-right-2", "line": 27, "column": 9, "nodeType": null, "endLine": 27, "endColumn": 47}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[side=right]:slide-in-from-left-2", "line": 28, "column": 9, "nodeType": null, "endLine": 28, "endColumn": 47}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[side=top]:slide-in-from-bottom-2", "line": 29, "column": 9, "nodeType": null, "endLine": 29, "endColumn": 47}], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "'use client'\n\nimport * as HoverCardPrimitive from '@radix-ui/react-hover-card'\nimport * as React from 'react'\n\nimport { cn } from '@/lib/utils'\n\nconst HoverCard = HoverCardPrimitive.Root\n\nconst HoverCardTrigger = HoverCardPrimitive.Trigger\n\nconst HoverCardContent = React.forwardRef<\n  React.ElementRef<typeof HoverCardPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof HoverCardPrimitive.Content>\n>(({ className, align = 'center', sideOffset = 4, ...props }, ref) => (\n  <HoverCardPrimitive.Content\n    ref={ref}\n    align={align}\n    sideOffset={sideOffset}\n    className={cn(\n      `\n        data-[state=open]:animate-in data-[state=open]:fade-in-0\n        data-[state=open]:zoom-in-95\n        data-[state=closed]:animate-out data-[state=closed]:fade-out-0\n        data-[state=closed]:zoom-out-95\n        data-[side=bottom]:slide-in-from-top-2\n        data-[side=left]:slide-in-from-right-2\n        data-[side=right]:slide-in-from-left-2\n        data-[side=top]:slide-in-from-bottom-2\n        z-50 w-64 rounded-md border bg-popover p-4 text-popover-foreground\n        shadow-md outline-hidden\n      `,\n      className\n    )}\n    {...props}\n  />\n))\nHoverCardContent.displayName = HoverCardPrimitive.Content.displayName\n\nexport { HoverCard, HoverCardTrigger, HoverCardContent }\n", "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/input-otp.tsx", "messages": [{"ruleId": "better-tailwindcss/multiline", "severity": 1, "message": "Unnecessary line wrapping. Expected\n\n`↵\n····flex·items-center↵\n··`\n\nto be\n\n`flex·items-center`", "line": 32, "column": 9, "nodeType": null, "endLine": 34, "endColumn": 4, "fix": {"range": [855, 882], "text": "`flex items-center`"}}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: animate-caret-blink", "line": 75, "column": 15, "nodeType": null, "endLine": 75, "endColumn": 34}], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": "'use client'\n\nimport { OTPInput, OTPInputContext } from 'input-otp'\nimport { Dot } from 'lucide-react'\nimport * as React from 'react'\n\nimport { cn } from '@/lib/utils'\n\nconst InputOTP = React.forwardRef<React.ElementRef<typeof OTPInput>, React.ComponentPropsWithoutRef<typeof OTPInput>>(\n  ({ className, containerClassName, ...props }, ref) => (\n    <OTPInput\n      ref={ref}\n      containerClassName={cn(\n        `\n          flex items-center gap-2\n          has-disabled:opacity-50\n        `,\n        containerClassName\n      )}\n      className={cn('disabled:cursor-not-allowed', className)}\n      {...props}\n    />\n  )\n)\nInputOTP.displayName = 'InputOTP'\n\nconst InputOTPGroup = React.forwardRef<React.ElementRef<'div'>, React.ComponentPropsWithoutRef<'div'>>(\n  ({ className, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn(\n        `\n    flex items-center\n  `,\n        className\n      )}\n      {...props}\n    />\n  )\n)\nInputOTPGroup.displayName = 'InputOTPGroup'\n\nconst InputOTPSlot = React.forwardRef<\n  React.ElementRef<'div'>,\n  React.ComponentPropsWithoutRef<'div'> & { index: number }\n>(({ index, className, ...props }, ref) => {\n  const inputOTPContext = React.useContext(OTPInputContext)\n  const { char, hasFakeCaret, isActive } = inputOTPContext.slots[index]\n\n  return (\n    <div\n      ref={ref}\n      className={cn(\n        `\n          relative flex h-10 w-10 items-center justify-center border-y border-r\n          border-input text-sm transition-all\n          first:rounded-l-md first:border-l\n          last:rounded-r-md\n        `,\n        isActive && 'z-10 ring-2 ring-ring ring-offset-background',\n        className\n      )}\n      {...props}\n    >\n      {char}\n      {hasFakeCaret && (\n        <div\n          className={`\n            pointer-events-none absolute inset-0 flex items-center\n            justify-center\n          `}\n        >\n          <div\n            className={`\n              animate-caret-blink h-4 w-px bg-foreground duration-1000\n            `}\n          />\n        </div>\n      )}\n    </div>\n  )\n})\nInputOTPSlot.displayName = 'InputOTPSlot'\n\nconst InputOTPSeparator = React.forwardRef<React.ElementRef<'div'>, React.ComponentPropsWithoutRef<'div'>>(\n  ({ ...props }, ref) => (\n    <div ref={ref} role='separator' {...props}>\n      <Dot />\n    </div>\n  )\n)\nInputOTPSeparator.displayName = 'InputOTPSeparator'\n\nexport { InputOTP, InputOTPGroup, InputOTPSlot, InputOTPSeparator }\n", "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/input.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/label.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/menubar.tsx", "messages": [{"ruleId": "better-tailwindcss/multiline", "severity": 1, "message": "Unnecessary line wrapping. Expected\n\n`↵\n······flex·h-10·items-center·space-x-1·rounded-md·border·bg-background·p-1↵\n····`\n\nto be\n\n`flex·h-10·items-center·space-x-1·rounded-md·border·bg-background·p-1`", "line": 26, "column": 7, "nodeType": null, "endLine": 28, "endColumn": 6, "fix": {"range": [679, 761], "text": "`flex h-10 items-center space-x-1 rounded-md border bg-background p-1`"}}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=open]:animate-in", "line": 90, "column": 9, "nodeType": null, "endLine": 90, "endColumn": 37}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=open]:fade-in-0", "line": 90, "column": 38, "nodeType": null, "endLine": 90, "endColumn": 65}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=open]:zoom-in-95", "line": 91, "column": 9, "nodeType": null, "endLine": 91, "endColumn": 37}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=closed]:animate-out", "line": 92, "column": 9, "nodeType": null, "endLine": 92, "endColumn": 40}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=closed]:fade-out-0", "line": 92, "column": 41, "nodeType": null, "endLine": 92, "endColumn": 71}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=closed]:zoom-out-95", "line": 93, "column": 9, "nodeType": null, "endLine": 93, "endColumn": 40}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[side=bottom]:slide-in-from-top-2", "line": 94, "column": 9, "nodeType": null, "endLine": 94, "endColumn": 47}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[side=left]:slide-in-from-right-2", "line": 95, "column": 9, "nodeType": null, "endLine": 95, "endColumn": 47}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[side=right]:slide-in-from-left-2", "line": 96, "column": 9, "nodeType": null, "endLine": 96, "endColumn": 47}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[side=top]:slide-in-from-bottom-2", "line": 97, "column": 9, "nodeType": null, "endLine": 97, "endColumn": 47}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=open]:animate-in", "line": 120, "column": 11, "nodeType": null, "endLine": 120, "endColumn": 39}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=open]:fade-in-0", "line": 120, "column": 40, "nodeType": null, "endLine": 120, "endColumn": 67}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=open]:zoom-in-95", "line": 121, "column": 11, "nodeType": null, "endLine": 121, "endColumn": 39}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=closed]:fade-out-0", "line": 122, "column": 11, "nodeType": null, "endLine": 122, "endColumn": 41}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=closed]:zoom-out-95", "line": 122, "column": 42, "nodeType": null, "endLine": 122, "endColumn": 73}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[side=bottom]:slide-in-from-top-2", "line": 123, "column": 11, "nodeType": null, "endLine": 123, "endColumn": 49}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[side=left]:slide-in-from-right-2", "line": 124, "column": 11, "nodeType": null, "endLine": 124, "endColumn": 49}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[side=right]:slide-in-from-left-2", "line": 125, "column": 11, "nodeType": null, "endLine": 125, "endColumn": 49}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[side=top]:slide-in-from-bottom-2", "line": 126, "column": 11, "nodeType": null, "endLine": 126, "endColumn": 49}, {"ruleId": "better-tailwindcss/multiline", "severity": 1, "message": "Unnecessary line wrapping. Expected\n\n`↵\n······absolute·left-2·flex·h-3.5·w-3.5·items-center·justify-center↵\n····`\n\nto be\n\n`absolute·left-2·flex·h-3.5·w-3.5·items-center·justify-center`", "line": 180, "column": 18, "nodeType": null, "endLine": 182, "endColumn": 6, "fix": {"range": [5633, 5707], "text": "`absolute left-2 flex h-3.5 w-3.5 items-center justify-center`"}}, {"ruleId": "better-tailwindcss/multiline", "severity": 1, "message": "Unnecessary line wrapping. Expected\n\n`↵\n······absolute·left-2·flex·h-3.5·w-3.5·items-center·justify-center↵\n····`\n\nto be\n\n`absolute·left-2·flex·h-3.5·w-3.5·items-center·justify-center`", "line": 211, "column": 18, "nodeType": null, "endLine": 213, "endColumn": 6, "fix": {"range": [6578, 6652], "text": "`absolute left-2 flex h-3.5 w-3.5 items-center justify-center`"}}, {"ruleId": "better-tailwindcss/multiline", "severity": 1, "message": "Unnecessary line wrapping. Expected\n\n`↵\n····ml-auto·text-xs·tracking-widest·text-muted-foreground↵\n··`\n\nto be\n\n`ml-auto·text-xs·tracking-widest·text-muted-foreground`", "line": 250, "column": 9, "nodeType": null, "endLine": 252, "endColumn": 4, "fix": {"range": [7891, 7954], "text": "`ml-auto text-xs tracking-widest text-muted-foreground`"}}], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 23, "fixableErrorCount": 0, "fixableWarningCount": 4, "source": "'use client'\n\nimport * as MenubarPrimitive from '@radix-ui/react-menubar'\nimport { Check, ChevronRight, Circle } from 'lucide-react'\nimport * as React from 'react'\n\nimport { cn } from '@/lib/utils'\n\nconst MenubarMenu = MenubarPrimitive.Menu\n\nconst MenubarGroup = MenubarPrimitive.Group\n\nconst MenubarPortal = MenubarPrimitive.Portal\n\nconst MenubarSub = MenubarPrimitive.Sub\n\nconst MenubarRadioGroup = MenubarPrimitive.RadioGroup\n\nconst Menubar = React.forwardRef<\n  React.ElementRef<typeof MenubarPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof MenubarPrimitive.Root>\n>(({ className, ...props }, ref) => (\n  <MenubarPrimitive.Root\n    ref={ref}\n    className={cn(\n      `\n      flex h-10 items-center space-x-1 rounded-md border bg-background p-1\n    `,\n      className\n    )}\n    {...props}\n  />\n))\nMenubar.displayName = MenubarPrimitive.Root.displayName\n\nconst MenubarTrigger = React.forwardRef<\n  React.ElementRef<typeof MenubarPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof MenubarPrimitive.Trigger>\n>(({ className, ...props }, ref) => (\n  <MenubarPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      `\n        flex cursor-default items-center rounded-sm px-3 py-1.5 text-sm\n        font-medium outline-hidden select-none\n        focus:bg-accent focus:text-accent-foreground\n        data-[state=open]:bg-accent data-[state=open]:text-accent-foreground\n      `,\n      className\n    )}\n    {...props}\n  />\n))\nMenubarTrigger.displayName = MenubarPrimitive.Trigger.displayName\n\nconst MenubarSubTrigger = React.forwardRef<\n  React.ElementRef<typeof MenubarPrimitive.SubTrigger>,\n  React.ComponentPropsWithoutRef<typeof MenubarPrimitive.SubTrigger> & {\n    inset?: boolean\n  }\n>(({ className, inset, children, ...props }, ref) => (\n  <MenubarPrimitive.SubTrigger\n    ref={ref}\n    className={cn(\n      `\n        flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm\n        outline-hidden select-none\n        focus:bg-accent focus:text-accent-foreground\n        data-[state=open]:bg-accent data-[state=open]:text-accent-foreground\n      `,\n      inset && 'pl-8',\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <ChevronRight className='ml-auto h-4 w-4' />\n  </MenubarPrimitive.SubTrigger>\n))\nMenubarSubTrigger.displayName = MenubarPrimitive.SubTrigger.displayName\n\nconst MenubarSubContent = React.forwardRef<\n  React.ElementRef<typeof MenubarPrimitive.SubContent>,\n  React.ComponentPropsWithoutRef<typeof MenubarPrimitive.SubContent>\n>(({ className, ...props }, ref) => (\n  <MenubarPrimitive.SubContent\n    ref={ref}\n    className={cn(\n      `\n        data-[state=open]:animate-in data-[state=open]:fade-in-0\n        data-[state=open]:zoom-in-95\n        data-[state=closed]:animate-out data-[state=closed]:fade-out-0\n        data-[state=closed]:zoom-out-95\n        data-[side=bottom]:slide-in-from-top-2\n        data-[side=left]:slide-in-from-right-2\n        data-[side=right]:slide-in-from-left-2\n        data-[side=top]:slide-in-from-bottom-2\n        z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1\n        text-popover-foreground\n      `,\n      className\n    )}\n    {...props}\n  />\n))\nMenubarSubContent.displayName = MenubarPrimitive.SubContent.displayName\n\nconst MenubarContent = React.forwardRef<\n  React.ElementRef<typeof MenubarPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof MenubarPrimitive.Content>\n>(({ className, align = 'start', alignOffset = -4, sideOffset = 8, ...props }, ref) => (\n  <MenubarPrimitive.Portal>\n    <MenubarPrimitive.Content\n      ref={ref}\n      align={align}\n      alignOffset={alignOffset}\n      sideOffset={sideOffset}\n      className={cn(\n        `\n          data-[state=open]:animate-in data-[state=open]:fade-in-0\n          data-[state=open]:zoom-in-95\n          data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95\n          data-[side=bottom]:slide-in-from-top-2\n          data-[side=left]:slide-in-from-right-2\n          data-[side=right]:slide-in-from-left-2\n          data-[side=top]:slide-in-from-bottom-2\n          z-50 min-w-[12rem] overflow-hidden rounded-md border bg-popover p-1\n          text-popover-foreground shadow-md\n        `,\n        className\n      )}\n      {...props}\n    />\n  </MenubarPrimitive.Portal>\n))\nMenubarContent.displayName = MenubarPrimitive.Content.displayName\n\nconst MenubarItem = React.forwardRef<\n  React.ElementRef<typeof MenubarPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof MenubarPrimitive.Item> & {\n    inset?: boolean\n  }\n>(({ className, inset, ...props }, ref) => (\n  <MenubarPrimitive.Item\n    ref={ref}\n    className={cn(\n      `\n        relative flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm\n        outline-hidden select-none\n        focus:bg-accent focus:text-accent-foreground\n        data-disabled:pointer-events-none data-disabled:opacity-50\n      `,\n      inset && 'pl-8',\n      className\n    )}\n    {...props}\n  />\n))\nMenubarItem.displayName = MenubarPrimitive.Item.displayName\n\nconst MenubarCheckboxItem = React.forwardRef<\n  React.ElementRef<typeof MenubarPrimitive.CheckboxItem>,\n  React.ComponentPropsWithoutRef<typeof MenubarPrimitive.CheckboxItem>\n>(({ className, children, checked, ...props }, ref) => (\n  <MenubarPrimitive.CheckboxItem\n    ref={ref}\n    className={cn(\n      `\n        relative flex cursor-default items-center rounded-sm py-1.5 pr-2 pl-8\n        text-sm outline-hidden select-none\n        focus:bg-accent focus:text-accent-foreground\n        data-disabled:pointer-events-none data-disabled:opacity-50\n      `,\n      className\n    )}\n    checked={checked}\n    {...props}\n  >\n    <span\n      className={`\n      absolute left-2 flex h-3.5 w-3.5 items-center justify-center\n    `}\n    >\n      <MenubarPrimitive.ItemIndicator>\n        <Check className='h-4 w-4' />\n      </MenubarPrimitive.ItemIndicator>\n    </span>\n    {children}\n  </MenubarPrimitive.CheckboxItem>\n))\nMenubarCheckboxItem.displayName = MenubarPrimitive.CheckboxItem.displayName\n\nconst MenubarRadioItem = React.forwardRef<\n  React.ElementRef<typeof MenubarPrimitive.RadioItem>,\n  React.ComponentPropsWithoutRef<typeof MenubarPrimitive.RadioItem>\n>(({ className, children, ...props }, ref) => (\n  <MenubarPrimitive.RadioItem\n    ref={ref}\n    className={cn(\n      `\n        relative flex cursor-default items-center rounded-sm py-1.5 pr-2 pl-8\n        text-sm outline-hidden select-none\n        focus:bg-accent focus:text-accent-foreground\n        data-disabled:pointer-events-none data-disabled:opacity-50\n      `,\n      className\n    )}\n    {...props}\n  >\n    <span\n      className={`\n      absolute left-2 flex h-3.5 w-3.5 items-center justify-center\n    `}\n    >\n      <MenubarPrimitive.ItemIndicator>\n        <Circle className='h-2 w-2 fill-current' />\n      </MenubarPrimitive.ItemIndicator>\n    </span>\n    {children}\n  </MenubarPrimitive.RadioItem>\n))\nMenubarRadioItem.displayName = MenubarPrimitive.RadioItem.displayName\n\nconst MenubarLabel = React.forwardRef<\n  React.ElementRef<typeof MenubarPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof MenubarPrimitive.Label> & {\n    inset?: boolean\n  }\n>(({ className, inset, ...props }, ref) => (\n  <MenubarPrimitive.Label\n    ref={ref}\n    className={cn('px-2 py-1.5 text-sm font-semibold', inset && 'pl-8', className)}\n    {...props}\n  />\n))\nMenubarLabel.displayName = MenubarPrimitive.Label.displayName\n\nconst MenubarSeparator = React.forwardRef<\n  React.ElementRef<typeof MenubarPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof MenubarPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <MenubarPrimitive.Separator ref={ref} className={cn('-mx-1 my-1 h-px bg-muted', className)} {...props} />\n))\nMenubarSeparator.displayName = MenubarPrimitive.Separator.displayName\n\nconst MenubarShortcut = ({ className, ...props }: React.HTMLAttributes<HTMLSpanElement>) => {\n  return (\n    <span\n      className={cn(\n        `\n    ml-auto text-xs tracking-widest text-muted-foreground\n  `,\n        className\n      )}\n      {...props}\n    />\n  )\n}\nMenubarShortcut.displayname = 'MenubarShortcut'\n\nexport {\n  Menubar,\n  MenubarMenu,\n  MenubarTrigger,\n  MenubarContent,\n  MenubarItem,\n  MenubarSeparator,\n  MenubarLabel,\n  MenubarCheckboxItem,\n  MenubarRadioGroup,\n  MenubarRadioItem,\n  MenubarPortal,\n  MenubarSubContent,\n  MenubarSubTrigger,\n  MenubarGroup,\n  MenubarSub,\n  MenubarShortcut,\n}\n", "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/navigation-menu.tsx", "messages": [{"ruleId": "better-tailwindcss/multiline", "severity": 1, "message": "Unnecessary line wrapping. Expected\n\n`↵\n······relative·z-10·flex·max-w-max·flex-1·items-center·justify-center↵\n····`\n\nto be\n\n`relative·z-10·flex·max-w-max·flex-1·items-center·justify-center`", "line": 15, "column": 7, "nodeType": null, "endLine": 17, "endColumn": 6, "fix": {"range": [518, 595], "text": "`relative z-10 flex max-w-max flex-1 items-center justify-center`"}}, {"ruleId": "better-tailwindcss/multiline", "severity": 1, "message": "Unnecessary line wrapping. Expected\n\n`↵\n······group·flex·flex-1·list-none·items-center·justify-center·space-x-1↵\n····`\n\nto be\n\n`group·flex·flex-1·list-none·items-center·justify-center·space-x-1`", "line": 35, "column": 7, "nodeType": null, "endLine": 37, "endColumn": 6, "fix": {"range": [1074, 1153], "text": "`group flex flex-1 list-none items-center justify-center space-x-1`"}}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[motion^=from-]:animate-in", "line": 88, "column": 9, "nodeType": null, "endLine": 88, "endColumn": 40}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[motion^=from-]:fade-in", "line": 88, "column": 41, "nodeType": null, "endLine": 88, "endColumn": 69}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[motion^=to-]:animate-out", "line": 89, "column": 9, "nodeType": null, "endLine": 89, "endColumn": 39}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[motion^=to-]:fade-out", "line": 89, "column": 40, "nodeType": null, "endLine": 89, "endColumn": 67}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[motion=from-end]:slide-in-from-right-52", "line": 90, "column": 9, "nodeType": null, "endLine": 90, "endColumn": 54}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[motion=from-start]:slide-in-from-left-52", "line": 91, "column": 9, "nodeType": null, "endLine": 91, "endColumn": 55}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[motion=to-end]:slide-out-to-right-52", "line": 92, "column": 9, "nodeType": null, "endLine": 92, "endColumn": 51}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[motion=to-start]:slide-out-to-left-52", "line": 93, "column": 9, "nodeType": null, "endLine": 93, "endColumn": 52}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: origin-top-center", "line": 114, "column": 11, "nodeType": null, "endLine": 114, "endColumn": 28}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=open]:animate-in", "line": 117, "column": 11, "nodeType": null, "endLine": 117, "endColumn": 39}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=open]:zoom-in-90", "line": 117, "column": 40, "nodeType": null, "endLine": 117, "endColumn": 68}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=closed]:animate-out", "line": 118, "column": 11, "nodeType": null, "endLine": 118, "endColumn": 42}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=closed]:zoom-out-95", "line": 118, "column": 43, "nodeType": null, "endLine": 118, "endColumn": 74}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=visible]:animate-in", "line": 138, "column": 9, "nodeType": null, "endLine": 138, "endColumn": 40}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=visible]:fade-in", "line": 138, "column": 41, "nodeType": null, "endLine": 138, "endColumn": 69}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=hidden]:animate-out", "line": 139, "column": 9, "nodeType": null, "endLine": 139, "endColumn": 40}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=hidden]:fade-out", "line": 139, "column": 41, "nodeType": null, "endLine": 139, "endColumn": 69}], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 19, "fixableErrorCount": 0, "fixableWarningCount": 2, "source": "import * as NavigationMenuPrimitive from '@radix-ui/react-navigation-menu'\nimport { cva } from 'class-variance-authority'\nimport { ChevronDown } from 'lucide-react'\nimport * as React from 'react'\n\nimport { cn } from '@/lib/utils'\n\nconst NavigationMenu = React.forwardRef<\n  React.ElementRef<typeof NavigationMenuPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof NavigationMenuPrimitive.Root>\n>(({ className, children, ...props }, ref) => (\n  <NavigationMenuPrimitive.Root\n    ref={ref}\n    className={cn(\n      `\n      relative z-10 flex max-w-max flex-1 items-center justify-center\n    `,\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <NavigationMenuViewport />\n  </NavigationMenuPrimitive.Root>\n))\nNavigationMenu.displayName = NavigationMenuPrimitive.Root.displayName\n\nconst NavigationMenuList = React.forwardRef<\n  React.ElementRef<typeof NavigationMenuPrimitive.List>,\n  React.ComponentPropsWithoutRef<typeof NavigationMenuPrimitive.List>\n>(({ className, ...props }, ref) => (\n  <NavigationMenuPrimitive.List\n    ref={ref}\n    className={cn(\n      `\n      group flex flex-1 list-none items-center justify-center space-x-1\n    `,\n      className\n    )}\n    {...props}\n  />\n))\nNavigationMenuList.displayName = NavigationMenuPrimitive.List.displayName\n\nconst NavigationMenuItem = NavigationMenuPrimitive.Item\n\nconst navigationMenuTriggerStyle = cva(\n  `\n    group inline-flex h-10 w-max items-center justify-center rounded-md\n    bg-background px-4 py-2 text-sm font-medium transition-colors\n    hover:bg-accent hover:text-accent-foreground\n    focus:bg-accent focus:text-accent-foreground focus:outline-hidden\n    disabled:pointer-events-none disabled:opacity-50\n    data-active:bg-accent/50\n    data-[state=open]:bg-accent/50\n  `\n)\n\nconst NavigationMenuTrigger = React.forwardRef<\n  React.ElementRef<typeof NavigationMenuPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof NavigationMenuPrimitive.Trigger>\n>(({ className, children, ...props }, ref) => (\n  <NavigationMenuPrimitive.Trigger\n    ref={ref}\n    className={cn(navigationMenuTriggerStyle(), 'group', className)}\n    {...props}\n  >\n    {children}{' '}\n    <ChevronDown\n      className={`\n        relative top-[1px] ml-1 h-3 w-3 transition duration-200\n        group-data-[state=open]:rotate-180\n      `}\n      aria-hidden='true'\n    />\n  </NavigationMenuPrimitive.Trigger>\n))\nNavigationMenuTrigger.displayName = NavigationMenuPrimitive.Trigger.displayName\n\nconst NavigationMenuContent = React.forwardRef<\n  React.ElementRef<typeof NavigationMenuPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof NavigationMenuPrimitive.Content>\n>(({ className, ...props }, ref) => (\n  <NavigationMenuPrimitive.Content\n    ref={ref}\n    className={cn(\n      `\n        data-[motion^=from-]:animate-in data-[motion^=from-]:fade-in\n        data-[motion^=to-]:animate-out data-[motion^=to-]:fade-out\n        data-[motion=from-end]:slide-in-from-right-52\n        data-[motion=from-start]:slide-in-from-left-52\n        data-[motion=to-end]:slide-out-to-right-52\n        data-[motion=to-start]:slide-out-to-left-52\n        top-0 left-0 w-full\n        md:absolute md:w-auto\n      `,\n      className\n    )}\n    {...props}\n  />\n))\nNavigationMenuContent.displayName = NavigationMenuPrimitive.Content.displayName\n\nconst NavigationMenuLink = NavigationMenuPrimitive.Link\n\nconst NavigationMenuViewport = React.forwardRef<\n  React.ElementRef<typeof NavigationMenuPrimitive.Viewport>,\n  React.ComponentPropsWithoutRef<typeof NavigationMenuPrimitive.Viewport>\n>(({ className, ...props }, ref) => (\n  <div className={cn('absolute top-full left-0 flex justify-center')}>\n    <NavigationMenuPrimitive.Viewport\n      className={cn(\n        `\n          origin-top-center relative mt-1.5\n          h-(--radix-navigation-menu-viewport-height) w-full overflow-hidden\n          rounded-md border bg-popover text-popover-foreground shadow-lg\n          data-[state=open]:animate-in data-[state=open]:zoom-in-90\n          data-[state=closed]:animate-out data-[state=closed]:zoom-out-95\n          md:w-(--radix-navigation-menu-viewport-width)\n        `,\n        className\n      )}\n      ref={ref}\n      {...props}\n    />\n  </div>\n))\nNavigationMenuViewport.displayName = NavigationMenuPrimitive.Viewport.displayName\n\nconst NavigationMenuIndicator = React.forwardRef<\n  React.ElementRef<typeof NavigationMenuPrimitive.Indicator>,\n  React.ComponentPropsWithoutRef<typeof NavigationMenuPrimitive.Indicator>\n>(({ className, ...props }, ref) => (\n  <NavigationMenuPrimitive.Indicator\n    ref={ref}\n    className={cn(\n      `\n        data-[state=visible]:animate-in data-[state=visible]:fade-in\n        data-[state=hidden]:animate-out data-[state=hidden]:fade-out\n        top-full z-1 flex h-1.5 items-end justify-center overflow-hidden\n      `,\n      className\n    )}\n    {...props}\n  >\n    <div\n      className={`\n        relative top-[60%] h-2 w-2 rotate-45 rounded-tl-sm bg-border shadow-md\n      `}\n    />\n  </NavigationMenuPrimitive.Indicator>\n))\nNavigationMenuIndicator.displayName = NavigationMenuPrimitive.Indicator.displayName\n\nexport {\n  navigationMenuTriggerStyle,\n  NavigationMenu,\n  NavigationMenuList,\n  NavigationMenuItem,\n  NavigationMenuContent,\n  NavigationMenuTrigger,\n  NavigationMenuLink,\n  NavigationMenuIndicator,\n  NavigationMenuViewport,\n}\n", "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/number-ticker.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/pagination.tsx", "messages": [{"ruleId": "better-tailwindcss/multiline", "severity": 1, "message": "Unnecessary line wrapping. Expected\n\n`↵\n····gap-1·pl-2.5↵\n··`\n\nto be\n\n`gap-1·pl-2.5`", "line": 55, "column": 7, "nodeType": null, "endLine": 57, "endColumn": 4, "fix": {"range": [1702, 1724], "text": "`gap-1 pl-2.5`"}}, {"ruleId": "better-tailwindcss/multiline", "severity": 1, "message": "Unnecessary line wrapping. Expected\n\n`↵\n····gap-1·pr-2.5↵\n··`\n\nto be\n\n`gap-1·pr-2.5`", "line": 73, "column": 7, "nodeType": null, "endLine": 75, "endColumn": 4, "fix": {"range": [2105, 2127], "text": "`gap-1 pr-2.5`"}}], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 2, "source": "import { ChevronLeft, ChevronRight, MoreHorizontal } from 'lucide-react'\nimport * as React from 'react'\n\nimport type { ButtonProps } from '@/components/ui/button'\nimport { buttonVariants } from '@/components/ui/button'\nimport { cn } from '@/lib/utils'\n\nconst Pagination = ({ className, ...props }: React.ComponentProps<'nav'>) => (\n  <nav\n    role='navigation'\n    aria-label='pagination'\n    className={cn('mx-auto flex w-full justify-center', className)}\n    {...props}\n  />\n)\nPagination.displayName = 'Pagination'\n\nconst PaginationContent = React.forwardRef<HTMLUListElement, React.ComponentProps<'ul'>>(\n  ({ className, ...props }, ref) => (\n    <ul ref={ref} className={cn('flex flex-row items-center gap-1', className)} {...props} />\n  )\n)\nPaginationContent.displayName = 'PaginationContent'\n\nconst PaginationItem = React.forwardRef<HTMLLIElement, React.ComponentProps<'li'>>(({ className, ...props }, ref) => (\n  <li ref={ref} className={cn('', className)} {...props} />\n))\nPaginationItem.displayName = 'PaginationItem'\n\ntype PaginationLinkProps = {\n  isActive?: boolean\n} & Pick<ButtonProps, 'size'> &\n  React.ComponentProps<'a'>\n\nconst PaginationLink = ({ className, isActive, size = 'icon', ...props }: PaginationLinkProps) => (\n  <a\n    aria-current={isActive ? 'page' : undefined}\n    className={cn(\n      buttonVariants({\n        variant: isActive ? 'outline' : 'ghost',\n        size,\n      }),\n      className\n    )}\n    {...props}\n  />\n)\nPaginationLink.displayName = 'PaginationLink'\n\nconst PaginationPrevious = ({ className, ...props }: React.ComponentProps<typeof PaginationLink>) => (\n  <PaginationLink\n    aria-label='Go to previous page'\n    size='default'\n    className={cn(\n      `\n    gap-1 pl-2.5\n  `,\n      className\n    )}\n    {...props}\n  >\n    <ChevronLeft className='h-4 w-4' />\n    <span>Previous</span>\n  </PaginationLink>\n)\nPaginationPrevious.displayName = 'PaginationPrevious'\n\nconst PaginationNext = ({ className, ...props }: React.ComponentProps<typeof PaginationLink>) => (\n  <PaginationLink\n    aria-label='Go to next page'\n    size='default'\n    className={cn(\n      `\n    gap-1 pr-2.5\n  `,\n      className\n    )}\n    {...props}\n  >\n    <span>Next</span>\n    <ChevronRight className='h-4 w-4' />\n  </PaginationLink>\n)\nPaginationNext.displayName = 'PaginationNext'\n\nconst PaginationEllipsis = ({ className, ...props }: React.ComponentProps<'span'>) => (\n  <span aria-hidden className={cn('flex h-9 w-9 items-center justify-center', className)} {...props}>\n    <MoreHorizontal className='h-4 w-4' />\n    <span className='sr-only'>More pages</span>\n  </span>\n)\nPaginationEllipsis.displayName = 'PaginationEllipsis'\n\nexport {\n  Pagination,\n  PaginationContent,\n  PaginationEllipsis,\n  PaginationItem,\n  PaginationLink,\n  PaginationNext,\n  PaginationPrevious,\n}\n", "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/popover.tsx", "messages": [{"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=open]:animate-in", "line": 23, "column": 11, "nodeType": null, "endLine": 23, "endColumn": 39}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=open]:fade-in-0", "line": 23, "column": 40, "nodeType": null, "endLine": 23, "endColumn": 67}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=open]:zoom-in-95", "line": 24, "column": 11, "nodeType": null, "endLine": 24, "endColumn": 39}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=closed]:animate-out", "line": 25, "column": 11, "nodeType": null, "endLine": 25, "endColumn": 42}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=closed]:fade-out-0", "line": 25, "column": 43, "nodeType": null, "endLine": 25, "endColumn": 73}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=closed]:zoom-out-95", "line": 26, "column": 11, "nodeType": null, "endLine": 26, "endColumn": 42}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[side=bottom]:slide-in-from-top-2", "line": 27, "column": 11, "nodeType": null, "endLine": 27, "endColumn": 49}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[side=left]:slide-in-from-right-2", "line": 28, "column": 11, "nodeType": null, "endLine": 28, "endColumn": 49}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[side=right]:slide-in-from-left-2", "line": 29, "column": 11, "nodeType": null, "endLine": 29, "endColumn": 49}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[side=top]:slide-in-from-bottom-2", "line": 30, "column": 11, "nodeType": null, "endLine": 30, "endColumn": 49}], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "'use client'\n\nimport * as PopoverPrimitive from '@radix-ui/react-popover'\nimport * as React from 'react'\n\nimport { cn } from '@/lib/utils'\n\nconst Popover = PopoverPrimitive.Root\n\nconst PopoverTrigger = PopoverPrimitive.Trigger\n\nconst PopoverContent = React.forwardRef<\n  React.ElementRef<typeof PopoverPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof PopoverPrimitive.Content>\n>(({ className, align = 'center', sideOffset = 4, ...props }, ref) => (\n  <PopoverPrimitive.Portal>\n    <PopoverPrimitive.Content\n      ref={ref}\n      align={align}\n      sideOffset={sideOffset}\n      className={cn(\n        `\n          data-[state=open]:animate-in data-[state=open]:fade-in-0\n          data-[state=open]:zoom-in-95\n          data-[state=closed]:animate-out data-[state=closed]:fade-out-0\n          data-[state=closed]:zoom-out-95\n          data-[side=bottom]:slide-in-from-top-2\n          data-[side=left]:slide-in-from-right-2\n          data-[side=right]:slide-in-from-left-2\n          data-[side=top]:slide-in-from-bottom-2\n          z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground\n          shadow-md outline-hidden\n        `,\n        className\n      )}\n      {...props}\n    />\n  </PopoverPrimitive.Portal>\n))\nPopoverContent.displayName = PopoverPrimitive.Content.displayName\n\nexport { Popover, PopoverTrigger, PopoverContent }\n", "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/progress.tsx", "messages": [{"ruleId": "better-tailwindcss/multiline", "severity": 1, "message": "Unnecessary line wrapping. Expected\n\n`↵\n······relative·h-4·w-full·overflow-hidden·rounded-full·bg-secondary↵\n····`\n\nto be\n\n`relative·h-4·w-full·overflow-hidden·rounded-full·bg-secondary`", "line": 15, "column": 7, "nodeType": null, "endLine": 17, "endColumn": 6, "fix": {"range": [402, 477], "text": "`relative h-4 w-full overflow-hidden rounded-full bg-secondary`"}}], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": "'use client'\n\nimport * as ProgressPrimitive from '@radix-ui/react-progress'\nimport * as React from 'react'\n\nimport { cn } from '@/lib/utils'\n\nconst Progress = React.forwardRef<\n  React.ElementRef<typeof ProgressPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof ProgressPrimitive.Root>\n>(({ className, value, ...props }, ref) => (\n  <ProgressPrimitive.Root\n    ref={ref}\n    className={cn(\n      `\n      relative h-4 w-full overflow-hidden rounded-full bg-secondary\n    `,\n      className\n    )}\n    {...props}\n  >\n    <ProgressPrimitive.Indicator\n      className='h-full w-full flex-1 bg-primary transition-all'\n      style={{ transform: `translateX(-${100 - (value || 0)}%)` }}\n    />\n  </ProgressPrimitive.Root>\n))\nProgress.displayName = ProgressPrimitive.Root.displayName\n\nexport { Progress }\n", "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/pulsating-button.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/radio-group.tsx", "messages": [{"ruleId": "better-tailwindcss/multiline", "severity": 1, "message": "Unnecessary line wrapping. Expected\n\n`↵\n········flex·items-center·justify-center↵\n······`\n\nto be\n\n`flex·items-center·justify-center`", "line": 38, "column": 20, "nodeType": null, "endLine": 40, "endColumn": 8, "fix": {"range": [1242, 1292], "text": "`flex items-center justify-center`"}}], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": "'use client'\n\nimport * as RadioGroupPrimitive from '@radix-ui/react-radio-group'\nimport { Circle } from 'lucide-react'\nimport * as React from 'react'\n\nimport { cn } from '@/lib/utils'\n\nconst RadioGroup = React.forwardRef<\n  React.ElementRef<typeof RadioGroupPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof RadioGroupPrimitive.Root>\n>(({ className, ...props }, ref) => {\n  return <RadioGroupPrimitive.Root className={cn('grid gap-2', className)} {...props} ref={ref} />\n})\nRadioGroup.displayName = RadioGroupPrimitive.Root.displayName\n\nconst RadioGroupItem = React.forwardRef<\n  React.ElementRef<typeof RadioGroupPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof RadioGroupPrimitive.Item>\n>(({ className, ...props }, ref) => {\n  return (\n    <RadioGroupPrimitive.Item\n      ref={ref}\n      className={cn(\n        `\n          aspect-square h-4 w-4 rounded-full border border-primary text-primary\n          ring-offset-background\n          focus:outline-hidden\n          focus-visible:ring-2 focus-visible:ring-ring\n          focus-visible:ring-offset-2\n          disabled:cursor-not-allowed disabled:opacity-50\n        `,\n        className\n      )}\n      {...props}\n    >\n      <RadioGroupPrimitive.Indicator\n        className={`\n        flex items-center justify-center\n      `}\n      >\n        <Circle className='h-2.5 w-2.5 fill-current text-current' />\n      </RadioGroupPrimitive.Indicator>\n    </RadioGroupPrimitive.Item>\n  )\n})\nRadioGroupItem.displayName = RadioGroupPrimitive.Item.displayName\n\nexport { RadioGroup, RadioGroupItem }\n", "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/resizable.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/script-loader.tsx", "messages": [{"ruleId": "@typescript-eslint/no-unused-vars", "severity": 1, "message": "'props' is defined but never used. Allowed unused args must match /^_/u.", "line": 34, "column": 6, "nodeType": null, "messageId": "unusedVar", "endLine": 34, "endColumn": 11}], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "'use client'\n\nimport Script from 'next/script'\nimport { useEffect, useState } from 'react'\n\ninterface ScriptLoaderProps {\n  src: string\n  id?: string\n  strategy?: 'beforeInteractive' | 'afterInteractive' | 'lazyOnload'\n  onLoad?: () => void\n  onError?: () => void\n  dangerouslySetInnerHTML?: { __html: string }\n  inViewport?: boolean // Only load when in viewport\n  dataSrc?: string\n  dataTestId?: string\n  nonce?: string // Allow passing nonce as a prop\n}\n\n/**\n * OptimizedScript component - A wrapper for Next.js Script component\n * with improved loading strategies and performance optimizations\n */\nexport function ScriptLoader({\n  src,\n  id,\n  strategy = 'afterInteractive',\n  onLoad,\n  onError,\n  dangerouslySetInnerHTML,\n  inViewport = false,\n  dataSrc,\n  dataTestId,\n  nonce: propNonce, // Use the prop name\n  ...props\n}: ScriptLoaderProps) {\n  const [shouldLoad, setShouldLoad] = useState(!inViewport)\n  const [effectiveNonce, setEffectiveNonce] = useState(propNonce || '')\n\n  useEffect(() => {\n    if (!propNonce) {\n      // If nonce is not passed as a prop, try to get it from the meta tag\n      const metaNonce = document.querySelector('meta[name=\"csp-nonce\"]')?.getAttribute('content') || ''\n      setEffectiveNonce(metaNonce)\n    }\n  }, [propNonce]) // Re-run if propNonce changes (though unlikely for this use case)\n\n  useEffect(() => {\n    if (inViewport) {\n      // Create intersection observer for viewport detection\n      const observer = new IntersectionObserver(entries => {\n        entries.forEach(entry => {\n          if (entry.isIntersecting) {\n            setShouldLoad(true)\n            observer.disconnect()\n          }\n        })\n      })\n\n      // Create a div element to observe\n      const element = document.createElement('div')\n      element.id = `script-observer-${id || Math.random().toString(36).substring(2)}`\n      element.style.height = '1px'\n      element.style.width = '1px'\n      element.style.position = 'absolute'\n      element.style.bottom = '200px' // Load slightly before scrolling to it\n      element.style.left = '0'\n      document.body.appendChild(element)\n\n      observer.observe(element)\n\n      return () => {\n        observer.disconnect()\n        if (document.body.contains(element)) {\n          document.body.removeChild(element)\n        }\n      }\n    }\n  }, [id, inViewport])\n\n  if (!shouldLoad) {\n    return null\n  }\n\n  return (\n    <Script\n      id={id}\n      src={src}\n      strategy={strategy}\n      onLoad={onLoad}\n      onError={onError}\n      dangerouslySetInnerHTML={dangerouslySetInnerHTML}\n      data-src={dataSrc}\n      data-testid={dataTestId}\n      nonce={dangerouslySetInnerHTML ? effectiveNonce : undefined} // Use effectiveNonce\n    />\n  )\n}\n\nexport default ScriptLoader\n", "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/scroll-area.tsx", "messages": [{"ruleId": "better-tailwindcss/multiline", "severity": 1, "message": "Unnecessary line wrapping. Expected\n\n`↵\n······relative·flex-1·rounded-full·bg-border↵\n····`\n\nto be\n\n`relative·flex-1·rounded-full·bg-border`", "line": 36, "column": 18, "nodeType": null, "endLine": 38, "endColumn": 6, "fix": {"range": [1418, 1470], "text": "`relative flex-1 rounded-full bg-border`"}}], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": "'use client'\n\nimport * as ScrollAreaPrimitive from '@radix-ui/react-scroll-area'\nimport * as React from 'react'\n\nimport { cn } from '@/lib/utils'\n\nconst ScrollArea = React.forwardRef<\n  React.ElementRef<typeof ScrollAreaPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof ScrollAreaPrimitive.Root>\n>(({ className, children, ...props }, ref) => (\n  <ScrollAreaPrimitive.Root ref={ref} className={cn('relative overflow-hidden', className)} {...props}>\n    <ScrollAreaPrimitive.Viewport className='h-full w-full rounded-[inherit]'>{children}</ScrollAreaPrimitive.Viewport>\n    <ScrollBar />\n    <ScrollAreaPrimitive.Corner />\n  </ScrollAreaPrimitive.Root>\n))\nScrollArea.displayName = ScrollAreaPrimitive.Root.displayName\n\nconst ScrollBar = React.forwardRef<\n  React.ElementRef<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>,\n  React.ComponentPropsWithoutRef<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>\n>(({ className, orientation = 'vertical', ...props }, ref) => (\n  <ScrollAreaPrimitive.ScrollAreaScrollbar\n    ref={ref}\n    orientation={orientation}\n    className={cn(\n      'flex touch-none transition-colors select-none',\n      orientation === 'vertical' && 'h-full w-2.5 border-l border-l-transparent p-[1px]',\n      orientation === 'horizontal' && 'h-2.5 flex-col border-t border-t-transparent p-[1px]',\n      className\n    )}\n    {...props}\n  >\n    <ScrollAreaPrimitive.ScrollAreaThumb\n      className={`\n      relative flex-1 rounded-full bg-border\n    `}\n    />\n  </ScrollAreaPrimitive.ScrollAreaScrollbar>\n))\nScrollBar.displayName = ScrollAreaPrimitive.ScrollAreaScrollbar.displayName\n\nexport { ScrollArea, ScrollBar }\n", "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/select.tsx", "messages": [{"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=open]:animate-in", "line": 79, "column": 11, "nodeType": null, "endLine": 79, "endColumn": 39}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=open]:fade-in-0", "line": 79, "column": 40, "nodeType": null, "endLine": 79, "endColumn": 67}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=open]:zoom-in-95", "line": 80, "column": 11, "nodeType": null, "endLine": 80, "endColumn": 39}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=closed]:animate-out", "line": 81, "column": 11, "nodeType": null, "endLine": 81, "endColumn": 42}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=closed]:fade-out-0", "line": 81, "column": 43, "nodeType": null, "endLine": 81, "endColumn": 73}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=closed]:zoom-out-95", "line": 82, "column": 11, "nodeType": null, "endLine": 82, "endColumn": 42}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[side=bottom]:slide-in-from-top-2", "line": 83, "column": 11, "nodeType": null, "endLine": 83, "endColumn": 49}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[side=left]:slide-in-from-right-2", "line": 84, "column": 11, "nodeType": null, "endLine": 84, "endColumn": 49}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[side=right]:slide-in-from-left-2", "line": 85, "column": 11, "nodeType": null, "endLine": 85, "endColumn": 49}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[side=top]:slide-in-from-bottom-2", "line": 86, "column": 11, "nodeType": null, "endLine": 86, "endColumn": 49}, {"ruleId": "better-tailwindcss/multiline", "severity": 1, "message": "Unnecessary line wrapping. Expected\n\n`↵\n····py-1.5·pr-2·pl-8·text-sm·font-semibold↵\n··`\n\nto be\n\n`py-1.5·pr-2·pl-8·text-sm·font-semibold`", "line": 119, "column": 7, "nodeType": null, "endLine": 121, "endColumn": 4, "fix": {"range": [4139, 4187], "text": "`py-1.5 pr-2 pl-8 text-sm font-semibold`"}}, {"ruleId": "better-tailwindcss/multiline", "severity": 1, "message": "Unnecessary line wrapping. Expected\n\n`↵\n······absolute·left-2·flex·h-3.5·w-3.5·items-center·justify-center↵\n····`\n\nto be\n\n`absolute·left-2·flex·h-3.5·w-3.5·items-center·justify-center`", "line": 147, "column": 18, "nodeType": null, "endLine": 149, "endColumn": 6, "fix": {"range": [4883, 4957], "text": "`absolute left-2 flex h-3.5 w-3.5 items-center justify-center`"}}], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 2, "source": "'use client'\n\nimport * as SelectPrimitive from '@radix-ui/react-select'\nimport { Check, ChevronDown, ChevronUp } from 'lucide-react'\nimport * as React from 'react'\n\nimport { cn } from '@/lib/utils'\n\nconst Select = SelectPrimitive.Root\n\nconst SelectGroup = SelectPrimitive.Group\n\nconst SelectValue = SelectPrimitive.Value\n\nconst SelectTrigger = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      `\n        flex h-10 w-full items-center justify-between rounded-md border\n        border-input bg-background px-3 py-2 text-sm ring-offset-background\n        placeholder:text-muted-foreground\n        focus:ring-2 focus:ring-ring focus:ring-offset-2 focus:outline-hidden\n        disabled:cursor-not-allowed disabled:opacity-50\n        [&>span]:line-clamp-1\n      `,\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <SelectPrimitive.Icon asChild>\n      <ChevronDown className='h-4 w-4 opacity-50' />\n    </SelectPrimitive.Icon>\n  </SelectPrimitive.Trigger>\n))\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName\n\nconst SelectScrollUpButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollUpButton\n    ref={ref}\n    className={cn('flex cursor-default items-center justify-center py-1', className)}\n    {...props}\n  >\n    <ChevronUp className='h-4 w-4' />\n  </SelectPrimitive.ScrollUpButton>\n))\nSelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName\n\nconst SelectScrollDownButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollDownButton\n    ref={ref}\n    className={cn('flex cursor-default items-center justify-center py-1', className)}\n    {...props}\n  >\n    <ChevronDown className='h-4 w-4' />\n  </SelectPrimitive.ScrollDownButton>\n))\nSelectScrollDownButton.displayName = SelectPrimitive.ScrollDownButton.displayName\n\nconst SelectContent = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>\n>(({ className, children, position = 'popper', ...props }, ref) => (\n  <SelectPrimitive.Portal>\n    <SelectPrimitive.Content\n      ref={ref}\n      className={cn(\n        `\n          data-[state=open]:animate-in data-[state=open]:fade-in-0\n          data-[state=open]:zoom-in-95\n          data-[state=closed]:animate-out data-[state=closed]:fade-out-0\n          data-[state=closed]:zoom-out-95\n          data-[side=bottom]:slide-in-from-top-2\n          data-[side=left]:slide-in-from-right-2\n          data-[side=right]:slide-in-from-left-2\n          data-[side=top]:slide-in-from-bottom-2\n          relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border\n          bg-popover text-popover-foreground shadow-md\n        `,\n        position === 'popper' &&\n          'data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1',\n        className\n      )}\n      position={position}\n      {...props}\n    >\n      <SelectScrollUpButton />\n      <SelectPrimitive.Viewport\n        className={cn(\n          'p-1',\n          position === 'popper' && 'h-(--radix-select-trigger-height) w-full min-w-(--radix-select-trigger-width)'\n        )}\n      >\n        {children}\n      </SelectPrimitive.Viewport>\n      <SelectScrollDownButton />\n    </SelectPrimitive.Content>\n  </SelectPrimitive.Portal>\n))\nSelectContent.displayName = SelectPrimitive.Content.displayName\n\nconst SelectLabel = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Label\n    ref={ref}\n    className={cn(\n      `\n    py-1.5 pr-2 pl-8 text-sm font-semibold\n  `,\n      className\n    )}\n    {...props}\n  />\n))\nSelectLabel.displayName = SelectPrimitive.Label.displayName\n\nconst SelectItem = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Item\n    ref={ref}\n    className={cn(\n      `\n        relative flex w-full cursor-default items-center rounded-sm py-1.5 pr-2\n        pl-8 text-sm outline-hidden select-none\n        focus:bg-accent focus:text-accent-foreground\n        data-disabled:pointer-events-none data-disabled:opacity-50\n      `,\n      className\n    )}\n    {...props}\n  >\n    <span\n      className={`\n      absolute left-2 flex h-3.5 w-3.5 items-center justify-center\n    `}\n    >\n      <SelectPrimitive.ItemIndicator>\n        <Check className='h-4 w-4' />\n      </SelectPrimitive.ItemIndicator>\n    </span>\n\n    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n  </SelectPrimitive.Item>\n))\nSelectItem.displayName = SelectPrimitive.Item.displayName\n\nconst SelectSeparator = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Separator ref={ref} className={cn('-mx-1 my-1 h-px bg-muted', className)} {...props} />\n))\nSelectSeparator.displayName = SelectPrimitive.Separator.displayName\n\nexport {\n  Select,\n  SelectGroup,\n  SelectValue,\n  SelectTrigger,\n  SelectContent,\n  SelectLabel,\n  SelectItem,\n  SelectSeparator,\n  SelectScrollUpButton,\n  SelectScrollDownButton,\n}\n", "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/separator.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/sheet.tsx", "messages": [{"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=open]:animate-in", "line": 25, "column": 9, "nodeType": null, "endLine": 25, "endColumn": 37}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=open]:fade-in-0", "line": 25, "column": 38, "nodeType": null, "endLine": 25, "endColumn": 65}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=closed]:animate-out", "line": 26, "column": 9, "nodeType": null, "endLine": 26, "endColumn": 40}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=closed]:fade-out-0", "line": 26, "column": 41, "nodeType": null, "endLine": 26, "endColumn": 71}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=open]:animate-in", "line": 39, "column": 5, "nodeType": null, "endLine": 39, "endColumn": 33}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=closed]:animate-out", "line": 40, "column": 5, "nodeType": null, "endLine": 40, "endColumn": 36}, {"ruleId": "better-tailwindcss/multiline", "severity": 1, "message": "Unnecessary line wrapping. Expected\n\n`↵\n····text-lg·font-semibold·text-foreground↵\n··`\n\nto be\n\n`text-lg·font-semibold·text-foreground`", "line": 125, "column": 7, "nodeType": null, "endLine": 127, "endColumn": 4, "fix": {"range": [3790, 3837], "text": "`text-lg font-semibold text-foreground`"}}, {"ruleId": "better-tailwindcss/multiline", "severity": 1, "message": "Unnecessary line wrapping. Expected\n\n`↵\n····text-sm·text-muted-foreground↵\n··`\n\nto be\n\n`text-sm·text-muted-foreground`", "line": 142, "column": 7, "nodeType": null, "endLine": 144, "endColumn": 4, "fix": {"range": [4217, 4256], "text": "`text-sm text-muted-foreground`"}}], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 2, "source": "'use client'\n\nimport * as SheetPrimitive from '@radix-ui/react-dialog'\nimport { cva, type VariantProps } from 'class-variance-authority'\nimport { X } from 'lucide-react'\nimport * as React from 'react'\n\nimport { cn } from '@/lib/utils'\n\nconst Sheet = SheetPrimitive.Root\n\nconst SheetTrigger = SheetPrimitive.Trigger\n\nconst SheetClose = SheetPrimitive.Close\n\nconst SheetPortal = SheetPrimitive.Portal\n\nconst SheetOverlay = React.forwardRef<\n  React.ElementRef<typeof SheetPrimitive.Overlay>,\n  React.ComponentPropsWithoutRef<typeof SheetPrimitive.Overlay>\n>(({ className, ...props }, ref) => (\n  <SheetPrimitive.Overlay\n    className={cn(\n      `\n        data-[state=open]:animate-in data-[state=open]:fade-in-0\n        data-[state=closed]:animate-out data-[state=closed]:fade-out-0\n        fixed inset-0 z-50 bg-black/80\n      `,\n      className\n    )}\n    {...props}\n    ref={ref}\n  />\n))\nSheetOverlay.displayName = SheetPrimitive.Overlay.displayName\n\nconst sheetVariants = cva(\n  `\n    data-[state=open]:animate-in data-[state=open]:duration-500\n    data-[state=closed]:animate-out data-[state=closed]:duration-300\n    fixed z-50 gap-4 bg-background p-6 shadow-lg transition ease-in-out\n  `,\n  {\n    variants: {\n      side: {\n        top: 'data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 border-b',\n        bottom:\n          'data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 border-t',\n        left: 'data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm',\n        right:\n          'data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0  h-full w-3/4 border-l sm:max-w-sm',\n      },\n    },\n    defaultVariants: {\n      side: 'right',\n    },\n  }\n)\n\ninterface SheetContentProps\n  extends React.ComponentPropsWithoutRef<typeof SheetPrimitive.Content>,\n    VariantProps<typeof sheetVariants> {}\n\nconst SheetContent = React.forwardRef<React.ElementRef<typeof SheetPrimitive.Content>, SheetContentProps>(\n  ({ side = 'right', className, children, ...props }, ref) => (\n    <SheetPortal>\n      <SheetOverlay />\n      <SheetPrimitive.Content ref={ref} className={cn(sheetVariants({ side }), className)} {...props}>\n        {children}\n        <SheetPrimitive.Close\n          className={`\n            absolute top-4 right-4 rounded-sm opacity-70 ring-offset-background\n            transition-opacity\n            hover:opacity-100\n            focus:ring-2 focus:ring-ring focus:ring-offset-2\n            focus:outline-hidden\n            disabled:pointer-events-none\n            data-[state=open]:bg-secondary\n          `}\n        >\n          <X className='h-4 w-4' />\n          <span className='sr-only'>Close</span>\n        </SheetPrimitive.Close>\n      </SheetPrimitive.Content>\n    </SheetPortal>\n  )\n)\nSheetContent.displayName = SheetPrimitive.Content.displayName\n\nconst SheetHeader = ({ className, ...props }: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      `\n        flex flex-col space-y-2 text-center\n        sm:text-left\n      `,\n      className\n    )}\n    {...props}\n  />\n)\nSheetHeader.displayName = 'SheetHeader'\n\nconst SheetFooter = ({ className, ...props }: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      `\n        flex flex-col-reverse\n        sm:flex-row sm:justify-end sm:space-x-2\n      `,\n      className\n    )}\n    {...props}\n  />\n)\nSheetFooter.displayName = 'SheetFooter'\n\nconst SheetTitle = React.forwardRef<\n  React.ElementRef<typeof SheetPrimitive.Title>,\n  React.ComponentPropsWithoutRef<typeof SheetPrimitive.Title>\n>(({ className, ...props }, ref) => (\n  <SheetPrimitive.Title\n    ref={ref}\n    className={cn(\n      `\n    text-lg font-semibold text-foreground\n  `,\n      className\n    )}\n    {...props}\n  />\n))\nSheetTitle.displayName = SheetPrimitive.Title.displayName\n\nconst SheetDescription = React.forwardRef<\n  React.ElementRef<typeof SheetPrimitive.Description>,\n  React.ComponentPropsWithoutRef<typeof SheetPrimitive.Description>\n>(({ className, ...props }, ref) => (\n  <SheetPrimitive.Description\n    ref={ref}\n    className={cn(\n      `\n    text-sm text-muted-foreground\n  `,\n      className\n    )}\n    {...props}\n  />\n))\nSheetDescription.displayName = SheetPrimitive.Description.displayName\n\nexport {\n  Sheet,\n  SheetPortal,\n  SheetOverlay,\n  SheetTrigger,\n  SheetClose,\n  SheetContent,\n  SheetHeader,\n  SheetFooter,\n  SheetTitle,\n  SheetDescription,\n}\n", "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/sidebar.tsx", "messages": [{"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: has-data-[variant=inset]:bg-sidebar", "line": 125, "column": 15, "nodeType": null, "endLine": 125, "endColumn": 50}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: bg-sidebar", "line": 155, "column": 13, "nodeType": null, "endLine": 155, "endColumn": 23}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: text-sidebar-foreground", "line": 155, "column": 24, "nodeType": null, "endLine": 155, "endColumn": 47}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: bg-sidebar", "line": 175, "column": 13, "nodeType": null, "endLine": 175, "endColumn": 23}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: text-sidebar-foreground", "line": 175, "column": 24, "nodeType": null, "endLine": 175, "endColumn": 47}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: text-sidebar-foreground", "line": 195, "column": 20, "nodeType": null, "endLine": 195, "endColumn": 43}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: bg-sidebar", "line": 238, "column": 13, "nodeType": null, "endLine": 238, "endColumn": 23}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: group-data-[variant=floating]:border-sidebar-border", "line": 239, "column": 13, "nodeType": null, "endLine": 239, "endColumn": 64}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: hover:after:bg-sidebar-border", "line": 292, "column": 13, "nodeType": null, "endLine": 292, "endColumn": 42}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: hover:group-data-[collapsible=offcanvas]:bg-sidebar", "line": 309, "column": 13, "nodeType": null, "endLine": 309, "endColumn": 64}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: focus-visible:ring-sidebar-ring", "line": 353, "column": 13, "nodeType": null, "endLine": 353, "endColumn": 44}, {"ruleId": "better-tailwindcss/multiline", "severity": 1, "message": "Unnecessary line wrapping. Expected\n\n`↵\n····flex·flex-col·gap-2·p-2↵\n··`\n\nto be\n\n`flex·flex-col·gap-2·p-2`", "line": 371, "column": 9, "nodeType": null, "endLine": 373, "endColumn": 4, "fix": {"range": [11356, 11389], "text": "`flex flex-col gap-2 p-2`"}}, {"ruleId": "better-tailwindcss/multiline", "severity": 1, "message": "Unnecessary line wrapping. Expected\n\n`↵\n····flex·flex-col·gap-2·p-2↵\n··`\n\nto be\n\n`flex·flex-col·gap-2·p-2`", "line": 388, "column": 9, "nodeType": null, "endLine": 390, "endColumn": 4, "fix": {"range": [11707, 11740], "text": "`flex flex-col gap-2 p-2`"}}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: bg-sidebar-border", "line": 405, "column": 24, "nodeType": null, "endLine": 405, "endColumn": 41}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: text-sidebar-foreground/70", "line": 453, "column": 13, "nodeType": null, "endLine": 453, "endColumn": 39}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: ring-sidebar-ring", "line": 453, "column": 40, "nodeType": null, "endLine": 453, "endColumn": 57}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: text-sidebar-foreground", "line": 482, "column": 13, "nodeType": null, "endLine": 482, "endColumn": 36}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: ring-sidebar-ring", "line": 482, "column": 37, "nodeType": null, "endLine": 482, "endColumn": 54}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: hover:bg-sidebar-accent", "line": 485, "column": 13, "nodeType": null, "endLine": 485, "endColumn": 36}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: hover:text-sidebar-accent-foreground", "line": 485, "column": 37, "nodeType": null, "endLine": 485, "endColumn": 73}, {"ruleId": "better-tailwindcss/multiline", "severity": 1, "message": "Unnecessary line wrapping. Expected\n\n`↵\n····flex·w-full·min-w-0·flex-col·gap-1↵\n··`\n\nto be\n\n`flex·w-full·min-w-0·flex-col·gap-1`", "line": 516, "column": 7, "nodeType": null, "endLine": 518, "endColumn": 4, "fix": {"range": [15414, 15458], "text": "`flex w-full min-w-0 flex-col gap-1`"}}, {"ruleId": "better-tailwindcss/multiline", "severity": 1, "message": "Unnecessary line wrapping. Expected\n\n`↵\n····group/menu-item·relative↵\n··`\n\nto be\n\n`group/menu-item·relative`", "line": 531, "column": 7, "nodeType": null, "endLine": 533, "endColumn": 4, "fix": {"range": [15741, 15775], "text": "`group/menu-item relative`"}}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: ring-sidebar-ring", "line": 543, "column": 22, "nodeType": null, "endLine": 543, "endColumn": 39}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: hover:bg-sidebar-accent", "line": 546, "column": 5, "nodeType": null, "endLine": 546, "endColumn": 28}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: hover:text-sidebar-accent-foreground", "line": 546, "column": 29, "nodeType": null, "endLine": 546, "endColumn": 65}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: active:bg-sidebar-accent", "line": 547, "column": 5, "nodeType": null, "endLine": 547, "endColumn": 29}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: active:text-sidebar-accent-foreground", "line": 547, "column": 30, "nodeType": null, "endLine": 547, "endColumn": 67}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[active=true]:bg-sidebar-accent", "line": 548, "column": 5, "nodeType": null, "endLine": 548, "endColumn": 41}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[active=true]:text-sidebar-accent-foreground", "line": 549, "column": 5, "nodeType": null, "endLine": 549, "endColumn": 54}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=open]:hover:bg-sidebar-accent", "line": 551, "column": 5, "nodeType": null, "endLine": 551, "endColumn": 46}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=open]:hover:text-sidebar-accent-foreground", "line": 552, "column": 5, "nodeType": null, "endLine": 552, "endColumn": 59}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: text-sidebar-foreground", "line": 637, "column": 11, "nodeType": null, "endLine": 637, "endColumn": 34}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: ring-sidebar-ring", "line": 637, "column": 35, "nodeType": null, "endLine": 637, "endColumn": 52}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: hover:bg-sidebar-accent", "line": 640, "column": 11, "nodeType": null, "endLine": 640, "endColumn": 34}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: hover:text-sidebar-accent-foreground", "line": 640, "column": 35, "nodeType": null, "endLine": 640, "endColumn": 71}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: peer-hover/menu-button:text-sidebar-accent-foreground", "line": 641, "column": 11, "nodeType": null, "endLine": 641, "endColumn": 64}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: text-sidebar-foreground", "line": 671, "column": 11, "nodeType": null, "endLine": 671, "endColumn": 34}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: peer-hover/menu-button:text-sidebar-accent-foreground", "line": 676, "column": 11, "nodeType": null, "endLine": 676, "endColumn": 64}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: peer-data-[active=true]/menu-button:text-sidebar-accent-foreground", "line": 677, "column": 11, "nodeType": null, "endLine": 677, "endColumn": 77}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: border-sidebar-border", "line": 731, "column": 11, "nodeType": null, "endLine": 731, "endColumn": 32}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: text-sidebar-foreground", "line": 766, "column": 11, "nodeType": null, "endLine": 766, "endColumn": 34}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: ring-sidebar-ring", "line": 766, "column": 35, "nodeType": null, "endLine": 766, "endColumn": 52}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: hover:bg-sidebar-accent", "line": 769, "column": 11, "nodeType": null, "endLine": 769, "endColumn": 34}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: hover:text-sidebar-accent-foreground", "line": 769, "column": 35, "nodeType": null, "endLine": 769, "endColumn": 71}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: active:bg-sidebar-accent", "line": 770, "column": 11, "nodeType": null, "endLine": 770, "endColumn": 35}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: active:text-sidebar-accent-foreground", "line": 770, "column": 36, "nodeType": null, "endLine": 770, "endColumn": 73}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: [&>svg]:text-sidebar-accent-foreground", "line": 771, "column": 11, "nodeType": null, "endLine": 771, "endColumn": 49}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[active=true]:bg-sidebar-accent", "line": 778, "column": 11, "nodeType": null, "endLine": 778, "endColumn": 47}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[active=true]:text-sidebar-accent-foreground", "line": 779, "column": 11, "nodeType": null, "endLine": 779, "endColumn": 60}], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 49, "fixableErrorCount": 0, "fixableWarningCount": 4, "source": "'use client'\n\nimport { Slot } from '@radix-ui/react-slot'\nimport type { VariantProps } from 'class-variance-authority'\nimport { cva } from 'class-variance-authority'\nimport { PanelLeft } from 'lucide-react'\nimport * as React from 'react'\n\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Separator } from '@/components/ui/separator'\nimport { She<PERSON>, SheetContent } from '@/components/ui/sheet'\nimport { Skeleton } from '@/components/ui/skeleton'\nimport { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'\nimport { useIsMobile } from '@/hooks/use-mobile'\nimport { cn } from '@/lib/utils'\n\nconst SIDEBAR_COOKIE_NAME = 'sidebar:state'\nconst SIDEBAR_COOKIE_MAX_AGE = 60 * 60 * 24 * 7\nconst SIDEBAR_WIDTH = '16rem'\nconst SIDEBAR_WIDTH_MOBILE = '18rem'\nconst SIDEBAR_WIDTH_ICON = '3rem'\nconst SIDEBAR_KEYBOARD_SHORTCUT = 'b'\n\ntype SidebarContext = {\n  state: 'expanded' | 'collapsed'\n  open: boolean\n  setOpen: (open: boolean) => void\n  openMobile: boolean\n  setOpenMobile: (open: boolean) => void\n  isMobile: boolean\n  toggleSidebar: () => void\n}\n\nconst SidebarContext = React.createContext<SidebarContext | null>(null)\n\nfunction useSidebar() {\n  const context = React.useContext(SidebarContext)\n  if (!context) {\n    throw new Error('useSidebar must be used within a SidebarProvider.')\n  }\n\n  return context\n}\n\nconst SidebarProvider = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<'div'> & {\n    defaultOpen?: boolean\n    open?: boolean\n    onOpenChange?: (open: boolean) => void\n  }\n>(({ defaultOpen = true, open: openProp, onOpenChange: setOpenProp, className, style, children, ...props }, ref) => {\n  const isMobile = useIsMobile()\n  const [openMobile, setOpenMobile] = React.useState(false)\n\n  // This is the internal state of the sidebar.\n  // We use openProp and setOpenProp for control from outside the component.\n  const [_open, _setOpen] = React.useState(defaultOpen)\n  const open = openProp ?? _open\n  const setOpen = React.useCallback(\n    (value: boolean | ((value: boolean) => boolean)) => {\n      const openState = typeof value === 'function' ? value(open) : value\n      if (setOpenProp) {\n        setOpenProp(openState)\n      } else {\n        _setOpen(openState)\n      }\n\n      // This sets the cookie to keep the sidebar state.\n      document.cookie = `${SIDEBAR_COOKIE_NAME}=${openState}; path=/; max-age=${SIDEBAR_COOKIE_MAX_AGE}`\n    },\n    [setOpenProp, open]\n  )\n\n  // Helper to toggle the sidebar.\n  const toggleSidebar = React.useCallback(() => {\n    return isMobile ? setOpenMobile(open => !open) : setOpen(open => !open)\n  }, [isMobile, setOpen, setOpenMobile])\n\n  // Adds a keyboard shortcut to toggle the sidebar.\n  React.useEffect(() => {\n    const handleKeyDown = (event: KeyboardEvent) => {\n      if (event.key === SIDEBAR_KEYBOARD_SHORTCUT && (event.metaKey || event.ctrlKey)) {\n        event.preventDefault()\n        toggleSidebar()\n      }\n    }\n\n    window.addEventListener('keydown', handleKeyDown)\n    return () => window.removeEventListener('keydown', handleKeyDown)\n  }, [toggleSidebar])\n\n  // We add a state so that we can do data-state=\"expanded\" or \"collapsed\".\n  // This makes it easier to style the sidebar with Tailwind classes.\n  const state = open ? 'expanded' : 'collapsed'\n\n  const contextValue = React.useMemo<SidebarContext>(\n    () => ({\n      state,\n      open,\n      setOpen,\n      isMobile,\n      openMobile,\n      setOpenMobile,\n      toggleSidebar,\n    }),\n    [state, open, setOpen, isMobile, openMobile, setOpenMobile, toggleSidebar]\n  )\n\n  return (\n    <SidebarContext.Provider value={contextValue}>\n      <TooltipProvider delayDuration={0}>\n        <div\n          style={\n            {\n              '--sidebar-width': SIDEBAR_WIDTH,\n              '--sidebar-width-icon': SIDEBAR_WIDTH_ICON,\n              ...style,\n            } as React.CSSProperties\n          }\n          className={cn(\n            `\n              group/sidebar-wrapper flex min-h-svh w-full\n              has-data-[variant=inset]:bg-sidebar\n            `,\n            className\n          )}\n          ref={ref}\n          {...props}\n        >\n          {children}\n        </div>\n      </TooltipProvider>\n    </SidebarContext.Provider>\n  )\n})\nSidebarProvider.displayName = 'SidebarProvider'\n\nconst Sidebar = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<'div'> & {\n    side?: 'left' | 'right'\n    variant?: 'sidebar' | 'floating' | 'inset'\n    collapsible?: 'offcanvas' | 'icon' | 'none'\n  }\n>(({ side = 'left', variant = 'sidebar', collapsible = 'offcanvas', className, children, ...props }, ref) => {\n  const { isMobile, state, openMobile, setOpenMobile } = useSidebar()\n\n  if (collapsible === 'none') {\n    return (\n      <div\n        className={cn(\n          `\n            bg-sidebar text-sidebar-foreground flex h-full w-(--sidebar-width)\n            flex-col\n          `,\n          className\n        )}\n        ref={ref}\n        {...props}\n      >\n        {children}\n      </div>\n    )\n  }\n\n  if (isMobile) {\n    return (\n      <Sheet open={openMobile} onOpenChange={setOpenMobile} {...props}>\n        <SheetContent\n          data-sidebar='sidebar'\n          data-mobile='true'\n          className={`\n            bg-sidebar text-sidebar-foreground w-(--sidebar-width) p-0\n            [&>button]:hidden\n          `}\n          style={\n            {\n              '--sidebar-width': SIDEBAR_WIDTH_MOBILE,\n            } as React.CSSProperties\n          }\n          side={side}\n        >\n          <div className='flex h-full w-full flex-col'>{children}</div>\n        </SheetContent>\n      </Sheet>\n    )\n  }\n\n  return (\n    <div\n      ref={ref}\n      className={`\n        group peer text-sidebar-foreground hidden\n        md:block\n      `}\n      data-state={state}\n      data-collapsible={state === 'collapsed' ? collapsible : ''}\n      data-variant={variant}\n      data-side={side}\n    >\n      {/* This is what handles the sidebar gap on desktop */}\n      <div\n        className={cn(\n          `\n            relative h-svh w-(--sidebar-width) bg-transparent transition-[width]\n            duration-200 ease-linear\n          `,\n          'group-data-[collapsible=offcanvas]:w-0',\n          'group-data-[side=right]:rotate-180',\n          variant === 'floating' || variant === 'inset'\n            ? 'group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4)))]'\n            : 'group-data-[collapsible=icon]:w-(--sidebar-width-icon)'\n        )}\n      />\n      <div\n        className={cn(\n          `\n            fixed inset-y-0 z-10 hidden h-svh w-(--sidebar-width)\n            transition-[left,right,width] duration-200 ease-linear\n            md:flex\n          `,\n          side === 'left'\n            ? 'left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]'\n            : 'right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]',\n          // Adjust the padding for floating and inset variants.\n          variant === 'floating' || variant === 'inset'\n            ? 'p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4))+2px)]'\n            : 'group-data-[collapsible=icon]:w-(--sidebar-width-icon) group-data-[side=left]:border-r group-data-[side=right]:border-l',\n          className\n        )}\n        {...props}\n      >\n        <div\n          data-sidebar='sidebar'\n          className={`\n            bg-sidebar flex h-full w-full flex-col\n            group-data-[variant=floating]:border-sidebar-border\n            group-data-[variant=floating]:rounded-lg\n            group-data-[variant=floating]:border\n            group-data-[variant=floating]:shadow-sm\n          `}\n        >\n          {children}\n        </div>\n      </div>\n    </div>\n  )\n})\nSidebar.displayName = 'Sidebar'\n\nconst SidebarTrigger = React.forwardRef<React.ElementRef<typeof Button>, React.ComponentProps<typeof Button>>(\n  ({ className, onClick, ...props }, ref) => {\n    const { toggleSidebar } = useSidebar()\n\n    return (\n      <Button\n        ref={ref}\n        data-sidebar='trigger'\n        variant='ghost'\n        size='icon'\n        className={cn('h-7 w-7', className)}\n        onClick={event => {\n          onClick?.(event)\n          toggleSidebar()\n        }}\n        {...props}\n      >\n        <PanelLeft />\n        <span className='sr-only'>Toggle Sidebar</span>\n      </Button>\n    )\n  }\n)\nSidebarTrigger.displayName = 'SidebarTrigger'\n\nconst SidebarRail = React.forwardRef<HTMLButtonElement, React.ComponentProps<'button'>>(\n  ({ className, ...props }, ref) => {\n    const { toggleSidebar } = useSidebar()\n\n    return (\n      <button\n        ref={ref}\n        data-sidebar='rail'\n        aria-label='Toggle Sidebar'\n        tabIndex={-1}\n        onClick={toggleSidebar}\n        title='Toggle Sidebar'\n        className={cn(\n          `\n            hover:after:bg-sidebar-border\n            absolute inset-y-0 z-20 hidden w-4 -translate-x-1/2 transition-all\n            ease-linear\n            group-data-[side=left]:-right-4\n            group-data-[side=right]:left-0\n            after:absolute after:inset-y-0 after:left-1/2 after:w-[2px]\n            sm:flex\n          `,\n          `\n            in-data-[side=left]:cursor-w-resize\n            in-data-[side=right]:cursor-e-resize\n          `,\n          `\n            [[data-side=left][data-state=collapsed]_&]:cursor-e-resize\n            [[data-side=right][data-state=collapsed]_&]:cursor-w-resize\n          `,\n          `\n            hover:group-data-[collapsible=offcanvas]:bg-sidebar\n            group-data-[collapsible=offcanvas]:translate-x-0\n            group-data-[collapsible=offcanvas]:after:left-full\n          `,\n          '[[data-side=left][data-collapsible=offcanvas]_&]:-right-2',\n          '[[data-side=right][data-collapsible=offcanvas]_&]:-left-2',\n          className\n        )}\n        {...props}\n      />\n    )\n  }\n)\nSidebarRail.displayName = 'SidebarRail'\n\nconst SidebarInset = React.forwardRef<HTMLDivElement, React.ComponentProps<'main'>>(({ className, ...props }, ref) => {\n  return (\n    <main\n      ref={ref}\n      className={cn(\n        'relative flex min-h-svh flex-1 flex-col bg-background',\n        `\n          peer-data-[variant=inset]:min-h-[calc(100svh-(--spacing(4)))]\n          md:peer-data-[variant=inset]:m-2 md:peer-data-[variant=inset]:ml-0\n          md:peer-data-[variant=inset]:rounded-xl\n          md:peer-data-[variant=inset]:shadow-sm\n          md:peer-data-[variant=inset]:peer-data-[state=collapsed]:ml-2\n        `,\n        className\n      )}\n      {...props}\n    />\n  )\n})\nSidebarInset.displayName = 'SidebarInset'\n\nconst SidebarInput = React.forwardRef<React.ElementRef<typeof Input>, React.ComponentProps<typeof Input>>(\n  ({ className, ...props }, ref) => {\n    return (\n      <Input\n        ref={ref}\n        data-sidebar='input'\n        className={cn(\n          `\n            focus-visible:ring-sidebar-ring focus-visible:ring-2\n            h-8 w-full bg-background shadow-none\n          `,\n          className\n        )}\n        {...props}\n      />\n    )\n  }\n)\nSidebarInput.displayName = 'SidebarInput'\n\nconst SidebarHeader = React.forwardRef<HTMLDivElement, React.ComponentProps<'div'>>(({ className, ...props }, ref) => {\n  return (\n    <div\n      ref={ref}\n      data-sidebar='header'\n      className={cn(\n        `\n    flex flex-col gap-2 p-2\n  `,\n        className\n      )}\n      {...props}\n    />\n  )\n})\nSidebarHeader.displayName = 'SidebarHeader'\n\nconst SidebarFooter = React.forwardRef<HTMLDivElement, React.ComponentProps<'div'>>(({ className, ...props }, ref) => {\n  return (\n    <div\n      ref={ref}\n      data-sidebar='footer'\n      className={cn(\n        `\n    flex flex-col gap-2 p-2\n  `,\n        className\n      )}\n      {...props}\n    />\n  )\n})\nSidebarFooter.displayName = 'SidebarFooter'\n\nconst SidebarSeparator = React.forwardRef<React.ElementRef<typeof Separator>, React.ComponentProps<typeof Separator>>(\n  ({ className, ...props }, ref) => {\n    return (\n      <Separator\n        ref={ref}\n        data-sidebar='separator'\n        className={cn('bg-sidebar-border mx-2 w-auto', className)}\n        {...props}\n      />\n    )\n  }\n)\nSidebarSeparator.displayName = 'SidebarSeparator'\n\nconst SidebarContent = React.forwardRef<HTMLDivElement, React.ComponentProps<'div'>>(({ className, ...props }, ref) => {\n  return (\n    <div\n      ref={ref}\n      data-sidebar='content'\n      className={cn(\n        `\n          flex min-h-0 flex-1 flex-col gap-2 overflow-auto\n          group-data-[collapsible=icon]:overflow-hidden\n        `,\n        className\n      )}\n      {...props}\n    />\n  )\n})\nSidebarContent.displayName = 'SidebarContent'\n\nconst SidebarGroup = React.forwardRef<HTMLDivElement, React.ComponentProps<'div'>>(({ className, ...props }, ref) => {\n  return (\n    <div\n      ref={ref}\n      data-sidebar='group'\n      className={cn('relative flex w-full min-w-0 flex-col p-2', className)}\n      {...props}\n    />\n  )\n})\nSidebarGroup.displayName = 'SidebarGroup'\n\nconst SidebarGroupLabel = React.forwardRef<HTMLDivElement, React.ComponentProps<'div'> & { asChild?: boolean }>(\n  ({ className, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : 'div'\n\n    return (\n      <Comp\n        ref={ref}\n        data-sidebar='group-label'\n        className={cn(\n          `\n            text-sidebar-foreground/70 ring-sidebar-ring flex h-8 shrink-0\n            items-center rounded-md px-2 text-xs font-medium outline-hidden\n            transition-[margin,opa] duration-200 ease-linear\n            focus-visible:ring-2\n            [&>svg]:size-4 [&>svg]:shrink-0\n          `,\n          `\n            group-data-[collapsible=icon]:-mt-8\n            group-data-[collapsible=icon]:opacity-0\n          `,\n          className\n        )}\n        {...props}\n      />\n    )\n  }\n)\nSidebarGroupLabel.displayName = 'SidebarGroupLabel'\n\nconst SidebarGroupAction = React.forwardRef<HTMLButtonElement, React.ComponentProps<'button'> & { asChild?: boolean }>(\n  ({ className, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : 'button'\n\n    return (\n      <Comp\n        ref={ref}\n        data-sidebar='group-action'\n        className={cn(\n          `\n            text-sidebar-foreground ring-sidebar-ring absolute top-3.5 right-3\n            flex aspect-square w-5 items-center justify-center rounded-md p-0\n            outline-hidden transition-transform\n            hover:bg-sidebar-accent hover:text-sidebar-accent-foreground\n            focus-visible:ring-2\n            [&>svg]:size-4 [&>svg]:shrink-0\n          `,\n          // Increases the hit area of the button on mobile.\n          `\n            after:absolute after:-inset-2\n            md:after:hidden\n          `,\n          'group-data-[collapsible=icon]:hidden',\n          className\n        )}\n        {...props}\n      />\n    )\n  }\n)\nSidebarGroupAction.displayName = 'SidebarGroupAction'\n\nconst SidebarGroupContent = React.forwardRef<HTMLDivElement, React.ComponentProps<'div'>>(\n  ({ className, ...props }, ref) => (\n    <div ref={ref} data-sidebar='group-content' className={cn('w-full text-sm', className)} {...props} />\n  )\n)\nSidebarGroupContent.displayName = 'SidebarGroupContent'\n\nconst SidebarMenu = React.forwardRef<HTMLUListElement, React.ComponentProps<'ul'>>(({ className, ...props }, ref) => (\n  <ul\n    ref={ref}\n    data-sidebar='menu'\n    className={cn(\n      `\n    flex w-full min-w-0 flex-col gap-1\n  `,\n      className\n    )}\n    {...props}\n  />\n))\nSidebarMenu.displayName = 'SidebarMenu'\n\nconst SidebarMenuItem = React.forwardRef<HTMLLIElement, React.ComponentProps<'li'>>(({ className, ...props }, ref) => (\n  <li\n    ref={ref}\n    data-sidebar='menu-item'\n    className={cn(\n      `\n    group/menu-item relative\n  `,\n      className\n    )}\n    {...props}\n  />\n))\nSidebarMenuItem.displayName = 'SidebarMenuItem'\n\nconst sidebarMenuButtonVariants = cva(\n  `\n    peer/menu-button ring-sidebar-ring flex w-full items-center gap-2\n    overflow-hidden rounded-md p-2 text-left text-sm outline-hidden\n    transition-[width,height,padding]\n    hover:bg-sidebar-accent hover:text-sidebar-accent-foreground\n    active:bg-sidebar-accent active:text-sidebar-accent-foreground\n    data-[active=true]:bg-sidebar-accent\n    data-[active=true]:text-sidebar-accent-foreground\n    data-[active=true]:font-medium\n    data-[state=open]:hover:bg-sidebar-accent\n    data-[state=open]:hover:text-sidebar-accent-foreground\n    group-has-data-[sidebar=menu-action]/menu-item:pr-8\n    group-data-[collapsible=icon]:size-8! group-data-[collapsible=icon]:p-2!\n    focus-visible:ring-2\n    disabled:pointer-events-none disabled:opacity-50\n    aria-disabled:pointer-events-none aria-disabled:opacity-50\n    [&>span:last-child]:truncate\n    [&>svg]:size-4 [&>svg]:shrink-0\n  `,\n  {\n    variants: {\n      variant: {\n        default: 'hover:bg-sidebar-accent hover:text-sidebar-accent-foreground',\n        outline:\n          'hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))] bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))]',\n      },\n      size: {\n        default: 'h-8 text-sm',\n        sm: 'h-7 text-xs',\n        lg: 'h-12 text-sm group-data-[collapsible=icon]:p-0!',\n      },\n    },\n    defaultVariants: {\n      variant: 'default',\n      size: 'default',\n    },\n  }\n)\n\nconst SidebarMenuButton = React.forwardRef<\n  HTMLButtonElement,\n  React.ComponentProps<'button'> & {\n    asChild?: boolean\n    isActive?: boolean\n    tooltip?: string | React.ComponentProps<typeof TooltipContent>\n  } & VariantProps<typeof sidebarMenuButtonVariants>\n>(({ asChild = false, isActive = false, variant = 'default', size = 'default', tooltip, className, ...props }, ref) => {\n  const Comp = asChild ? Slot : 'button'\n  const { isMobile, state } = useSidebar()\n\n  const button = (\n    <Comp\n      ref={ref}\n      data-sidebar='menu-button'\n      data-size={size}\n      data-active={isActive}\n      className={cn(sidebarMenuButtonVariants({ variant, size }), className)}\n      {...props}\n    />\n  )\n\n  if (!tooltip) {\n    return button\n  }\n\n  if (typeof tooltip === 'string') {\n    tooltip = {\n      children: tooltip,\n    }\n  }\n\n  return (\n    <Tooltip>\n      <TooltipTrigger asChild>{button}</TooltipTrigger>\n      <TooltipContent side='right' align='center' hidden={state !== 'collapsed' || isMobile} {...tooltip} />\n    </Tooltip>\n  )\n})\nSidebarMenuButton.displayName = 'SidebarMenuButton'\n\nconst SidebarMenuAction = React.forwardRef<\n  HTMLButtonElement,\n  React.ComponentProps<'button'> & {\n    asChild?: boolean\n    showOnHover?: boolean\n  }\n>(({ className, asChild = false, showOnHover = false, ...props }, ref) => {\n  const Comp = asChild ? Slot : 'button'\n\n  return (\n    <Comp\n      ref={ref}\n      data-sidebar='menu-action'\n      className={cn(\n        `\n          text-sidebar-foreground ring-sidebar-ring absolute top-1.5 right-1\n          flex aspect-square w-5 items-center justify-center rounded-md p-0\n          outline-hidden transition-transform\n          hover:bg-sidebar-accent hover:text-sidebar-accent-foreground\n          peer-hover/menu-button:text-sidebar-accent-foreground\n          focus-visible:ring-2\n          [&>svg]:size-4 [&>svg]:shrink-0\n        `,\n        // Increases the hit area of the button on mobile.\n        `\n          after:absolute after:-inset-2\n          md:after:hidden\n        `,\n        'peer-data-[size=sm]/menu-button:top-1',\n        'peer-data-[size=default]/menu-button:top-1.5',\n        'peer-data-[size=lg]/menu-button:top-2.5',\n        'group-data-[collapsible=icon]:hidden',\n        showOnHover &&\n          'peer-data-[active=true]/menu-button:text-sidebar-accent-foreground group-focus-within/menu-item:opacity-100 group-hover/menu-item:opacity-100 data-[state=open]:opacity-100 md:opacity-0',\n        className\n      )}\n      {...props}\n    />\n  )\n})\nSidebarMenuAction.displayName = 'SidebarMenuAction'\n\nconst SidebarMenuBadge = React.forwardRef<HTMLDivElement, React.ComponentProps<'div'>>(\n  ({ className, ...props }, ref) => (\n    <div\n      ref={ref}\n      data-sidebar='menu-badge'\n      className={cn(\n        `\n          text-sidebar-foreground pointer-events-none absolute right-1 flex h-5\n          min-w-5 items-center justify-center rounded-md px-1 text-xs\n          font-medium tabular-nums select-none\n        `,\n        `\n          peer-hover/menu-button:text-sidebar-accent-foreground\n          peer-data-[active=true]/menu-button:text-sidebar-accent-foreground\n        `,\n        'peer-data-[size=sm]/menu-button:top-1',\n        'peer-data-[size=default]/menu-button:top-1.5',\n        'peer-data-[size=lg]/menu-button:top-2.5',\n        'group-data-[collapsible=icon]:hidden',\n        className\n      )}\n      {...props}\n    />\n  )\n)\nSidebarMenuBadge.displayName = 'SidebarMenuBadge'\n\nconst SidebarMenuSkeleton = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<'div'> & {\n    showIcon?: boolean\n  }\n>(({ className, showIcon = false, ...props }, ref) => {\n  // Random width between 50 to 90%.\n  const width = React.useMemo(() => {\n    return `${Math.floor(Math.random() * 40) + 50}%`\n  }, [])\n\n  return (\n    <div\n      ref={ref}\n      data-sidebar='menu-skeleton'\n      className={cn('flex h-8 items-center gap-2 rounded-md px-2', className)}\n      {...props}\n    >\n      {showIcon && <Skeleton className='size-4 rounded-md' data-sidebar='menu-skeleton-icon' />}\n      <Skeleton\n        className='h-4 max-w-(--skeleton-width) flex-1'\n        data-sidebar='menu-skeleton-text'\n        style={\n          {\n            '--skeleton-width': width,\n          } as React.CSSProperties\n        }\n      />\n    </div>\n  )\n})\nSidebarMenuSkeleton.displayName = 'SidebarMenuSkeleton'\n\nconst SidebarMenuSub = React.forwardRef<HTMLUListElement, React.ComponentProps<'ul'>>(\n  ({ className, ...props }, ref) => (\n    <ul\n      ref={ref}\n      data-sidebar='menu-sub'\n      className={cn(\n        `\n          border-sidebar-border mx-3.5 flex min-w-0 translate-x-px flex-col\n          gap-1 border-l px-2.5 py-0.5\n        `,\n        'group-data-[collapsible=icon]:hidden',\n        className\n      )}\n      {...props}\n    />\n  )\n)\nSidebarMenuSub.displayName = 'SidebarMenuSub'\n\nconst SidebarMenuSubItem = React.forwardRef<HTMLLIElement, React.ComponentProps<'li'>>(({ ...props }, ref) => (\n  <li ref={ref} {...props} />\n))\nSidebarMenuSubItem.displayName = 'SidebarMenuSubItem'\n\nconst SidebarMenuSubButton = React.forwardRef<\n  HTMLAnchorElement,\n  React.ComponentProps<'a'> & {\n    asChild?: boolean\n    size?: 'sm' | 'md'\n    isActive?: boolean\n  }\n>(({ asChild = false, size = 'md', isActive, className, ...props }, ref) => {\n  const Comp = asChild ? Slot : 'a'\n\n  return (\n    <Comp\n      ref={ref}\n      data-sidebar='menu-sub-button'\n      data-size={size}\n      data-active={isActive}\n      className={cn(\n        `\n          text-sidebar-foreground ring-sidebar-ring flex h-7 min-w-0\n          -translate-x-px items-center gap-2 overflow-hidden rounded-md px-2\n          outline-hidden\n          hover:bg-sidebar-accent hover:text-sidebar-accent-foreground\n          active:bg-sidebar-accent active:text-sidebar-accent-foreground\n          [&>svg]:text-sidebar-accent-foreground [&>svg]:size-4 [&>svg]:shrink-0\n          focus-visible:ring-2\n          disabled:pointer-events-none disabled:opacity-50\n          aria-disabled:pointer-events-none aria-disabled:opacity-50\n          [&>span:last-child]:truncate\n        `,\n        `\n          data-[active=true]:bg-sidebar-accent\n          data-[active=true]:text-sidebar-accent-foreground\n        `,\n        size === 'sm' && 'text-xs',\n        size === 'md' && 'text-sm',\n        'group-data-[collapsible=icon]:hidden',\n        className\n      )}\n      {...props}\n    />\n  )\n})\nSidebarMenuSubButton.displayName = 'SidebarMenuSubButton'\n\nexport {\n  Sidebar,\n  SidebarContent,\n  SidebarFooter,\n  SidebarGroup,\n  SidebarGroupAction,\n  SidebarGroupContent,\n  SidebarGroupLabel,\n  SidebarHeader,\n  SidebarInput,\n  SidebarInset,\n  SidebarMenu,\n  SidebarMenuAction,\n  SidebarMenuBadge,\n  SidebarMenuButton,\n  SidebarMenuItem,\n  SidebarMenuSkeleton,\n  SidebarMenuSub,\n  SidebarMenuSubButton,\n  SidebarMenuSubItem,\n  SidebarProvider,\n  SidebarRail,\n  SidebarSeparator,\n  SidebarTrigger,\n  useSidebar,\n}\n", "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/skeleton.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/slider.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/sonner.tsx", "messages": [{"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: toaster", "line": 14, "column": 18, "nodeType": null, "endLine": 14, "endColumn": 25}], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "'use client'\n\nimport { useTheme } from 'next-themes'\nimport { Toaster as Sonner } from 'sonner'\n\ntype ToasterProps = React.ComponentProps<typeof Sonner>\n\nconst Toaster = ({ ...props }: ToasterProps) => {\n  const { theme = 'system' } = useTheme()\n\n  return (\n    <Sonner\n      theme={theme as ToasterProps['theme']}\n      className='toaster group'\n      toastOptions={{\n        classNames: {\n          toast:\n            'group toast group-[.toaster]:bg-background group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-lg',\n          description: 'group-[.toast]:text-muted-foreground',\n          actionButton: 'group-[.toast]:bg-primary group-[.toast]:text-primary-foreground',\n          cancelButton: 'group-[.toast]:bg-muted group-[.toast]:text-muted-foreground',\n        },\n      }}\n      {...props}\n    />\n  )\n}\n\nexport { Toaster }\n", "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/switch.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/table.tsx", "messages": [{"ruleId": "better-tailwindcss/multiline", "severity": 1, "message": "Unnecessary line wrapping. Expected\n\n`↵\n····[&_tr]:border-b↵\n··`\n\nto be\n\n`[&_tr]:border-b`", "line": 19, "column": 9, "nodeType": null, "endLine": 21, "endColumn": 4, "fix": {"range": [592, 617], "text": "`[&_tr]:border-b`"}}], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": "import * as React from 'react'\n\nimport { cn } from '@/lib/utils'\n\nconst Table = React.forwardRef<HTMLTableElement, React.HTMLAttributes<HTMLTableElement>>(\n  ({ className, ...props }, ref) => (\n    <div className='relative w-full overflow-auto'>\n      <table ref={ref} className={cn('w-full caption-bottom text-sm', className)} {...props} />\n    </div>\n  )\n)\nTable.displayName = 'Table'\n\nconst TableHeader = React.forwardRef<HTMLTableSectionElement, React.HTMLAttributes<HTMLTableSectionElement>>(\n  ({ className, ...props }, ref) => (\n    <thead\n      ref={ref}\n      className={cn(\n        `\n    [&_tr]:border-b\n  `,\n        className\n      )}\n      {...props}\n    />\n  )\n)\nTableHeader.displayName = 'TableHeader'\n\nconst TableBody = React.forwardRef<HTMLTableSectionElement, React.HTMLAttributes<HTMLTableSectionElement>>(\n  ({ className, ...props }, ref) => (\n    <tbody ref={ref} className={cn('[&_tr:last-child]:border-0', className)} {...props} />\n  )\n)\nTableBody.displayName = 'TableBody'\n\nconst TableFooter = React.forwardRef<HTMLTableSectionElement, React.HTMLAttributes<HTMLTableSectionElement>>(\n  ({ className, ...props }, ref) => (\n    <tfoot\n      ref={ref}\n      className={cn(\n        `\n          border-t bg-muted/50 font-medium\n          last:[&>tr]:border-b-0\n        `,\n        className\n      )}\n      {...props}\n    />\n  )\n)\nTableFooter.displayName = 'TableFooter'\n\nconst TableRow = React.forwardRef<HTMLTableRowElement, React.HTMLAttributes<HTMLTableRowElement>>(\n  ({ className, ...props }, ref) => (\n    <tr\n      ref={ref}\n      className={cn(\n        `\n          border-b transition-colors\n          hover:bg-muted/50\n          data-[state=selected]:bg-muted\n        `,\n        className\n      )}\n      {...props}\n    />\n  )\n)\nTableRow.displayName = 'TableRow'\n\nconst TableHead = React.forwardRef<HTMLTableCellElement, React.ThHTMLAttributes<HTMLTableCellElement>>(\n  ({ className, ...props }, ref) => (\n    <th\n      ref={ref}\n      className={cn(\n        `\n          h-12 px-4 text-left align-middle font-medium text-muted-foreground\n          [&:has([role=checkbox])]:pr-0\n        `,\n        className\n      )}\n      {...props}\n    />\n  )\n)\nTableHead.displayName = 'TableHead'\n\nconst TableCell = React.forwardRef<HTMLTableCellElement, React.TdHTMLAttributes<HTMLTableCellElement>>(\n  ({ className, ...props }, ref) => (\n    <td\n      ref={ref}\n      className={cn(\n        `\n          p-4 align-middle\n          [&:has([role=checkbox])]:pr-0\n        `,\n        className\n      )}\n      {...props}\n    />\n  )\n)\nTableCell.displayName = 'TableCell'\n\nconst TableCaption = React.forwardRef<HTMLTableCaptionElement, React.HTMLAttributes<HTMLTableCaptionElement>>(\n  ({ className, ...props }, ref) => (\n    <caption ref={ref} className={cn('mt-4 text-sm text-muted-foreground', className)} {...props} />\n  )\n)\nTableCaption.displayName = 'TableCaption'\n\nexport { Table, TableHeader, TableBody, TableFooter, TableHead, TableRow, TableCell, TableCaption }\n", "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/tabs.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/textarea.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/toast.tsx", "messages": [{"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=open]:animate-in", "line": 36, "column": 5, "nodeType": null, "endLine": 36, "endColumn": 33}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=open]:slide-in-from-top-full", "line": 36, "column": 34, "nodeType": null, "endLine": 36, "endColumn": 74}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=open]:sm:slide-in-from-bottom-full", "line": 37, "column": 5, "nodeType": null, "endLine": 37, "endColumn": 51}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=closed]:animate-out", "line": 38, "column": 5, "nodeType": null, "endLine": 38, "endColumn": 36}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=closed]:fade-out-80", "line": 38, "column": 37, "nodeType": null, "endLine": 38, "endColumn": 68}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=closed]:slide-out-to-right-full", "line": 39, "column": 5, "nodeType": null, "endLine": 39, "endColumn": 48}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[swipe=end]:animate-out", "line": 40, "column": 5, "nodeType": null, "endLine": 40, "endColumn": 33}], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "'use client'\n\nimport * as ToastPrimitives from '@radix-ui/react-toast'\nimport { cva, type VariantProps } from 'class-variance-authority'\nimport { X } from 'lucide-react'\nimport * as React from 'react'\n\nimport { cn } from '@/lib/utils'\n\nconst ToastProvider = ToastPrimitives.Provider\n\nconst ToastViewport = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitives.Viewport>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Viewport>\n>(({ className, ...props }, ref) => (\n  <ToastPrimitives.Viewport\n    ref={ref}\n    className={cn(\n      `\n        fixed top-0 z-100 flex max-h-screen w-full flex-col-reverse p-4\n        sm:top-auto sm:right-0 sm:bottom-0 sm:flex-col\n        md:max-w-[420px]\n      `,\n      className\n    )}\n    {...props}\n  />\n))\nToastViewport.displayName = ToastPrimitives.Viewport.displayName\n\nconst toastVariants = cva(\n  `\n    group pointer-events-auto relative flex w-full items-center justify-between\n    space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg\n    transition-all\n    data-[state=open]:animate-in data-[state=open]:slide-in-from-top-full\n    data-[state=open]:sm:slide-in-from-bottom-full\n    data-[state=closed]:animate-out data-[state=closed]:fade-out-80\n    data-[state=closed]:slide-out-to-right-full\n    data-[swipe=end]:animate-out\n    data-[swipe=end]:translate-x-(--radix-toast-swipe-end-x)\n    data-[swipe=cancel]:translate-x-0\n    data-[swipe=move]:translate-x-(--radix-toast-swipe-move-x)\n    data-[swipe=move]:transition-none\n  `,\n  {\n    variants: {\n      variant: {\n        default: 'border bg-background text-foreground',\n        destructive: 'destructive group border-destructive bg-destructive text-destructive-foreground',\n      },\n    },\n    defaultVariants: {\n      variant: 'default',\n    },\n  }\n)\n\nconst Toast = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitives.Root>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Root> & VariantProps<typeof toastVariants>\n>(({ className, variant, ...props }, ref) => {\n  return <ToastPrimitives.Root ref={ref} className={cn(toastVariants({ variant }), className)} {...props} />\n})\nToast.displayName = ToastPrimitives.Root.displayName\n\nconst ToastAction = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitives.Action>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Action>\n>(({ className, ...props }, ref) => (\n  <ToastPrimitives.Action\n    ref={ref}\n    className={cn(\n      `\n        inline-flex h-8 shrink-0 items-center justify-center rounded-md border\n        bg-transparent px-3 text-sm font-medium ring-offset-background\n        transition-colors\n        group-[.destructive]:border-muted/40\n        hover:bg-secondary hover:group-[.destructive]:border-destructive/30\n        hover:group-[.destructive]:bg-destructive\n        hover:group-[.destructive]:text-destructive-foreground\n        focus:ring-2 focus:ring-ring focus:ring-offset-2 focus:outline-hidden\n        focus:group-[.destructive]:ring-destructive\n        disabled:pointer-events-none disabled:opacity-50\n      `,\n      className\n    )}\n    {...props}\n  />\n))\nToastAction.displayName = ToastPrimitives.Action.displayName\n\nconst ToastClose = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitives.Close>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Close>\n>(({ className, ...props }, ref) => (\n  <ToastPrimitives.Close\n    ref={ref}\n    className={cn(\n      `\n        absolute top-2 right-2 rounded-md p-1 text-foreground/50 opacity-0\n        transition-opacity\n        group-hover:opacity-100\n        group-[.destructive]:text-red-300\n        hover:text-foreground hover:group-[.destructive]:text-red-50\n        focus:opacity-100 focus:ring-2 focus:outline-hidden\n        focus:group-[.destructive]:ring-red-400\n        focus:group-[.destructive]:ring-offset-red-600\n      `,\n      className\n    )}\n    toast-close=''\n    {...props}\n  >\n    <X className='h-4 w-4' />\n  </ToastPrimitives.Close>\n))\nToastClose.displayName = ToastPrimitives.Close.displayName\n\nconst ToastTitle = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitives.Title>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Title>\n>(({ className, ...props }, ref) => (\n  <ToastPrimitives.Title ref={ref} className={cn('text-sm font-semibold', className)} {...props} />\n))\nToastTitle.displayName = ToastPrimitives.Title.displayName\n\nconst ToastDescription = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitives.Description>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Description>\n>(({ className, ...props }, ref) => (\n  <ToastPrimitives.Description ref={ref} className={cn('text-sm opacity-90', className)} {...props} />\n))\nToastDescription.displayName = ToastPrimitives.Description.displayName\n\ntype ToastProps = React.ComponentPropsWithoutRef<typeof Toast>\n\ntype ToastActionElement = React.ReactElement<typeof ToastAction>\n\nexport {\n  type ToastProps,\n  type ToastActionElement,\n  ToastProvider,\n  ToastViewport,\n  Toast,\n  ToastTitle,\n  ToastDescription,\n  ToastClose,\n  ToastAction,\n}\n", "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/toaster.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/toggle-group.tsx", "messages": [{"ruleId": "better-tailwindcss/multiline", "severity": 1, "message": "Unnecessary line wrapping. Expected\n\n`↵\n····flex·items-center·justify-center·gap-1↵\n··`\n\nto be\n\n`flex·items-center·justify-center·gap-1`", "line": 22, "column": 7, "nodeType": null, "endLine": 24, "endColumn": 4, "fix": {"range": [725, 773], "text": "`flex items-center justify-center gap-1`"}}], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": "'use client'\n\nimport * as ToggleGroupPrimitive from '@radix-ui/react-toggle-group'\nimport { type VariantProps } from 'class-variance-authority'\nimport * as React from 'react'\n\nimport { toggleVariants } from '@/components/ui/toggle'\nimport { cn } from '@/lib/utils'\n\nconst ToggleGroupContext = React.createContext<VariantProps<typeof toggleVariants>>({\n  size: 'default',\n  variant: 'default',\n})\n\nconst ToggleGroup = React.forwardRef<\n  React.ElementRef<typeof ToggleGroupPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof ToggleGroupPrimitive.Root> & VariantProps<typeof toggleVariants>\n>(({ className, variant, size, children, ...props }, ref) => (\n  <ToggleGroupPrimitive.Root\n    ref={ref}\n    className={cn(\n      `\n    flex items-center justify-center gap-1\n  `,\n      className\n    )}\n    {...props}\n  >\n    <ToggleGroupContext.Provider value={{ variant, size }}>{children}</ToggleGroupContext.Provider>\n  </ToggleGroupPrimitive.Root>\n))\n\nToggleGroup.displayName = ToggleGroupPrimitive.Root.displayName\n\nconst ToggleGroupItem = React.forwardRef<\n  React.ElementRef<typeof ToggleGroupPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof ToggleGroupPrimitive.Item> & VariantProps<typeof toggleVariants>\n>(({ className, children, variant, size, ...props }, ref) => {\n  const context = React.useContext(ToggleGroupContext)\n\n  return (\n    <ToggleGroupPrimitive.Item\n      ref={ref}\n      className={cn(\n        toggleVariants({\n          variant: context.variant || variant,\n          size: context.size || size,\n        }),\n        className\n      )}\n      {...props}\n    >\n      {children}\n    </ToggleGroupPrimitive.Item>\n  )\n})\n\nToggleGroupItem.displayName = ToggleGroupPrimitive.Item.displayName\n\nexport { ToggleGroup, ToggleGroupItem }\n", "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/toggle.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/tooltip.tsx", "messages": [{"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: animate-in", "line": 23, "column": 9, "nodeType": null, "endLine": 23, "endColumn": 19}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: fade-in-0", "line": 23, "column": 20, "nodeType": null, "endLine": 23, "endColumn": 29}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: zoom-in-95", "line": 23, "column": 30, "nodeType": null, "endLine": 23, "endColumn": 40}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=closed]:animate-out", "line": 25, "column": 9, "nodeType": null, "endLine": 25, "endColumn": 40}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=closed]:fade-out-0", "line": 25, "column": 41, "nodeType": null, "endLine": 25, "endColumn": 71}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[state=closed]:zoom-out-95", "line": 26, "column": 9, "nodeType": null, "endLine": 26, "endColumn": 40}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[side=bottom]:slide-in-from-top-2", "line": 27, "column": 9, "nodeType": null, "endLine": 27, "endColumn": 47}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[side=left]:slide-in-from-right-2", "line": 28, "column": 9, "nodeType": null, "endLine": 28, "endColumn": 47}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[side=right]:slide-in-from-left-2", "line": 29, "column": 9, "nodeType": null, "endLine": 29, "endColumn": 47}, {"ruleId": "better-tailwindcss/no-unregistered-classes", "severity": 1, "message": "Unregistered class detected: data-[side=top]:slide-in-from-bottom-2", "line": 30, "column": 9, "nodeType": null, "endLine": 30, "endColumn": 47}], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "'use client'\n\nimport * as TooltipPrimitive from '@radix-ui/react-tooltip'\nimport * as React from 'react'\n\nimport { cn } from '@/lib/utils'\n\nconst TooltipProvider = TooltipPrimitive.Provider\n\nconst Tooltip = TooltipPrimitive.Root\n\nconst TooltipTrigger = TooltipPrimitive.Trigger\n\nconst TooltipContent = React.forwardRef<\n  React.ElementRef<typeof TooltipPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof TooltipPrimitive.Content>\n>(({ className, sideOffset = 4, ...props }, ref) => (\n  <TooltipPrimitive.Content\n    ref={ref}\n    sideOffset={sideOffset}\n    className={cn(\n      `\n        animate-in fade-in-0 zoom-in-95 z-50 overflow-hidden rounded-md border\n        bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md\n        data-[state=closed]:animate-out data-[state=closed]:fade-out-0\n        data-[state=closed]:zoom-out-95\n        data-[side=bottom]:slide-in-from-top-2\n        data-[side=left]:slide-in-from-right-2\n        data-[side=right]:slide-in-from-left-2\n        data-[side=top]:slide-in-from-bottom-2\n      `,\n      className\n    )}\n    {...props}\n  />\n))\nTooltipContent.displayName = TooltipPrimitive.Content.displayName\n\nexport { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider }\n", "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/use-mobile.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/use-toast.ts", "messages": [{"ruleId": "@typescript-eslint/no-unused-vars", "severity": 1, "message": "'actionTypes' is assigned a value but only used as a type. Allowed unused vars must match /^_/u.", "line": 18, "column": 7, "nodeType": null, "messageId": "usedOnlyAsType", "endLine": 18, "endColumn": 18}], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "'use client'\n\n// Inspired by react-hot-toast library\nimport * as React from 'react'\n\nimport type { ToastActionElement, ToastProps } from '@/components/ui/toast'\n\nconst TOAST_LIMIT = 1\nconst TOAST_REMOVE_DELAY = 1000000\n\ntype ToasterToast = ToastProps & {\n  id: string\n  title?: React.ReactNode\n  description?: React.ReactNode\n  action?: ToastActionElement\n}\n\nconst actionTypes = {\n  ADD_TOAST: 'ADD_TOAST',\n  UPDATE_TOAST: 'UPDATE_TOAST',\n  DISMISS_TOAST: 'DISMISS_TOAST',\n  REMOVE_TOAST: 'REMOVE_TOAST',\n} as const\n\nlet count = 0\n\nfunction genId() {\n  count = (count + 1) % Number.MAX_SAFE_INTEGER\n  return count.toString()\n}\n\ntype ActionType = typeof actionTypes\n\ntype Action =\n  | {\n      type: ActionType['ADD_TOAST']\n      toast: ToasterToast\n    }\n  | {\n      type: ActionType['UPDATE_TOAST']\n      toast: Partial<ToasterToast>\n    }\n  | {\n      type: ActionType['DISMISS_TOAST']\n      toastId?: ToasterToast['id']\n    }\n  | {\n      type: ActionType['REMOVE_TOAST']\n      toastId?: ToasterToast['id']\n    }\n\ninterface State {\n  toasts: ToasterToast[]\n}\n\nconst toastTimeouts = new Map<string, ReturnType<typeof setTimeout>>()\n\nconst addToRemoveQueue = (toastId: string) => {\n  if (toastTimeouts.has(toastId)) {\n    return\n  }\n\n  const timeout = setTimeout(() => {\n    toastTimeouts.delete(toastId)\n    dispatch({\n      type: 'REMOVE_TOAST',\n      toastId,\n    })\n  }, TOAST_REMOVE_DELAY)\n\n  toastTimeouts.set(toastId, timeout)\n}\n\nexport const reducer = (state: State, action: Action): State => {\n  switch (action.type) {\n    case 'ADD_TOAST':\n      return {\n        ...state,\n        toasts: [action.toast, ...state.toasts].slice(0, TOAST_LIMIT),\n      }\n\n    case 'UPDATE_TOAST':\n      return {\n        ...state,\n        toasts: state.toasts.map(t => (t.id === action.toast.id ? { ...t, ...action.toast } : t)),\n      }\n\n    case 'DISMISS_TOAST': {\n      const { toastId } = action\n\n      // ! Side effects ! - This could be extracted into a dismissToast() action,\n      // but I'll keep it here for simplicity\n      if (toastId) {\n        addToRemoveQueue(toastId)\n      } else {\n        state.toasts.forEach(toast => {\n          addToRemoveQueue(toast.id)\n        })\n      }\n\n      return {\n        ...state,\n        toasts: state.toasts.map(t =>\n          t.id === toastId || toastId === undefined\n            ? {\n                ...t,\n                open: false,\n              }\n            : t\n        ),\n      }\n    }\n    case 'REMOVE_TOAST':\n      if (action.toastId === undefined) {\n        return {\n          ...state,\n          toasts: [],\n        }\n      }\n      return {\n        ...state,\n        toasts: state.toasts.filter(t => t.id !== action.toastId),\n      }\n  }\n}\n\nconst listeners: Array<(state: State) => void> = []\n\nlet memoryState: State = { toasts: [] }\n\nfunction dispatch(action: Action) {\n  memoryState = reducer(memoryState, action)\n  listeners.forEach(listener => {\n    listener(memoryState)\n  })\n}\n\ntype Toast = Omit<ToasterToast, 'id'>\n\nfunction toast({ ...props }: Toast) {\n  const id = genId()\n\n  const update = (props: ToasterToast) =>\n    dispatch({\n      type: 'UPDATE_TOAST',\n      toast: { ...props, id },\n    })\n  const dismiss = () => dispatch({ type: 'DISMISS_TOAST', toastId: id })\n\n  dispatch({\n    type: 'ADD_TOAST',\n    toast: {\n      ...props,\n      id,\n      open: true,\n      onOpenChange: open => {\n        if (!open) dismiss()\n      },\n    },\n  })\n\n  return {\n    id,\n    dismiss,\n    update,\n  }\n}\n\nfunction useToast() {\n  const [state, setState] = React.useState<State>(memoryState)\n\n  React.useEffect(() => {\n    listeners.push(setState)\n    return () => {\n      const index = listeners.indexOf(setState)\n      if (index > -1) {\n        listeners.splice(index, 1)\n      }\n    }\n  }, [state])\n\n  return {\n    ...state,\n    toast,\n    dismiss: (toastId?: string) => dispatch({ type: 'DISMISS_TOAST', toastId }),\n  }\n}\n\nexport { useToast, toast }\n", "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/contexts/gdpr-context.tsx", "messages": [{"ruleId": "no-console", "severity": 1, "message": "Unexpected console statement.", "line": 49, "column": 9, "nodeType": "MemberExpression", "messageId": "unexpected", "endLine": 49, "endColumn": 22, "suggestions": [{"fix": {"range": [1445, 1498], "text": ""}, "messageId": "removeConsole", "data": {"propertyName": "error"}, "desc": "Remove the console.error()."}]}, {"ruleId": "no-console", "severity": 1, "message": "Unexpected console statement.", "line": 157, "column": 3, "nodeType": "MemberExpression", "messageId": "unexpected", "endLine": 157, "endColumn": 14, "suggestions": [{"fix": {"range": [3956, 4003], "text": ""}, "messageId": "removeConsole", "data": {"propertyName": "log"}, "desc": "Remove the console.log()."}]}], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "'use client'\n\nimport React, { createContext, useContext, useEffect, useState } from 'react'\n\nexport interface CookieConsent {\n  necessary: boolean\n  analytics: boolean\n  marketing: boolean\n}\n\ninterface GDPRContextType {\n  consent: CookieConsent | null\n  showBanner: boolean\n  showCustomize: boolean\n  acceptAll: () => void\n  rejectAll: () => void\n  saveCustom: (consent: CookieConsent) => void\n  openCustomize: () => void\n  closeBanner: () => void\n}\n\nconst GDPRContext = createContext<GDPRContextType | undefined>(undefined)\n\nconst CONSENT_COOKIE_NAME = 'ponyclub-cookie-consent'\nconst CONSENT_VERSION = '1.0'\n\nexport function GDPRProvider({ children }: { children: React.ReactNode }) {\n  const [consent, setConsent] = useState<CookieConsent | null>(null)\n  const [showBanner, setShowBanner] = useState(false)\n  const [showCustomize, setShowCustomize] = useState(false)\n\n  // Load consent from localStorage on mount\n  useEffect(() => {\n    if (typeof window === 'undefined') return\n\n    const savedConsent = localStorage.getItem(CONSENT_COOKIE_NAME)\n    if (savedConsent) {\n      try {\n        const parsed = JSON.parse(savedConsent)\n        if (parsed.version === CONSENT_VERSION) {\n          setConsent(parsed.consent)\n          // Apply consent to tracking scripts\n          applyConsent(parsed.consent)\n        } else {\n          // Version mismatch, show banner again\n          setShowBanner(true)\n        }\n      } catch (error) {\n        console.error('Error parsing consent cookie:', error)\n        setShowBanner(true)\n      }\n    } else {\n      // No consent found, show banner\n      setShowBanner(true)\n    }\n  }, [])\n\n  const saveConsent = (newConsent: CookieConsent) => {\n    const consentData = {\n      consent: newConsent,\n      version: CONSENT_VERSION,\n      timestamp: new Date().toISOString(),\n    }\n\n    localStorage.setItem(CONSENT_COOKIE_NAME, JSON.stringify(consentData))\n    setConsent(newConsent)\n    setShowBanner(false)\n    setShowCustomize(false)\n\n    // Apply consent to tracking scripts\n    applyConsent(newConsent)\n\n    // Track consent choice\n    trackConsentChoice(newConsent)\n  }\n\n  const acceptAll = () => {\n    saveConsent({\n      necessary: true,\n      analytics: true,\n      marketing: true,\n    })\n  }\n\n  const rejectAll = () => {\n    saveConsent({\n      necessary: true, // Always true - required for site functionality\n      analytics: false,\n      marketing: false,\n    })\n  }\n\n  const saveCustom = (customConsent: CookieConsent) => {\n    saveConsent({\n      ...customConsent,\n      necessary: true, // Always true - required for site functionality\n    })\n  }\n\n  const openCustomize = () => {\n    setShowCustomize(true)\n  }\n\n  const closeBanner = () => {\n    setShowBanner(false)\n    setShowCustomize(false)\n  }\n\n  return (\n    <GDPRContext.Provider\n      value={{\n        consent,\n        showBanner,\n        showCustomize,\n        acceptAll,\n        rejectAll,\n        saveCustom,\n        openCustomize,\n        closeBanner,\n      }}\n    >\n      {children}\n    </GDPRContext.Provider>\n  )\n}\n\nexport function useGDPR() {\n  const context = useContext(GDPRContext)\n  if (context === undefined) {\n    throw new Error('useGDPR must be used within a GDPRProvider')\n  }\n  return context\n}\n\n// Apply consent to tracking scripts\nfunction applyConsent(consent: CookieConsent) {\n  if (typeof window === 'undefined') return\n\n  // Google Analytics\n  if (window.gtag) {\n    window.gtag('consent', 'update', {\n      analytics_storage: consent.analytics ? 'granted' : 'denied',\n      ad_storage: consent.marketing ? 'granted' : 'denied',\n      ad_user_data: consent.marketing ? 'granted' : 'denied',\n      ad_personalization: consent.marketing ? 'granted' : 'denied',\n    })\n  }\n\n  // Facebook Pixel\n  if (window.fbq && !consent.marketing) {\n    // Disable Facebook Pixel if marketing consent is denied\n    window.fbq('consent', 'revoke')\n  } else if (window.fbq && consent.marketing) {\n    window.fbq('consent', 'grant')\n  }\n\n  console.log('[GDPR] Consent applied:', consent)\n}\n\n// Track consent choice for analytics\nfunction trackConsentChoice(consent: CookieConsent) {\n  if (typeof window === 'undefined') return\n\n  // Only track if analytics consent is given\n  if (consent.analytics && window.gtag) {\n    window.gtag('event', 'consent_update', {\n      event_category: 'GDPR',\n      analytics_consent: consent.analytics,\n      marketing_consent: consent.marketing,\n      consent_method: 'banner',\n    })\n  }\n}\n", "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/contexts/language-context.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/hooks/use-mobile.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/hooks/use-toast.ts", "messages": [{"ruleId": "@typescript-eslint/no-unused-vars", "severity": 1, "message": "'actionTypes' is assigned a value but only used as a type. Allowed unused vars must match /^_/u.", "line": 18, "column": 7, "nodeType": null, "messageId": "usedOnlyAsType", "endLine": 18, "endColumn": 18}], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "'use client'\n\n// Inspired by react-hot-toast library\nimport * as React from 'react'\n\nimport type { ToastActionElement, ToastProps } from '@/components/ui/toast'\n\nconst TOAST_LIMIT = 1\nconst TOAST_REMOVE_DELAY = 1000000\n\ntype ToasterToast = ToastProps & {\n  id: string\n  title?: React.ReactNode\n  description?: React.ReactNode\n  action?: ToastActionElement\n}\n\nconst actionTypes = {\n  ADD_TOAST: 'ADD_TOAST',\n  UPDATE_TOAST: 'UPDATE_TOAST',\n  DISMISS_TOAST: 'DISMISS_TOAST',\n  REMOVE_TOAST: 'REMOVE_TOAST',\n} as const\n\nlet count = 0\n\nfunction genId() {\n  count = (count + 1) % Number.MAX_SAFE_INTEGER\n  return count.toString()\n}\n\ntype ActionType = typeof actionTypes\n\ntype Action =\n  | {\n      type: ActionType['ADD_TOAST']\n      toast: ToasterToast\n    }\n  | {\n      type: ActionType['UPDATE_TOAST']\n      toast: Partial<ToasterToast>\n    }\n  | {\n      type: ActionType['DISMISS_TOAST']\n      toastId?: ToasterToast['id']\n    }\n  | {\n      type: ActionType['REMOVE_TOAST']\n      toastId?: ToasterToast['id']\n    }\n\ninterface State {\n  toasts: ToasterToast[]\n}\n\nconst toastTimeouts = new Map<string, ReturnType<typeof setTimeout>>()\n\nconst addToRemoveQueue = (toastId: string) => {\n  if (toastTimeouts.has(toastId)) {\n    return\n  }\n\n  const timeout = setTimeout(() => {\n    toastTimeouts.delete(toastId)\n    dispatch({\n      type: 'REMOVE_TOAST',\n      toastId,\n    })\n  }, TOAST_REMOVE_DELAY)\n\n  toastTimeouts.set(toastId, timeout)\n}\n\nexport const reducer = (state: State, action: Action): State => {\n  switch (action.type) {\n    case 'ADD_TOAST':\n      return {\n        ...state,\n        toasts: [action.toast, ...state.toasts].slice(0, TOAST_LIMIT),\n      }\n\n    case 'UPDATE_TOAST':\n      return {\n        ...state,\n        toasts: state.toasts.map(t => (t.id === action.toast.id ? { ...t, ...action.toast } : t)),\n      }\n\n    case 'DISMISS_TOAST': {\n      const { toastId } = action\n\n      // ! Side effects ! - This could be extracted into a dismissToast() action,\n      // but I'll keep it here for simplicity\n      if (toastId) {\n        addToRemoveQueue(toastId)\n      } else {\n        state.toasts.forEach(toast => {\n          addToRemoveQueue(toast.id)\n        })\n      }\n\n      return {\n        ...state,\n        toasts: state.toasts.map(t =>\n          t.id === toastId || toastId === undefined\n            ? {\n                ...t,\n                open: false,\n              }\n            : t\n        ),\n      }\n    }\n    case 'REMOVE_TOAST':\n      if (action.toastId === undefined) {\n        return {\n          ...state,\n          toasts: [],\n        }\n      }\n      return {\n        ...state,\n        toasts: state.toasts.filter(t => t.id !== action.toastId),\n      }\n  }\n}\n\nconst listeners: Array<(state: State) => void> = []\n\nlet memoryState: State = { toasts: [] }\n\nfunction dispatch(action: Action) {\n  memoryState = reducer(memoryState, action)\n  listeners.forEach(listener => {\n    listener(memoryState)\n  })\n}\n\ntype Toast = Omit<ToasterToast, 'id'>\n\nfunction toast({ ...props }: Toast) {\n  const id = genId()\n\n  const update = (props: ToasterToast) =>\n    dispatch({\n      type: 'UPDATE_TOAST',\n      toast: { ...props, id },\n    })\n  const dismiss = () => dispatch({ type: 'DISMISS_TOAST', toastId: id })\n\n  dispatch({\n    type: 'ADD_TOAST',\n    toast: {\n      ...props,\n      id,\n      open: true,\n      onOpenChange: open => {\n        if (!open) dismiss()\n      },\n    },\n  })\n\n  return {\n    id,\n    dismiss,\n    update,\n  }\n}\n\nfunction useToast() {\n  const [state, setState] = React.useState<State>(memoryState)\n\n  React.useEffect(() => {\n    listeners.push(setState)\n    return () => {\n      const index = listeners.indexOf(setState)\n      if (index > -1) {\n        listeners.splice(index, 1)\n      }\n    }\n  }, [state])\n\n  return {\n    ...state,\n    toast,\n    dismiss: (toastId?: string) => dispatch({ type: 'DISMISS_TOAST', toastId }),\n  }\n}\n\nexport { useToast, toast }\n", "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/lib/bokun-lang.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/lib/gallery-data/index.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/lib/gallery-data/kayaking.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/lib/gallery-data/rafting.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/lib/gallery-data/riding.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/lib/gallery-data/river-village.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/lib/gallery-data/trekking.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/lib/image-optimization.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/lib/sitemap-data.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/lib/translations/el.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/lib/translations/en.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/lib/translations/index.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/lib/use-bokun-language.ts", "messages": [{"ruleId": "@typescript-eslint/no-explicit-any", "severity": 1, "message": "Unexpected any. Specify a different type.", "line": 6, "column": 30, "nodeType": "TSAnyKeyword", "messageId": "unexpectedAny", "endLine": 6, "endColumn": 33, "suggestions": [{"messageId": "suggestUnknown", "fix": {"range": [179, 182], "text": "unknown"}, "desc": "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct."}, {"messageId": "suggestNever", "fix": {"range": [179, 182], "text": "never"}, "desc": "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of."}]}], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "import { useEffect } from 'react'\n\nexport function useBokunLanguage(lang: string) {\n  useEffect(() => {\n    if (typeof window === 'undefined') return\n    const bokun = (window as any).BokunWidgets\n    if (!bokun) return\n\n    // Set global default for pop-up checkout (if supported)\n    if (typeof bokun.setLanguage === 'function') {\n      bokun.setLanguage(lang)\n    }\n\n    // Update all widgets and buttons with the new language\n    document.querySelectorAll<HTMLElement>('.bokunWidget, .bokunButton').forEach(el => {\n      // Update data-src URL with lang query param\n      if (el.dataset.src) {\n        const url = new URL(el.dataset.src, window.location.origin)\n        url.searchParams.set('lang', lang)\n        el.dataset.src = url.toString()\n      }\n      // Set data-lang attribute for newer widgets\n      el.setAttribute('data-lang', lang)\n    })\n\n    // Reload widgets to apply language change\n    if (typeof bokun.reload === 'function') {\n      bokun.reload()\n    }\n  }, [lang])\n}\n", "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/lib/utils.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/middleware.ts", "messages": [{"ruleId": "@typescript-eslint/no-unused-vars", "severity": 1, "message": "'devScriptSources' is assigned a value but never used. Allowed unused vars must match /^_/u.", "line": 12, "column": 7, "nodeType": null, "messageId": "unusedVar", "endLine": 12, "endColumn": 23}], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "import type { NoseconeOptions } from '@nosecone/next'\nimport { createMiddleware as createNoseconeMiddleware, defaults as noseconeDefaults } from '@nosecone/next'\nimport type { NextRequest } from 'next/server'\nimport { NextResponse } from 'next/server'\n\nconst locales = ['en', 'el']\nconst defaultLocale = 'en'\n\nconst isDev = process.env.NODE_ENV === 'development'\n\n// Define additional script sources for development\nconst devScriptSources = isDev\n  ? ([\"'unsafe-eval'\", \"'unsafe-inline'\", 'https://vercel.live', 'https://va.vercel-scripts.com'] as const)\n  : ([] as const)\n\n// Define additional connect sources for development\nconst devConnectSources = isDev\n  ? (['ws://localhost:3000', 'https://vercel.live', 'https://va.vercel-scripts.com'] as const)\n  : ([] as const)\n\nconst noseconeOptions: NoseconeOptions = {\n  ...noseconeDefaults,\n  // Disable COEP to prevent cross-origin resource blocking\n  crossOriginEmbedderPolicy: false,\n  contentSecurityPolicy: isDev\n    ? false // Disable CSP entirely in development to allow all inline scripts\n    : {\n        // Use nosecone defaults with nonces for production\n        ...noseconeDefaults.contentSecurityPolicy,\n        directives: {\n          ...noseconeDefaults.contentSecurityPolicy.directives,\n          baseUri: [\"'none'\"],\n          childSrc: [\"'none'\"],\n          defaultSrc: [\"'self'\"],\n          objectSrc: [\"'none'\"],\n          formAction: [\"'self'\"],\n          frameAncestors: [\"'none'\"],\n          manifestSrc: [\"'self'\"],\n          mediaSrc: [\"'self'\"],\n          scriptSrc: [\n            ...noseconeDefaults.contentSecurityPolicy.directives.scriptSrc,\n            \"'strict-dynamic'\" as const,\n            'https://widgets.bokun.io' as const,\n            'https://static.bokun.io' as const,\n            'https://cdn.bokun.io' as const,\n            'https://assets.bokun.io' as const,\n            'https://www.googletagmanager.com' as const,\n            'https://www.google-analytics.com' as const,\n            'https://maps.googleapis.com' as const,\n            'https://js-agent.newrelic.com' as const,\n            'https://featurable.com' as const,\n            'https://www.gstatic.com' as const,\n            'https://apis.google.com' as const,\n            'https://vercel.live' as const, // Added for Vercel preview toolbar script\n            \"'sha256-VYskjExgHaP1F6hubwqGdG9++A8I+HfVOuylfR5fUJ0='\" as const, // Added for Bokun widget's inline script\n          ],\n          // Add worker-src directive to allow blob workers\n          workerSrc: [\n            \"'self'\",\n            'blob:',\n            'https://widgets.bokun.io',\n            'https://static.bokun.io',\n            'https://maps.googleapis.com',\n          ],\n          styleSrc: [\n            ...noseconeDefaults.contentSecurityPolicy.directives.styleSrc,\n            'https://fonts.googleapis.com' as const,\n            'https://widgets.bokun.io' as const,\n            'https://static.bokun.io' as const,\n            'https://cdn.bokun.io' as const,\n          ],\n          imgSrc: [\n            ...noseconeDefaults.contentSecurityPolicy.directives.imgSrc,\n            'https://images.unsplash.com' as const,\n            'https://maps.googleapis.com' as const,\n            'https://maps.gstatic.com' as const,\n            'https://www.google-analytics.com' as const,\n            'https://widgets.bokun.io' as const,\n            'https://static.bokun.io' as const,\n            'https://cdn.bokun.io' as const,\n            'https://assets.bokun.io' as const,\n            'https://ponyclub.gr' as const,\n            'https://www.ponyclub.gr' as const,\n            'https://www.googletagmanager.com' as const,\n            'https://pagead2.googlesyndication.com' as const,\n            'https://media-cdn.tripadvisor.com' as const,\n            'https://lh3.googleusercontent.com' as const,\n            'https://www.google.com' as const,\n            'https://www.google.fi' as const,\n            'https://www.google.co.uk' as const,\n            'https://www.google.de' as const,\n            'https://www.google.fr' as const,\n            'https://www.google.it' as const,\n            'https://www.google.es' as const,\n            'https://www.google.nl' as const,\n            'https://www.google.be' as const,\n            'https://www.google.at' as const,\n            'https://www.google.ch' as const,\n            'https://www.google.se' as const,\n            'https://www.google.no' as const,\n            'https://www.google.dk' as const,\n            'https://www.google.pl' as const,\n            'https://www.google.cz' as const,\n            'https://www.google.hu' as const,\n            'https://www.google.ro' as const,\n            'https://www.google.bg' as const,\n            'https://www.google.hr' as const,\n            'https://www.google.si' as const,\n            'https://www.google.sk' as const,\n            'https://www.google.lt' as const,\n            'https://www.google.lv' as const,\n            'https://www.google.ee' as const,\n            'https://www.google.ie' as const,\n            'https://www.google.pt' as const,\n            'https://www.google.gr' as const,\n            'https://www.google.cy' as const,\n            'https://www.google.mt' as const,\n            'https://www.google.lu' as const,\n            'https://googleads.g.doubleclick.net' as const,\n          ],\n          fontSrc: [\n            ...noseconeDefaults.contentSecurityPolicy.directives.fontSrc,\n            'https://fonts.gstatic.com' as const,\n            'https://widgets.bokun.io' as const,\n            'https://static.bokun.io' as const,\n          ],\n          connectSrc: [\n            ...noseconeDefaults.contentSecurityPolicy.directives.connectSrc,\n            'https://analytics.google.com' as const,\n            'https://region1.google-analytics.com' as const,\n            'https://region1.analytics.google.com' as const,\n            'https://widgets.bokun.io' as const,\n            'https://static.bokun.io' as const,\n            'https://cdn.bokun.io' as const,\n            'https://api.bokun.io' as const,\n            'https://maps.googleapis.com' as const,\n            'https://pagead2.googlesyndication.com' as const,\n            'https://featurable.com' as const, // Added for react-google-reviews\n            'https://www.google.com' as const,\n            'https://google.com' as const,\n            'https://googleads.g.doubleclick.net' as const,\n            'https://stats.g.doubleclick.net' as const,\n            ...devConnectSources,\n          ],\n          frameSrc: [\n            \"'self'\" as const, // Added 'self'\n            'https://widgets.bokun.io' as const,\n            'https://static.bokun.io' as const,\n            'https://www.google.com' as const,\n            'https://www.googletagmanager.com' as const,\n            'https://vercel.live' as const, // Added for Vercel preview toolbar\n          ],\n          upgradeInsecureRequests: process.env.NODE_ENV === 'production',\n          reportUri: ['/api/csp-violations'], // Add CSP violation reporting endpoint\n        },\n      },\n  // Vercel toolbar helper for previews\n  ...(process.env.VERCEL_ENV === 'preview' ? { withVercelToolbar: true } : {}),\n  strictTransportSecurity: {\n    maxAge: 63072000,\n    includeSubDomains: true,\n    preload: true,\n  },\n  xFrameOptions: { action: 'deny' },\n  referrerPolicy: { policy: ['no-referrer'] },\n}\n\nconst configuredNoseconeMiddleware = createNoseconeMiddleware(noseconeOptions)\n\nexport async function middleware(request: NextRequest) {\n  let response = await configuredNoseconeMiddleware()\n\n  if (!response) {\n    response = NextResponse.next()\n  }\n\n  const { pathname } = request.nextUrl\n  const pathnameHasLocale = locales.some(locale => pathname.startsWith(`/${locale}/`) || pathname === `/${locale}`)\n\n  if (!pathnameHasLocale) {\n    // Check for a saved locale preference in the cookie\n    const localeCookie = request.cookies.get('NEXT_LOCALE')?.value\n    const chosenLocale = locales.includes(localeCookie as string) ? localeCookie : defaultLocale\n\n    const newPathname = pathname === '/' ? `/${chosenLocale}` : `/${chosenLocale}${pathname}`\n    const url = request.nextUrl.clone()\n    url.pathname = newPathname\n\n    const i18nRedirectResponse = NextResponse.redirect(url)\n    // Copy headers from the security middleware response to the redirect response\n    response.headers.forEach((value, key) => {\n      if (!i18nRedirectResponse.headers.has(key)) {\n        i18nRedirectResponse.headers.set(key, value)\n      }\n    })\n    return i18nRedirectResponse\n  }\n\n  response.headers.set('Permissions-Policy', 'camera=(), microphone=(), geolocation=(self), payment=()')\n\n  return response\n}\n\nexport const config = {\n  matcher: [\n    '/((?!api|monitoring|_next/static|_next/image|images|assets|fonts|favicon.ico|robots.txt|sitemap.xml|sw.js|manifest.webmanifest|.*\\\\..*).*)',\n  ],\n}\n", "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/next-env.d.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/scripts/generate-sitemap-data.js", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": []}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/types/bokun.d.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/types/esbuild.d.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/types/global.d.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/types/pg-protocol.d.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}, {"filePath": "/Users/<USER>/Documents/GitHub/ponyclub-v0/types/vite.d.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "max-len", "replacedBy": ["@stylistic/js/max-len"], "info": {"message": "Formatting rules are being moved out of ESLint core.", "url": "https://eslint.org/blog/2023/10/deprecating-formatting-rules/", "deprecatedSince": "8.53.0", "availableUntil": "10.0.0", "replacedBy": [{"message": "ESLint Stylistic now maintains deprecated stylistic core rules.", "url": "https://eslint.style/guide/migration", "plugin": {"name": "@stylistic/eslint-plugin-js", "url": "https://eslint.style/packages/js"}, "rule": {"name": "max-len", "url": "https://eslint.style/rules/js/max-len"}}]}}]}]