[{"/Users/<USER>/Documents/GitHub/ponyclub-v0/app/[locale]/error.tsx": "1", "/Users/<USER>/Documents/GitHub/ponyclub-v0/app/[locale]/for-schools/page.tsx": "2", "/Users/<USER>/Documents/GitHub/ponyclub-v0/app/[locale]/kayak-rafting/page.tsx": "3", "/Users/<USER>/Documents/GitHub/ponyclub-v0/app/[locale]/kayaking/page.tsx": "4", "/Users/<USER>/Documents/GitHub/ponyclub-v0/app/[locale]/layout.tsx": "5", "/Users/<USER>/Documents/GitHub/ponyclub-v0/app/[locale]/not-found.tsx": "6", "/Users/<USER>/Documents/GitHub/ponyclub-v0/app/[locale]/page.tsx": "7", "/Users/<USER>/Documents/GitHub/ponyclub-v0/app/[locale]/rafting/page.tsx": "8", "/Users/<USER>/Documents/GitHub/ponyclub-v0/app/[locale]/riding/page.tsx": "9", "/Users/<USER>/Documents/GitHub/ponyclub-v0/app/[locale]/river-village/page.tsx": "10", "/Users/<USER>/Documents/GitHub/ponyclub-v0/app/[locale]/test-footer/page.tsx": "11", "/Users/<USER>/Documents/GitHub/ponyclub-v0/app/[locale]/trekking/page.tsx": "12", "/Users/<USER>/Documents/GitHub/ponyclub-v0/app/api/csp-violations/route.ts": "13", "/Users/<USER>/Documents/GitHub/ponyclub-v0/app/global-error.tsx": "14", "/Users/<USER>/Documents/GitHub/ponyclub-v0/app/sitemap.ts": "15", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ActivityPageLayout.tsx": "16", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/BokunWidget.tsx": "17", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ClientLayout.tsx": "18", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/DynamicBokunWidget.tsx": "19", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/DynamicContactDetails.tsx": "20", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/DynamicGallery.tsx": "21", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/DynamicGoogleMap.tsx": "22", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/EnhancedPackageCard.tsx": "23", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/Footer.tsx": "24", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/HomePageContent.tsx": "25", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/PageLayout.tsx": "26", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/SummerProgramCard.tsx": "27", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/activities-menu.tsx": "28", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/client/BokunStyles.tsx": "29", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/client/BookingButton.tsx": "30", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/client/GDPRBanner.tsx": "31", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/client/GDPRGoogleAnalytics.tsx": "32", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/client/GoogleReviews.tsx": "33", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/client/PriceListButton.tsx": "34", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/contact-details-enhanced.tsx": "35", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/desktop-menu.tsx": "36", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/hamburger-menu.tsx": "37", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/hero/HeroSection.tsx": "38", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/language-selector.tsx": "39", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/responsive-navigation.tsx": "40", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/site-header.tsx": "41", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/standalone-menu-items.tsx": "42", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/theme-provider.tsx": "43", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/Container.tsx": "44", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/Gallery.tsx": "45", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/OptimizedImage.tsx": "46", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/PriceListPopup.tsx": "47", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/accordion.tsx": "48", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/alert-dialog.tsx": "49", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/alert.tsx": "50", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/aspect-ratio.tsx": "51", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/avatar.tsx": "52", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/badge.tsx": "53", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/border-beam.tsx": "54", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/breadcrumb.tsx": "55", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/button.tsx": "56", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/calendar.tsx": "57", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/card.tsx": "58", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/carousel.tsx": "59", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/chart.tsx": "60", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/checkbox.tsx": "61", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/collapsible.tsx": "62", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/command.tsx": "63", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/context-menu.tsx": "64", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/dialog.tsx": "65", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/drawer.tsx": "66", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/dropdown-menu.tsx": "67", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/form.tsx": "68", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/grid-pattern.tsx": "69", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/hover-card.tsx": "70", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/input-otp.tsx": "71", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/input.tsx": "72", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/label.tsx": "73", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/menubar.tsx": "74", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/navigation-menu.tsx": "75", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/number-ticker.tsx": "76", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/pagination.tsx": "77", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/popover.tsx": "78", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/progress.tsx": "79", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/pulsating-button.tsx": "80", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/radio-group.tsx": "81", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/resizable.tsx": "82", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/script-loader.tsx": "83", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/scroll-area.tsx": "84", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/select.tsx": "85", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/separator.tsx": "86", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/sheet.tsx": "87", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/sidebar.tsx": "88", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/skeleton.tsx": "89", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/slider.tsx": "90", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/sonner.tsx": "91", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/switch.tsx": "92", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/table.tsx": "93", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/tabs.tsx": "94", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/textarea.tsx": "95", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/toast.tsx": "96", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/toaster.tsx": "97", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/toggle-group.tsx": "98", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/toggle.tsx": "99", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/tooltip.tsx": "100", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/use-mobile.tsx": "101", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/use-toast.ts": "102", "/Users/<USER>/Documents/GitHub/ponyclub-v0/contexts/gdpr-context.tsx": "103", "/Users/<USER>/Documents/GitHub/ponyclub-v0/contexts/language-context.tsx": "104", "/Users/<USER>/Documents/GitHub/ponyclub-v0/hooks/use-mobile.tsx": "105", "/Users/<USER>/Documents/GitHub/ponyclub-v0/hooks/use-toast.ts": "106", "/Users/<USER>/Documents/GitHub/ponyclub-v0/lib/bokun-lang.ts": "107", "/Users/<USER>/Documents/GitHub/ponyclub-v0/lib/gallery-data/index.ts": "108", "/Users/<USER>/Documents/GitHub/ponyclub-v0/lib/gallery-data/kayaking.ts": "109", "/Users/<USER>/Documents/GitHub/ponyclub-v0/lib/gallery-data/rafting.ts": "110", "/Users/<USER>/Documents/GitHub/ponyclub-v0/lib/gallery-data/riding.ts": "111", "/Users/<USER>/Documents/GitHub/ponyclub-v0/lib/gallery-data/river-village.ts": "112", "/Users/<USER>/Documents/GitHub/ponyclub-v0/lib/gallery-data/trekking.ts": "113", "/Users/<USER>/Documents/GitHub/ponyclub-v0/lib/image-optimization.ts": "114", "/Users/<USER>/Documents/GitHub/ponyclub-v0/lib/sitemap-data.ts": "115", "/Users/<USER>/Documents/GitHub/ponyclub-v0/lib/translations/el.ts": "116", "/Users/<USER>/Documents/GitHub/ponyclub-v0/lib/translations/en.ts": "117", "/Users/<USER>/Documents/GitHub/ponyclub-v0/lib/translations/index.ts": "118", "/Users/<USER>/Documents/GitHub/ponyclub-v0/lib/use-bokun-language.ts": "119", "/Users/<USER>/Documents/GitHub/ponyclub-v0/lib/utils.ts": "120", "/Users/<USER>/Documents/GitHub/ponyclub-v0/middleware.ts": "121", "/Users/<USER>/Documents/GitHub/ponyclub-v0/next-env.d.ts": "122", "/Users/<USER>/Documents/GitHub/ponyclub-v0/scripts/generate-sitemap-data.js": "123", "/Users/<USER>/Documents/GitHub/ponyclub-v0/types/bokun.d.ts": "124", "/Users/<USER>/Documents/GitHub/ponyclub-v0/types/esbuild.d.ts": "125", "/Users/<USER>/Documents/GitHub/ponyclub-v0/types/global.d.ts": "126", "/Users/<USER>/Documents/GitHub/ponyclub-v0/types/pg-protocol.d.ts": "127", "/Users/<USER>/Documents/GitHub/ponyclub-v0/types/vite.d.ts": "128"}, {"size": 2584, "mtime": 1749657765547, "results": "129", "hashOfConfig": "130"}, {"size": 41219, "mtime": 1749657766551, "results": "131", "hashOfConfig": "130"}, {"size": 811, "mtime": 1749582033644, "results": "132", "hashOfConfig": "130"}, {"size": 5144, "mtime": 1749657765547, "results": "133", "hashOfConfig": "130"}, {"size": 8209, "mtime": 1749657765547, "results": "134", "hashOfConfig": "130"}, {"size": 2007, "mtime": 1749657765547, "results": "135", "hashOfConfig": "130"}, {"size": 426, "mtime": 1749594729006, "results": "136", "hashOfConfig": "130"}, {"size": 4733, "mtime": 1749657765547, "results": "137", "hashOfConfig": "130"}, {"size": 2035, "mtime": 1749594741410, "results": "138", "hashOfConfig": "130"}, {"size": 5540, "mtime": 1749657765547, "results": "139", "hashOfConfig": "130"}, {"size": 1660, "mtime": 1749657765547, "results": "140", "hashOfConfig": "130"}, {"size": 16426, "mtime": 1749657765547, "results": "141", "hashOfConfig": "130"}, {"size": 765, "mtime": 1749594857483, "results": "142", "hashOfConfig": "130"}, {"size": 658, "mtime": 1749582033744, "results": "143", "hashOfConfig": "130"}, {"size": 929, "mtime": 1749594754901, "results": "144", "hashOfConfig": "130"}, {"size": 6449, "mtime": 1749657765547, "results": "145", "hashOfConfig": "130"}, {"size": 8256, "mtime": 1749594998111, "results": "146", "hashOfConfig": "130"}, {"size": 1475, "mtime": 1749582033832, "results": "147", "hashOfConfig": "130"}, {"size": 1892, "mtime": 1749657766710, "results": "148", "hashOfConfig": "130"}, {"size": 428, "mtime": 1749657766712, "results": "149", "hashOfConfig": "130"}, {"size": 634, "mtime": 1749657766714, "results": "150", "hashOfConfig": "130"}, {"size": 606, "mtime": 1749657765547, "results": "151", "hashOfConfig": "130"}, {"size": 11511, "mtime": 1749657765547, "results": "152", "hashOfConfig": "130"}, {"size": 3414, "mtime": 1749657765547, "results": "153", "hashOfConfig": "130"}, {"size": 14869, "mtime": 1749657766758, "results": "154", "hashOfConfig": "130"}, {"size": 920, "mtime": 1749655104662, "results": "155", "hashOfConfig": "130"}, {"size": 8096, "mtime": 1749657765547, "results": "156", "hashOfConfig": "130"}, {"size": 4073, "mtime": 1749657765547, "results": "157", "hashOfConfig": "130"}, {"size": 2511, "mtime": 1749582033802, "results": "158", "hashOfConfig": "130"}, {"size": 5401, "mtime": 1749656350147, "results": "159", "hashOfConfig": "130"}, {"size": 10218, "mtime": 1749657765547, "results": "160", "hashOfConfig": "130"}, {"size": 2228, "mtime": 1749582033823, "results": "161", "hashOfConfig": "130"}, {"size": 1683, "mtime": 1749657765547, "results": "162", "hashOfConfig": "130"}, {"size": 801, "mtime": 1749656350147, "results": "163", "hashOfConfig": "130"}, {"size": 4879, "mtime": 1749657765547, "results": "164", "hashOfConfig": "130"}, {"size": 9028, "mtime": 1749657765547, "results": "165", "hashOfConfig": "130"}, {"size": 7916, "mtime": 1749657765547, "results": "166", "hashOfConfig": "130"}, {"size": 4058, "mtime": 1749657766747, "results": "167", "hashOfConfig": "130"}, {"size": 3264, "mtime": 1749657765547, "results": "168", "hashOfConfig": "130"}, {"size": 1450, "mtime": 1749582033930, "results": "169", "hashOfConfig": "130"}, {"size": 1161, "mtime": 1749657765547, "results": "170", "hashOfConfig": "130"}, {"size": 1116, "mtime": 1749657765547, "results": "171", "hashOfConfig": "130"}, {"size": 287, "mtime": 1749582033941, "results": "172", "hashOfConfig": "130"}, {"size": 1128, "mtime": 1749657765547, "results": "173", "hashOfConfig": "130"}, {"size": 3862, "mtime": 1749657766882, "results": "174", "hashOfConfig": "130"}, {"size": 4099, "mtime": 1749656350147, "results": "175", "hashOfConfig": "130"}, {"size": 4901, "mtime": 1749657765547, "results": "176", "hashOfConfig": "130"}, {"size": 2055, "mtime": 1749657766780, "results": "177", "hashOfConfig": "130"}, {"size": 4605, "mtime": 1749657766785, "results": "178", "hashOfConfig": "130"}, {"size": 1628, "mtime": 1749657765547, "results": "179", "hashOfConfig": "130"}, {"size": 154, "mtime": 1749582033957, "results": "180", "hashOfConfig": "130"}, {"size": 2294, "mtime": 1749657766793, "results": "181", "hashOfConfig": "130"}, {"size": 1100, "mtime": 1749656350147, "results": "182", "hashOfConfig": "130"}, {"size": 2369, "mtime": 1749657765547, "results": "183", "hashOfConfig": "130"}, {"size": 2797, "mtime": 1749657766802, "results": "184", "hashOfConfig": "130"}, {"size": 1857, "mtime": 1749656350147, "results": "185", "hashOfConfig": "130"}, {"size": 3025, "mtime": 1749657765547, "results": "186", "hashOfConfig": "130"}, {"size": 1736, "mtime": 1749657766817, "results": "187", "hashOfConfig": "130"}, {"size": 6178, "mtime": 1749656350147, "results": "188", "hashOfConfig": "130"}, {"size": 10882, "mtime": 1749657765547, "results": "189", "hashOfConfig": "130"}, {"size": 1125, "mtime": 1749657766841, "results": "190", "hashOfConfig": "130"}, {"size": 329, "mtime": 1749582034004, "results": "191", "hashOfConfig": "130"}, {"size": 5106, "mtime": 1749657766846, "results": "192", "hashOfConfig": "130"}, {"size": 7524, "mtime": 1749657766855, "results": "193", "hashOfConfig": "130"}, {"size": 4118, "mtime": 1749657765547, "results": "194", "hashOfConfig": "130"}, {"size": 3015, "mtime": 1749657766864, "results": "195", "hashOfConfig": "130"}, {"size": 7733, "mtime": 1749657766872, "results": "196", "hashOfConfig": "130"}, {"size": 4021, "mtime": 1749657766878, "results": "197", "hashOfConfig": "130"}, {"size": 1452, "mtime": 1749656350147, "results": "198", "hashOfConfig": "130"}, {"size": 1288, "mtime": 1749656350147, "results": "199", "hashOfConfig": "130"}, {"size": 2370, "mtime": 1749657766893, "results": "200", "hashOfConfig": "130"}, {"size": 913, "mtime": 1749656350147, "results": "201", "hashOfConfig": "130"}, {"size": 708, "mtime": 1749656350147, "results": "202", "hashOfConfig": "130"}, {"size": 8224, "mtime": 1749657766906, "results": "203", "hashOfConfig": "130"}, {"size": 5253, "mtime": 1749657766913, "results": "204", "hashOfConfig": "130"}, {"size": 1756, "mtime": 1749657765547, "results": "205", "hashOfConfig": "130"}, {"size": 2705, "mtime": 1749657766930, "results": "206", "hashOfConfig": "130"}, {"size": 1356, "mtime": 1749656350147, "results": "207", "hashOfConfig": "130"}, {"size": 773, "mtime": 1749657766938, "results": "208", "hashOfConfig": "130"}, {"size": 1147, "mtime": 1749657765547, "results": "209", "hashOfConfig": "130"}, {"size": 1523, "mtime": 1749657766943, "results": "210", "hashOfConfig": "130"}, {"size": 1904, "mtime": 1749657765547, "results": "211", "hashOfConfig": "130"}, {"size": 2741, "mtime": 1749591564793, "results": "212", "hashOfConfig": "130"}, {"size": 1615, "mtime": 1749657766950, "results": "213", "hashOfConfig": "130"}, {"size": 5749, "mtime": 1749657766956, "results": "214", "hashOfConfig": "130"}, {"size": 706, "mtime": 1749655665595, "results": "215", "hashOfConfig": "130"}, {"size": 4456, "mtime": 1749657766964, "results": "216", "hashOfConfig": "130"}, {"size": 24078, "mtime": 1749657766983, "results": "217", "hashOfConfig": "130"}, {"size": 231, "mtime": 1749582034149, "results": "218", "hashOfConfig": "130"}, {"size": 1164, "mtime": 1749657765547, "results": "219", "hashOfConfig": "130"}, {"size": 870, "mtime": 1749582034152, "results": "220", "hashOfConfig": "130"}, {"size": 1269, "mtime": 1749656350147, "results": "221", "hashOfConfig": "130"}, {"size": 2912, "mtime": 1749657766995, "results": "222", "hashOfConfig": "130"}, {"size": 2021, "mtime": 1749656350147, "results": "223", "hashOfConfig": "130"}, {"size": 807, "mtime": 1749656350147, "results": "224", "hashOfConfig": "130"}, {"size": 5021, "mtime": 1749656350147, "results": "225", "hashOfConfig": "130"}, {"size": 739, "mtime": 1749582034170, "results": "226", "hashOfConfig": "130"}, {"size": 1714, "mtime": 1749657767010, "results": "227", "hashOfConfig": "130"}, {"size": 1551, "mtime": 1749656350147, "results": "228", "hashOfConfig": "130"}, {"size": 1231, "mtime": 1749656350147, "results": "229", "hashOfConfig": "130"}, {"size": 565, "mtime": 1749582034181, "results": "230", "hashOfConfig": "130"}, {"size": 3901, "mtime": 1749582034187, "results": "231", "hashOfConfig": "130"}, {"size": 4440, "mtime": 1749582034197, "results": "232", "hashOfConfig": "130"}, {"size": 2547, "mtime": 1749655104662, "results": "233", "hashOfConfig": "130"}, {"size": 565, "mtime": 1749582034336, "results": "234", "hashOfConfig": "130"}, {"size": 3901, "mtime": 1749655104662, "results": "235", "hashOfConfig": "130"}, {"size": 104, "mtime": 1749582034344, "results": "236", "hashOfConfig": "130"}, {"size": 105, "mtime": 1749582034345, "results": "237", "hashOfConfig": "130"}, {"size": 1174, "mtime": 1749582034346, "results": "238", "hashOfConfig": "130"}, {"size": 1488, "mtime": 1749582034347, "results": "239", "hashOfConfig": "130"}, {"size": 1054, "mtime": 1749582034349, "results": "240", "hashOfConfig": "130"}, {"size": 1449, "mtime": 1749582034350, "results": "241", "hashOfConfig": "130"}, {"size": 1245, "mtime": 1749582034351, "results": "242", "hashOfConfig": "130"}, {"size": 2975, "mtime": 1749582034354, "results": "243", "hashOfConfig": "130"}, {"size": 688, "mtime": 1749657767334, "results": "244", "hashOfConfig": "130"}, {"size": 15830, "mtime": 1749657090383, "results": "245", "hashOfConfig": "130"}, {"size": 9693, "mtime": 1749657001707, "results": "246", "hashOfConfig": "130"}, {"size": 173, "mtime": 1749582034366, "results": "247", "hashOfConfig": "130"}, {"size": 992, "mtime": 1749582034368, "results": "248", "hashOfConfig": "130"}, {"size": 166, "mtime": 1749582034369, "results": "249", "hashOfConfig": "130"}, {"size": 8817, "mtime": 1749600042190, "results": "250", "hashOfConfig": "130"}, {"size": 211, "mtime": 1749206852000, "results": "251", "hashOfConfig": "252"}, {"size": 2569, "mtime": 1749582034444, "results": "253", "hashOfConfig": "254"}, {"size": 264, "mtime": 1749582034460, "results": "255", "hashOfConfig": "252"}, {"size": 298, "mtime": 1749582034461, "results": "256", "hashOfConfig": "252"}, {"size": 1352, "mtime": 1749644215638, "results": "257", "hashOfConfig": "252"}, {"size": 152, "mtime": 1749582034466, "results": "258", "hashOfConfig": "252"}, {"size": 163, "mtime": 1749582034467, "results": "259", "hashOfConfig": "252"}, {"filePath": "260", "messages": "261", "suppressedMessages": "262", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "1l9hcqn", {"filePath": "263", "messages": "264", "suppressedMessages": "265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 4, "source": null}, {"filePath": "266", "messages": "267", "suppressedMessages": "268", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "269", "messages": "270", "suppressedMessages": "271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "272", "messages": "273", "suppressedMessages": "274", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "275", "messages": "276", "suppressedMessages": "277", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "278", "messages": "279", "suppressedMessages": "280", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "281", "messages": "282", "suppressedMessages": "283", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "284", "messages": "285", "suppressedMessages": "286", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "287", "messages": "288", "suppressedMessages": "289", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "290", "messages": "291", "suppressedMessages": "292", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "293", "messages": "294", "suppressedMessages": "295", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "296", "messages": "297", "suppressedMessages": "298", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "299", "messages": "300", "suppressedMessages": "301", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "302", "messages": "303", "suppressedMessages": "304", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "305", "messages": "306", "suppressedMessages": "307", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "308", "messages": "309", "suppressedMessages": "310", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 18, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "311", "messages": "312", "suppressedMessages": "313", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "314", "messages": "315", "suppressedMessages": "316", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "317", "messages": "318", "suppressedMessages": "319", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "320", "messages": "321", "suppressedMessages": "322", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "323", "messages": "324", "suppressedMessages": "325", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "326", "messages": "327", "suppressedMessages": "328", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "329", "messages": "330", "suppressedMessages": "331", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "332", "messages": "333", "suppressedMessages": "334", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "335", "messages": "336", "suppressedMessages": "337", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "338", "messages": "339", "suppressedMessages": "340", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "341", "messages": "342", "suppressedMessages": "343", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "344", "messages": "345", "suppressedMessages": "346", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "347", "messages": "348", "suppressedMessages": "349", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "350", "messages": "351", "suppressedMessages": "352", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "353", "messages": "354", "suppressedMessages": "355", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "356", "messages": "357", "suppressedMessages": "358", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "359", "messages": "360", "suppressedMessages": "361", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "362", "messages": "363", "suppressedMessages": "364", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "365", "messages": "366", "suppressedMessages": "367", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "368", "messages": "369", "suppressedMessages": "370", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "371", "messages": "372", "suppressedMessages": "373", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "374", "messages": "375", "suppressedMessages": "376", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "377", "messages": "378", "suppressedMessages": "379", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "380", "messages": "381", "suppressedMessages": "382", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "383", "messages": "384", "suppressedMessages": "385", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "386", "messages": "387", "suppressedMessages": "388", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "389", "messages": "390", "suppressedMessages": "391", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "392", "messages": "393", "suppressedMessages": "394", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "395", "messages": "396", "suppressedMessages": "397", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "398", "messages": "399", "suppressedMessages": "400", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "401", "messages": "402", "suppressedMessages": "403", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "404", "messages": "405", "suppressedMessages": "406", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 15, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "407", "messages": "408", "suppressedMessages": "409", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "410", "messages": "411", "suppressedMessages": "412", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "413", "messages": "414", "suppressedMessages": "415", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 2, "source": null}, {"filePath": "416", "messages": "417", "suppressedMessages": "418", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "419", "messages": "420", "suppressedMessages": "421", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "422", "messages": "423", "suppressedMessages": "424", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "425", "messages": "426", "suppressedMessages": "427", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "428", "messages": "429", "suppressedMessages": "430", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "431", "messages": "432", "suppressedMessages": "433", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 3, "source": null}, {"filePath": "434", "messages": "435", "suppressedMessages": "436", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "437", "messages": "438", "suppressedMessages": "439", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "440", "messages": "441", "suppressedMessages": "442", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "443", "messages": "444", "suppressedMessages": "445", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "446", "messages": "447", "suppressedMessages": "448", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 2, "source": null}, {"filePath": "449", "messages": "450", "suppressedMessages": "451", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 26, "fixableErrorCount": 0, "fixableWarningCount": 4, "source": null}, {"filePath": "452", "messages": "453", "suppressedMessages": "454", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "455", "messages": "456", "suppressedMessages": "457", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 2, "source": null}, {"filePath": "458", "messages": "459", "suppressedMessages": "460", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 23, "fixableErrorCount": 0, "fixableWarningCount": 3, "source": null}, {"filePath": "461", "messages": "462", "suppressedMessages": "463", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 2, "source": null}, {"filePath": "464", "messages": "465", "suppressedMessages": "466", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "467", "messages": "468", "suppressedMessages": "469", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "470", "messages": "471", "suppressedMessages": "472", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "473", "messages": "474", "suppressedMessages": "475", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "476", "messages": "477", "suppressedMessages": "478", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "479", "messages": "480", "suppressedMessages": "481", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 23, "fixableErrorCount": 0, "fixableWarningCount": 4, "source": null}, {"filePath": "482", "messages": "483", "suppressedMessages": "484", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 19, "fixableErrorCount": 0, "fixableWarningCount": 2, "source": null}, {"filePath": "485", "messages": "486", "suppressedMessages": "487", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "488", "messages": "489", "suppressedMessages": "490", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 2, "source": null}, {"filePath": "491", "messages": "492", "suppressedMessages": "493", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "494", "messages": "495", "suppressedMessages": "496", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "497", "messages": "498", "suppressedMessages": "499", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "500", "messages": "501", "suppressedMessages": "502", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "503", "messages": "504", "suppressedMessages": "505", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "506", "messages": "507", "suppressedMessages": "508", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "509", "messages": "510", "suppressedMessages": "511", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "512", "messages": "513", "suppressedMessages": "514", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 2, "source": null}, {"filePath": "515", "messages": "516", "suppressedMessages": "517", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "518", "messages": "519", "suppressedMessages": "520", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 2, "source": null}, {"filePath": "521", "messages": "522", "suppressedMessages": "523", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 49, "fixableErrorCount": 0, "fixableWarningCount": 4, "source": null}, {"filePath": "524", "messages": "525", "suppressedMessages": "526", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "527", "messages": "528", "suppressedMessages": "529", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "530", "messages": "531", "suppressedMessages": "532", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "533", "messages": "534", "suppressedMessages": "535", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "536", "messages": "537", "suppressedMessages": "538", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "539", "messages": "540", "suppressedMessages": "541", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "542", "messages": "543", "suppressedMessages": "544", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "545", "messages": "546", "suppressedMessages": "547", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "548", "messages": "549", "suppressedMessages": "550", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "551", "messages": "552", "suppressedMessages": "553", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "554", "messages": "555", "suppressedMessages": "556", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "557", "messages": "558", "suppressedMessages": "559", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "560", "messages": "561", "suppressedMessages": "562", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "563", "messages": "564", "suppressedMessages": "565", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "566", "messages": "567", "suppressedMessages": "568", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "569", "messages": "570", "suppressedMessages": "571", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "572", "messages": "573", "suppressedMessages": "574", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "575", "messages": "576", "suppressedMessages": "577", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "578", "messages": "579", "suppressedMessages": "580", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "581", "messages": "582", "suppressedMessages": "583", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "584", "messages": "585", "suppressedMessages": "586", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "587", "messages": "588", "suppressedMessages": "589", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "590", "messages": "591", "suppressedMessages": "592", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "593", "messages": "594", "suppressedMessages": "595", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "596", "messages": "597", "suppressedMessages": "598", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "599", "messages": "600", "suppressedMessages": "601", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "602", "messages": "603", "suppressedMessages": "604", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "605", "messages": "606", "suppressedMessages": "607", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "608", "messages": "609", "suppressedMessages": "610", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "611", "messages": "612", "suppressedMessages": "613", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "614", "messages": "615", "suppressedMessages": "616", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "617", "messages": "618", "suppressedMessages": "619", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "620", "messages": "621", "suppressedMessages": "622", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "623", "messages": "624", "suppressedMessages": "625", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "6ya6ut", {"filePath": "626", "messages": "627", "suppressedMessages": "628", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1j4h9xt", {"filePath": "629", "messages": "630", "suppressedMessages": "631", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "632", "messages": "633", "suppressedMessages": "634", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "635", "messages": "636", "suppressedMessages": "637", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "638", "messages": "639", "suppressedMessages": "640", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "641", "messages": "642", "suppressedMessages": "643", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Documents/GitHub/ponyclub-v0/app/[locale]/error.tsx", ["644"], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/app/[locale]/for-schools/page.tsx", ["645", "646", "647", "648"], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/app/[locale]/kayak-rafting/page.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/app/[locale]/kayaking/page.tsx", ["649", "650", "651", "652", "653", "654"], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/app/[locale]/layout.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/app/[locale]/not-found.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/app/[locale]/page.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/app/[locale]/rafting/page.tsx", ["655", "656", "657", "658", "659", "660"], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/app/[locale]/riding/page.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/app/[locale]/river-village/page.tsx", ["661", "662"], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/app/[locale]/test-footer/page.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/app/[locale]/trekking/page.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/app/api/csp-violations/route.ts", ["663", "664"], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/app/global-error.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/app/sitemap.ts", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ActivityPageLayout.tsx", ["665", "666", "667", "668"], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/BokunWidget.tsx", ["669", "670", "671", "672", "673", "674", "675", "676", "677", "678", "679", "680", "681", "682", "683", "684", "685", "686"], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ClientLayout.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/DynamicBokunWidget.tsx", ["687"], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/DynamicContactDetails.tsx", ["688"], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/DynamicGallery.tsx", ["689"], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/DynamicGoogleMap.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/EnhancedPackageCard.tsx", ["690", "691", "692", "693"], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/Footer.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/HomePageContent.tsx", ["694"], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/PageLayout.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/SummerProgramCard.tsx", ["695"], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/activities-menu.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/client/BokunStyles.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/client/BookingButton.tsx", ["696", "697", "698"], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/client/GDPRBanner.tsx", ["699", "700"], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/client/GDPRGoogleAnalytics.tsx", ["701", "702", "703", "704", "705"], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/client/GoogleReviews.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/client/PriceListButton.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/contact-details-enhanced.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/desktop-menu.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/hamburger-menu.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/hero/HeroSection.tsx", ["706", "707"], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/language-selector.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/responsive-navigation.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/site-header.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/standalone-menu-items.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/theme-provider.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/Container.tsx", ["708"], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/Gallery.tsx", ["709"], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/OptimizedImage.tsx", ["710", "711"], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/PriceListPopup.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/accordion.tsx", ["712"], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/alert-dialog.tsx", ["713", "714", "715", "716", "717", "718", "719", "720", "721", "722", "723", "724", "725", "726", "727"], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/alert.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/aspect-ratio.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/avatar.tsx", ["728", "729", "730"], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/badge.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/border-beam.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/breadcrumb.tsx", ["731"], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/button.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/calendar.tsx", ["732"], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/card.tsx", ["733", "734", "735"], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/carousel.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/chart.tsx", ["736", "737", "738", "739"], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/checkbox.tsx", ["740"], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/collapsible.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/command.tsx", ["741", "742"], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/context-menu.tsx", ["743", "744", "745", "746", "747", "748", "749", "750", "751", "752", "753", "754", "755", "756", "757", "758", "759", "760", "761", "762", "763", "764", "765", "766", "767", "768"], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/dialog.tsx", ["769", "770", "771", "772", "773", "774", "775", "776", "777", "778", "779", "780", "781", "782"], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/drawer.tsx", ["783", "784"], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/dropdown-menu.tsx", ["785", "786", "787", "788", "789", "790", "791", "792", "793", "794", "795", "796", "797", "798", "799", "800", "801", "802", "803", "804", "805", "806", "807"], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/form.tsx", ["808", "809"], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/grid-pattern.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/hover-card.tsx", ["810", "811", "812", "813", "814", "815", "816", "817", "818", "819"], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/input-otp.tsx", ["820", "821"], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/input.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/label.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/menubar.tsx", ["822", "823", "824", "825", "826", "827", "828", "829", "830", "831", "832", "833", "834", "835", "836", "837", "838", "839", "840", "841", "842", "843", "844"], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/navigation-menu.tsx", ["845", "846", "847", "848", "849", "850", "851", "852", "853", "854", "855", "856", "857", "858", "859", "860", "861", "862", "863"], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/number-ticker.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/pagination.tsx", ["864", "865"], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/popover.tsx", ["866", "867", "868", "869", "870", "871", "872", "873", "874", "875"], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/progress.tsx", ["876"], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/pulsating-button.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/radio-group.tsx", ["877"], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/resizable.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/script-loader.tsx", ["878"], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/scroll-area.tsx", ["879"], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/select.tsx", ["880", "881", "882", "883", "884", "885", "886", "887", "888", "889", "890", "891"], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/separator.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/sheet.tsx", ["892", "893", "894", "895", "896", "897", "898", "899"], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/sidebar.tsx", ["900", "901", "902", "903", "904", "905", "906", "907", "908", "909", "910", "911", "912", "913", "914", "915", "916", "917", "918", "919", "920", "921", "922", "923", "924", "925", "926", "927", "928", "929", "930", "931", "932", "933", "934", "935", "936", "937", "938", "939", "940", "941", "942", "943", "944", "945", "946", "947", "948"], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/skeleton.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/slider.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/sonner.tsx", ["949"], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/switch.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/table.tsx", ["950"], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/tabs.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/textarea.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/toast.tsx", ["951", "952", "953", "954", "955", "956", "957"], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/toaster.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/toggle-group.tsx", ["958"], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/toggle.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/tooltip.tsx", ["959", "960", "961", "962", "963", "964", "965", "966", "967", "968"], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/use-mobile.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/use-toast.ts", ["969"], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/contexts/gdpr-context.tsx", ["970", "971"], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/contexts/language-context.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/hooks/use-mobile.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/hooks/use-toast.ts", ["972"], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/lib/bokun-lang.ts", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/lib/gallery-data/index.ts", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/lib/gallery-data/kayaking.ts", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/lib/gallery-data/rafting.ts", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/lib/gallery-data/riding.ts", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/lib/gallery-data/river-village.ts", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/lib/gallery-data/trekking.ts", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/lib/image-optimization.ts", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/lib/sitemap-data.ts", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/lib/translations/el.ts", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/lib/translations/en.ts", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/lib/translations/index.ts", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/lib/use-bokun-language.ts", ["973"], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/lib/utils.ts", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/middleware.ts", ["974"], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/next-env.d.ts", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/scripts/generate-sitemap-data.js", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/types/bokun.d.ts", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/types/esbuild.d.ts", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/types/global.d.ts", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/types/pg-protocol.d.ts", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/types/vite.d.ts", [], [], {"ruleId": "975", "severity": 1, "message": "976", "line": 21, "column": 7, "nodeType": "977", "messageId": "978", "endLine": 21, "endColumn": 20, "suggestions": "979"}, {"ruleId": "980", "severity": 1, "message": "981", "line": 640, "column": 46, "nodeType": null, "endLine": 640, "endColumn": 83, "fix": "982"}, {"ruleId": "980", "severity": 1, "message": "981", "line": 686, "column": 46, "nodeType": null, "endLine": 686, "endColumn": 83, "fix": "983"}, {"ruleId": "980", "severity": 1, "message": "984", "line": 715, "column": 42, "nodeType": null, "endLine": 715, "endColumn": 85, "fix": "985"}, {"ruleId": "980", "severity": 1, "message": "986", "line": 930, "column": 69, "nodeType": null, "endLine": 930, "endColumn": 98, "fix": "987"}, {"ruleId": "988", "severity": 1, "message": "989", "line": 46, "column": 23, "nodeType": null, "endLine": 46, "endColumn": 28}, {"ruleId": "988", "severity": 1, "message": "990", "line": 46, "column": 29, "nodeType": null, "endLine": 46, "endColumn": 37}, {"ruleId": "988", "severity": 1, "message": "991", "line": 47, "column": 23, "nodeType": null, "endLine": 47, "endColumn": 27}, {"ruleId": "988", "severity": 1, "message": "989", "line": 76, "column": 23, "nodeType": null, "endLine": 76, "endColumn": 28}, {"ruleId": "988", "severity": 1, "message": "990", "line": 76, "column": 29, "nodeType": null, "endLine": 76, "endColumn": 37}, {"ruleId": "988", "severity": 1, "message": "991", "line": 77, "column": 23, "nodeType": null, "endLine": 77, "endColumn": 27}, {"ruleId": "988", "severity": 1, "message": "989", "line": 46, "column": 23, "nodeType": null, "endLine": 46, "endColumn": 28}, {"ruleId": "988", "severity": 1, "message": "990", "line": 46, "column": 29, "nodeType": null, "endLine": 46, "endColumn": 37}, {"ruleId": "988", "severity": 1, "message": "991", "line": 47, "column": 23, "nodeType": null, "endLine": 47, "endColumn": 27}, {"ruleId": "988", "severity": 1, "message": "989", "line": 74, "column": 23, "nodeType": null, "endLine": 74, "endColumn": 28}, {"ruleId": "988", "severity": 1, "message": "990", "line": 74, "column": 29, "nodeType": null, "endLine": 74, "endColumn": 37}, {"ruleId": "988", "severity": 1, "message": "991", "line": 75, "column": 23, "nodeType": null, "endLine": 75, "endColumn": 27}, {"ruleId": "988", "severity": 1, "message": "989", "line": 77, "column": 20, "nodeType": null, "endLine": 77, "endColumn": 25}, {"ruleId": "988", "severity": 1, "message": "989", "line": 140, "column": 23, "nodeType": null, "endLine": 140, "endColumn": 28}, {"ruleId": "975", "severity": 1, "message": "976", "line": 8, "column": 7, "nodeType": "977", "messageId": "978", "endLine": 8, "endColumn": 19, "suggestions": "992"}, {"ruleId": "975", "severity": 1, "message": "976", "line": 16, "column": 7, "nodeType": "977", "messageId": "978", "endLine": 16, "endColumn": 20, "suggestions": "993"}, {"ruleId": "988", "severity": 1, "message": "989", "line": 67, "column": 19, "nodeType": null, "endLine": 67, "endColumn": 24}, {"ruleId": "988", "severity": 1, "message": "989", "line": 109, "column": 19, "nodeType": null, "endLine": 109, "endColumn": 24}, {"ruleId": "988", "severity": 1, "message": "989", "line": 151, "column": 19, "nodeType": null, "endLine": 151, "endColumn": 24}, {"ruleId": "988", "severity": 1, "message": "989", "line": 178, "column": 19, "nodeType": null, "endLine": 178, "endColumn": 24}, {"ruleId": "975", "severity": 1, "message": "976", "line": 40, "column": 9, "nodeType": "977", "messageId": "978", "endLine": 40, "endColumn": 20, "suggestions": "994"}, {"ruleId": "975", "severity": 1, "message": "976", "line": 46, "column": 7, "nodeType": "977", "messageId": "978", "endLine": 46, "endColumn": 18, "suggestions": "995"}, {"ruleId": "975", "severity": 1, "message": "976", "line": 59, "column": 11, "nodeType": "977", "messageId": "978", "endLine": 59, "endColumn": 22, "suggestions": "996"}, {"ruleId": "975", "severity": 1, "message": "976", "line": 65, "column": 11, "nodeType": "977", "messageId": "978", "endLine": 65, "endColumn": 22, "suggestions": "997"}, {"ruleId": "975", "severity": 1, "message": "976", "line": 70, "column": 11, "nodeType": "977", "messageId": "978", "endLine": 70, "endColumn": 22, "suggestions": "998"}, {"ruleId": "975", "severity": 1, "message": "976", "line": 92, "column": 9, "nodeType": "977", "messageId": "978", "endLine": 92, "endColumn": 20, "suggestions": "999"}, {"ruleId": "975", "severity": 1, "message": "976", "line": 112, "column": 11, "nodeType": "977", "messageId": "978", "endLine": 112, "endColumn": 22, "suggestions": "1000"}, {"ruleId": "975", "severity": 1, "message": "976", "line": 118, "column": 13, "nodeType": "977", "messageId": "978", "endLine": 118, "endColumn": 24, "suggestions": "1001"}, {"ruleId": "975", "severity": 1, "message": "976", "line": 122, "column": 13, "nodeType": "977", "messageId": "978", "endLine": 122, "endColumn": 26, "suggestions": "1002"}, {"ruleId": "975", "severity": 1, "message": "976", "line": 128, "column": 11, "nodeType": "977", "messageId": "978", "endLine": 128, "endColumn": 22, "suggestions": "1003"}, {"ruleId": "975", "severity": 1, "message": "976", "line": 132, "column": 9, "nodeType": "977", "messageId": "978", "endLine": 132, "endColumn": 20, "suggestions": "1004"}, {"ruleId": "975", "severity": 1, "message": "976", "line": 137, "column": 7, "nodeType": "977", "messageId": "978", "endLine": 137, "endColumn": 18, "suggestions": "1005"}, {"ruleId": "975", "severity": 1, "message": "976", "line": 147, "column": 11, "nodeType": "977", "messageId": "978", "endLine": 147, "endColumn": 22, "suggestions": "1006"}, {"ruleId": "975", "severity": 1, "message": "976", "line": 153, "column": 13, "nodeType": "977", "messageId": "978", "endLine": 153, "endColumn": 24, "suggestions": "1007"}, {"ruleId": "975", "severity": 1, "message": "976", "line": 159, "column": 13, "nodeType": "977", "messageId": "978", "endLine": 159, "endColumn": 26, "suggestions": "1008"}, {"ruleId": "975", "severity": 1, "message": "976", "line": 167, "column": 7, "nodeType": "977", "messageId": "978", "endLine": 167, "endColumn": 18, "suggestions": "1009"}, {"ruleId": "975", "severity": 1, "message": "976", "line": 175, "column": 9, "nodeType": "977", "messageId": "978", "endLine": 175, "endColumn": 20, "suggestions": "1010"}, {"ruleId": "988", "severity": 1, "message": "1011", "line": 189, "column": 20, "nodeType": null, "endLine": 189, "endColumn": 31}, {"ruleId": "980", "severity": 1, "message": "1012", "line": 9, "column": 34, "nodeType": null, "endLine": 9, "endColumn": 84, "fix": "1013"}, {"ruleId": "980", "severity": 1, "message": "1014", "line": 9, "column": 34, "nodeType": null, "endLine": 9, "endColumn": 84, "fix": "1015"}, {"ruleId": "980", "severity": 1, "message": "1012", "line": 9, "column": 34, "nodeType": null, "endLine": 9, "endColumn": 84, "fix": "1016"}, {"ruleId": "988", "severity": 1, "message": "1017", "line": 161, "column": 15, "nodeType": null, "endLine": 161, "endColumn": 26}, {"ruleId": "988", "severity": 1, "message": "1018", "line": 166, "column": 29, "nodeType": null, "endLine": 166, "endColumn": 40}, {"ruleId": "988", "severity": 1, "message": "1019", "line": 176, "column": 29, "nodeType": null, "endLine": 176, "endColumn": 39}, {"ruleId": "988", "severity": 1, "message": "1020", "line": 186, "column": 29, "nodeType": null, "endLine": 186, "endColumn": 42}, {"ruleId": "980", "severity": 1, "message": "1021", "line": 66, "column": 27, "nodeType": null, "endLine": 66, "endColumn": 83, "fix": "1022"}, {"ruleId": "1023", "severity": 1, "message": "1024", "line": 102, "column": 1, "nodeType": "1025", "messageId": "1026", "endLine": 102, "endColumn": 159}, {"ruleId": "975", "severity": 1, "message": "976", "line": 104, "column": 9, "nodeType": "977", "messageId": "978", "endLine": 104, "endColumn": 21, "suggestions": "1027"}, {"ruleId": "975", "severity": 1, "message": "976", "line": 137, "column": 7, "nodeType": "977", "messageId": "978", "endLine": 137, "endColumn": 18, "suggestions": "1028"}, {"ruleId": "988", "severity": 1, "message": "1029", "line": 156, "column": 9, "nodeType": null, "endLine": 156, "endColumn": 20}, {"ruleId": "988", "severity": 1, "message": "1030", "line": 213, "column": 9, "nodeType": null, "endLine": 213, "endColumn": 19}, {"ruleId": "988", "severity": 1, "message": "1031", "line": 213, "column": 20, "nodeType": null, "endLine": 213, "endColumn": 40}, {"ruleId": "1032", "severity": 1, "message": "1033", "line": 6, "column": 19, "nodeType": null, "messageId": "1034", "endLine": 6, "endColumn": 32}, {"ruleId": "975", "severity": 1, "message": "976", "line": 24, "column": 7, "nodeType": "977", "messageId": "978", "endLine": 24, "endColumn": 18, "suggestions": "1035"}, {"ruleId": "975", "severity": 1, "message": "976", "line": 53, "column": 7, "nodeType": "977", "messageId": "978", "endLine": 53, "endColumn": 18, "suggestions": "1036"}, {"ruleId": "975", "severity": 1, "message": "976", "line": 65, "column": 11, "nodeType": "977", "messageId": "978", "endLine": 65, "endColumn": 22, "suggestions": "1037"}, {"ruleId": "975", "severity": 1, "message": "976", "line": 68, "column": 11, "nodeType": "977", "messageId": "978", "endLine": 68, "endColumn": 24, "suggestions": "1038"}, {"ruleId": "1032", "severity": 1, "message": "1039", "line": 4, "column": 29, "nodeType": null, "messageId": "1034", "endLine": 4, "endColumn": 37}, {"ruleId": "980", "severity": 1, "message": "1040", "line": 113, "column": 53, "nodeType": null, "endLine": 113, "endColumn": 105, "fix": "1041"}, {"ruleId": "1032", "severity": 1, "message": "1042", "line": 2, "column": 17, "nodeType": null, "messageId": "1034", "endLine": 2, "endColumn": 26}, {"ruleId": "980", "severity": 1, "message": "1043", "line": 63, "column": 89, "nodeType": null, "endLine": 63, "endColumn": 103, "fix": "1044"}, {"ruleId": "1045", "severity": 1, "message": "1046", "line": 22, "column": 8, "nodeType": "1047", "messageId": "1048", "endLine": 111, "endColumn": 2}, {"ruleId": "1049", "severity": 1, "message": "1050", "line": 38, "column": 66, "nodeType": "1051", "messageId": "1052", "endLine": 38, "endColumn": 69, "suggestions": "1053"}, {"ruleId": "980", "severity": 1, "message": "1054", "line": 38, "column": 31, "nodeType": null, "endLine": 38, "endColumn": 83, "fix": "1055"}, {"ruleId": "988", "severity": 1, "message": "1056", "line": 22, "column": 9, "nodeType": null, "endLine": 22, "endColumn": 37}, {"ruleId": "988", "severity": 1, "message": "1057", "line": 22, "column": 38, "nodeType": null, "endLine": 22, "endColumn": 65}, {"ruleId": "988", "severity": 1, "message": "1058", "line": 23, "column": 9, "nodeType": null, "endLine": 23, "endColumn": 40}, {"ruleId": "988", "severity": 1, "message": "1059", "line": 23, "column": 41, "nodeType": null, "endLine": 23, "endColumn": 71}, {"ruleId": "988", "severity": 1, "message": "1056", "line": 44, "column": 11, "nodeType": null, "endLine": 44, "endColumn": 39}, {"ruleId": "988", "severity": 1, "message": "1057", "line": 44, "column": 40, "nodeType": null, "endLine": 44, "endColumn": 67}, {"ruleId": "988", "severity": 1, "message": "1060", "line": 45, "column": 11, "nodeType": null, "endLine": 45, "endColumn": 39}, {"ruleId": "988", "severity": 1, "message": "1061", "line": 45, "column": 40, "nodeType": null, "endLine": 45, "endColumn": 80}, {"ruleId": "988", "severity": 1, "message": "1062", "line": 46, "column": 11, "nodeType": null, "endLine": 46, "endColumn": 52}, {"ruleId": "988", "severity": 1, "message": "1058", "line": 47, "column": 11, "nodeType": null, "endLine": 47, "endColumn": 42}, {"ruleId": "988", "severity": 1, "message": "1059", "line": 47, "column": 43, "nodeType": null, "endLine": 47, "endColumn": 73}, {"ruleId": "988", "severity": 1, "message": "1063", "line": 48, "column": 11, "nodeType": null, "endLine": 48, "endColumn": 42}, {"ruleId": "988", "severity": 1, "message": "1064", "line": 49, "column": 11, "nodeType": null, "endLine": 49, "endColumn": 52}, {"ruleId": "988", "severity": 1, "message": "1065", "line": 50, "column": 11, "nodeType": null, "endLine": 50, "endColumn": 53}, {"ruleId": "980", "severity": 1, "message": "1066", "line": 104, "column": 61, "nodeType": null, "endLine": 104, "endColumn": 92, "fix": "1067"}, {"ruleId": "980", "severity": 1, "message": "1068", "line": 17, "column": 19, "nodeType": null, "endLine": 17, "endColumn": 82, "fix": "1069"}, {"ruleId": "1023", "severity": 1, "message": "1070", "line": 28, "column": 1, "nodeType": "1025", "messageId": "1026", "endLine": 28, "endColumn": 147}, {"ruleId": "980", "severity": 1, "message": "1071", "line": 48, "column": 19, "nodeType": null, "endLine": 48, "endColumn": 89, "fix": "1072"}, {"ruleId": "980", "severity": 1, "message": "1073", "line": 79, "column": 60, "nodeType": null, "endLine": 79, "endColumn": 89, "fix": "1074"}, {"ruleId": "1032", "severity": 1, "message": "1075", "line": 6, "column": 26, "nodeType": null, "messageId": "1034", "endLine": 6, "endColumn": 42}, {"ruleId": "980", "severity": 1, "message": "1076", "line": 6, "column": 32, "nodeType": null, "endLine": 6, "endColumn": 90, "fix": "1077"}, {"ruleId": "980", "severity": 1, "message": "1078", "line": 19, "column": 34, "nodeType": null, "endLine": 19, "endColumn": 86, "fix": "1079"}, {"ruleId": "980", "severity": 1, "message": "1080", "line": 37, "column": 66, "nodeType": null, "endLine": 37, "endColumn": 94, "fix": "1081"}, {"ruleId": "1032", "severity": 1, "message": "1082", "line": 5, "column": 10, "nodeType": null, "messageId": "1034", "endLine": 5, "endColumn": 18}, {"ruleId": "1032", "severity": 1, "message": "1083", "line": 5, "column": 20, "nodeType": null, "messageId": "1034", "endLine": 5, "endColumn": 27}, {"ruleId": "1032", "severity": 1, "message": "1084", "line": 5, "column": 29, "nodeType": null, "messageId": "1034", "endLine": 5, "endColumn": 38}, {"ruleId": "1045", "severity": 1, "message": "1085", "line": 192, "column": 24, "nodeType": "1086", "messageId": "1048", "endLine": 262, "endColumn": 12}, {"ruleId": "980", "severity": 1, "message": "1087", "line": 29, "column": 48, "nodeType": null, "endLine": 29, "endColumn": 95, "fix": "1088"}, {"ruleId": "980", "severity": 1, "message": "1089", "line": 91, "column": 64, "nodeType": null, "endLine": 91, "endColumn": 90, "fix": "1090"}, {"ruleId": "980", "severity": 1, "message": "1091", "line": 148, "column": 30, "nodeType": null, "endLine": 148, "endColumn": 85, "fix": "1092"}, {"ruleId": "988", "severity": 1, "message": "1056", "line": 55, "column": 9, "nodeType": null, "endLine": 55, "endColumn": 37}, {"ruleId": "988", "severity": 1, "message": "1057", "line": 55, "column": 38, "nodeType": null, "endLine": 55, "endColumn": 65}, {"ruleId": "988", "severity": 1, "message": "1060", "line": 56, "column": 9, "nodeType": null, "endLine": 56, "endColumn": 37}, {"ruleId": "988", "severity": 1, "message": "1058", "line": 57, "column": 9, "nodeType": null, "endLine": 57, "endColumn": 40}, {"ruleId": "988", "severity": 1, "message": "1059", "line": 57, "column": 41, "nodeType": null, "endLine": 57, "endColumn": 71}, {"ruleId": "988", "severity": 1, "message": "1063", "line": 58, "column": 9, "nodeType": null, "endLine": 58, "endColumn": 40}, {"ruleId": "988", "severity": 1, "message": "1093", "line": 59, "column": 9, "nodeType": null, "endLine": 59, "endColumn": 47}, {"ruleId": "988", "severity": 1, "message": "1094", "line": 60, "column": 9, "nodeType": null, "endLine": 60, "endColumn": 47}, {"ruleId": "988", "severity": 1, "message": "1095", "line": 61, "column": 9, "nodeType": null, "endLine": 61, "endColumn": 47}, {"ruleId": "988", "severity": 1, "message": "1096", "line": 62, "column": 9, "nodeType": null, "endLine": 62, "endColumn": 47}, {"ruleId": "988", "severity": 1, "message": "1030", "line": 82, "column": 11, "nodeType": null, "endLine": 82, "endColumn": 21}, {"ruleId": "988", "severity": 1, "message": "1097", "line": 82, "column": 22, "nodeType": null, "endLine": 82, "endColumn": 32}, {"ruleId": "988", "severity": 1, "message": "1056", "line": 84, "column": 11, "nodeType": null, "endLine": 84, "endColumn": 39}, {"ruleId": "988", "severity": 1, "message": "1057", "line": 84, "column": 40, "nodeType": null, "endLine": 84, "endColumn": 67}, {"ruleId": "988", "severity": 1, "message": "1060", "line": 85, "column": 11, "nodeType": null, "endLine": 85, "endColumn": 39}, {"ruleId": "988", "severity": 1, "message": "1058", "line": 86, "column": 11, "nodeType": null, "endLine": 86, "endColumn": 42}, {"ruleId": "988", "severity": 1, "message": "1059", "line": 86, "column": 43, "nodeType": null, "endLine": 86, "endColumn": 73}, {"ruleId": "988", "severity": 1, "message": "1063", "line": 87, "column": 11, "nodeType": null, "endLine": 87, "endColumn": 42}, {"ruleId": "988", "severity": 1, "message": "1093", "line": 88, "column": 11, "nodeType": null, "endLine": 88, "endColumn": 49}, {"ruleId": "988", "severity": 1, "message": "1094", "line": 89, "column": 11, "nodeType": null, "endLine": 89, "endColumn": 49}, {"ruleId": "988", "severity": 1, "message": "1095", "line": 90, "column": 11, "nodeType": null, "endLine": 90, "endColumn": 49}, {"ruleId": "988", "severity": 1, "message": "1096", "line": 91, "column": 11, "nodeType": null, "endLine": 91, "endColumn": 49}, {"ruleId": "980", "severity": 1, "message": "1098", "line": 142, "column": 22, "nodeType": null, "endLine": 142, "endColumn": 84, "fix": "1099"}, {"ruleId": "980", "severity": 1, "message": "1098", "line": 169, "column": 22, "nodeType": null, "endLine": 169, "endColumn": 84, "fix": "1100"}, {"ruleId": "980", "severity": 1, "message": "1101", "line": 197, "column": 59, "nodeType": null, "endLine": 197, "endColumn": 86, "fix": "1102"}, {"ruleId": "980", "severity": 1, "message": "1091", "line": 202, "column": 30, "nodeType": null, "endLine": 202, "endColumn": 85, "fix": "1103"}, {"ruleId": "988", "severity": 1, "message": "1056", "line": 25, "column": 9, "nodeType": null, "endLine": 25, "endColumn": 37}, {"ruleId": "988", "severity": 1, "message": "1057", "line": 25, "column": 38, "nodeType": null, "endLine": 25, "endColumn": 65}, {"ruleId": "988", "severity": 1, "message": "1058", "line": 26, "column": 9, "nodeType": null, "endLine": 26, "endColumn": 40}, {"ruleId": "988", "severity": 1, "message": "1059", "line": 26, "column": 41, "nodeType": null, "endLine": 26, "endColumn": 71}, {"ruleId": "988", "severity": 1, "message": "1056", "line": 46, "column": 11, "nodeType": null, "endLine": 46, "endColumn": 39}, {"ruleId": "988", "severity": 1, "message": "1057", "line": 46, "column": 40, "nodeType": null, "endLine": 46, "endColumn": 67}, {"ruleId": "988", "severity": 1, "message": "1060", "line": 47, "column": 11, "nodeType": null, "endLine": 47, "endColumn": 39}, {"ruleId": "988", "severity": 1, "message": "1061", "line": 47, "column": 40, "nodeType": null, "endLine": 47, "endColumn": 80}, {"ruleId": "988", "severity": 1, "message": "1062", "line": 48, "column": 11, "nodeType": null, "endLine": 48, "endColumn": 52}, {"ruleId": "988", "severity": 1, "message": "1058", "line": 49, "column": 11, "nodeType": null, "endLine": 49, "endColumn": 42}, {"ruleId": "988", "severity": 1, "message": "1059", "line": 49, "column": 43, "nodeType": null, "endLine": 49, "endColumn": 73}, {"ruleId": "988", "severity": 1, "message": "1063", "line": 50, "column": 11, "nodeType": null, "endLine": 50, "endColumn": 42}, {"ruleId": "988", "severity": 1, "message": "1064", "line": 51, "column": 11, "nodeType": null, "endLine": 51, "endColumn": 52}, {"ruleId": "988", "severity": 1, "message": "1065", "line": 52, "column": 11, "nodeType": null, "endLine": 52, "endColumn": 53}, {"ruleId": "980", "severity": 1, "message": "1104", "line": 23, "column": 52, "nodeType": null, "endLine": 23, "endColumn": 84, "fix": "1105"}, {"ruleId": "980", "severity": 1, "message": "1066", "line": 86, "column": 56, "nodeType": null, "endLine": 86, "endColumn": 87, "fix": "1106"}, {"ruleId": "988", "severity": 1, "message": "1056", "line": 56, "column": 9, "nodeType": null, "endLine": 56, "endColumn": 37}, {"ruleId": "988", "severity": 1, "message": "1057", "line": 56, "column": 38, "nodeType": null, "endLine": 56, "endColumn": 65}, {"ruleId": "988", "severity": 1, "message": "1060", "line": 57, "column": 9, "nodeType": null, "endLine": 57, "endColumn": 37}, {"ruleId": "988", "severity": 1, "message": "1058", "line": 58, "column": 9, "nodeType": null, "endLine": 58, "endColumn": 40}, {"ruleId": "988", "severity": 1, "message": "1059", "line": 58, "column": 41, "nodeType": null, "endLine": 58, "endColumn": 71}, {"ruleId": "988", "severity": 1, "message": "1063", "line": 59, "column": 9, "nodeType": null, "endLine": 59, "endColumn": 40}, {"ruleId": "988", "severity": 1, "message": "1093", "line": 60, "column": 9, "nodeType": null, "endLine": 60, "endColumn": 47}, {"ruleId": "988", "severity": 1, "message": "1094", "line": 61, "column": 9, "nodeType": null, "endLine": 61, "endColumn": 47}, {"ruleId": "988", "severity": 1, "message": "1095", "line": 62, "column": 9, "nodeType": null, "endLine": 62, "endColumn": 47}, {"ruleId": "988", "severity": 1, "message": "1096", "line": 63, "column": 9, "nodeType": null, "endLine": 63, "endColumn": 47}, {"ruleId": "988", "severity": 1, "message": "1056", "line": 84, "column": 11, "nodeType": null, "endLine": 84, "endColumn": 39}, {"ruleId": "988", "severity": 1, "message": "1057", "line": 84, "column": 40, "nodeType": null, "endLine": 84, "endColumn": 67}, {"ruleId": "988", "severity": 1, "message": "1060", "line": 85, "column": 11, "nodeType": null, "endLine": 85, "endColumn": 39}, {"ruleId": "988", "severity": 1, "message": "1058", "line": 86, "column": 11, "nodeType": null, "endLine": 86, "endColumn": 42}, {"ruleId": "988", "severity": 1, "message": "1059", "line": 86, "column": 43, "nodeType": null, "endLine": 86, "endColumn": 73}, {"ruleId": "988", "severity": 1, "message": "1063", "line": 87, "column": 11, "nodeType": null, "endLine": 87, "endColumn": 42}, {"ruleId": "988", "severity": 1, "message": "1093", "line": 88, "column": 11, "nodeType": null, "endLine": 88, "endColumn": 49}, {"ruleId": "988", "severity": 1, "message": "1094", "line": 89, "column": 11, "nodeType": null, "endLine": 89, "endColumn": 49}, {"ruleId": "988", "severity": 1, "message": "1095", "line": 90, "column": 11, "nodeType": null, "endLine": 90, "endColumn": 49}, {"ruleId": "988", "severity": 1, "message": "1096", "line": 91, "column": 11, "nodeType": null, "endLine": 91, "endColumn": 49}, {"ruleId": "980", "severity": 1, "message": "1098", "line": 145, "column": 22, "nodeType": null, "endLine": 145, "endColumn": 84, "fix": "1107"}, {"ruleId": "980", "severity": 1, "message": "1098", "line": 172, "column": 22, "nodeType": null, "endLine": 172, "endColumn": 84, "fix": "1108"}, {"ruleId": "980", "severity": 1, "message": "1109", "line": 200, "column": 60, "nodeType": null, "endLine": 200, "endColumn": 86, "fix": "1110"}, {"ruleId": "980", "severity": 1, "message": "1111", "line": 109, "column": 62, "nodeType": null, "endLine": 109, "endColumn": 93, "fix": "1112"}, {"ruleId": "980", "severity": 1, "message": "1113", "line": 124, "column": 53, "nodeType": null, "endLine": 124, "endColumn": 91, "fix": "1114"}, {"ruleId": "988", "severity": 1, "message": "1056", "line": 22, "column": 9, "nodeType": null, "endLine": 22, "endColumn": 37}, {"ruleId": "988", "severity": 1, "message": "1057", "line": 22, "column": 38, "nodeType": null, "endLine": 22, "endColumn": 65}, {"ruleId": "988", "severity": 1, "message": "1060", "line": 23, "column": 9, "nodeType": null, "endLine": 23, "endColumn": 37}, {"ruleId": "988", "severity": 1, "message": "1058", "line": 24, "column": 9, "nodeType": null, "endLine": 24, "endColumn": 40}, {"ruleId": "988", "severity": 1, "message": "1059", "line": 24, "column": 41, "nodeType": null, "endLine": 24, "endColumn": 71}, {"ruleId": "988", "severity": 1, "message": "1063", "line": 25, "column": 9, "nodeType": null, "endLine": 25, "endColumn": 40}, {"ruleId": "988", "severity": 1, "message": "1093", "line": 26, "column": 9, "nodeType": null, "endLine": 26, "endColumn": 47}, {"ruleId": "988", "severity": 1, "message": "1094", "line": 27, "column": 9, "nodeType": null, "endLine": 27, "endColumn": 47}, {"ruleId": "988", "severity": 1, "message": "1095", "line": 28, "column": 9, "nodeType": null, "endLine": 28, "endColumn": 47}, {"ruleId": "988", "severity": 1, "message": "1096", "line": 29, "column": 9, "nodeType": null, "endLine": 29, "endColumn": 47}, {"ruleId": "980", "severity": 1, "message": "1115", "line": 28, "column": 66, "nodeType": null, "endLine": 28, "endColumn": 85, "fix": "1116"}, {"ruleId": "988", "severity": 1, "message": "1117", "line": 64, "column": 15, "nodeType": null, "endLine": 64, "endColumn": 34}, {"ruleId": "980", "severity": 1, "message": "1118", "line": 25, "column": 19, "nodeType": null, "endLine": 25, "endColumn": 89, "fix": "1119"}, {"ruleId": "988", "severity": 1, "message": "1056", "line": 85, "column": 9, "nodeType": null, "endLine": 85, "endColumn": 37}, {"ruleId": "988", "severity": 1, "message": "1057", "line": 85, "column": 38, "nodeType": null, "endLine": 85, "endColumn": 65}, {"ruleId": "988", "severity": 1, "message": "1060", "line": 86, "column": 9, "nodeType": null, "endLine": 86, "endColumn": 37}, {"ruleId": "988", "severity": 1, "message": "1058", "line": 87, "column": 9, "nodeType": null, "endLine": 87, "endColumn": 40}, {"ruleId": "988", "severity": 1, "message": "1059", "line": 87, "column": 41, "nodeType": null, "endLine": 87, "endColumn": 71}, {"ruleId": "988", "severity": 1, "message": "1063", "line": 88, "column": 9, "nodeType": null, "endLine": 88, "endColumn": 40}, {"ruleId": "988", "severity": 1, "message": "1093", "line": 89, "column": 9, "nodeType": null, "endLine": 89, "endColumn": 47}, {"ruleId": "988", "severity": 1, "message": "1094", "line": 90, "column": 9, "nodeType": null, "endLine": 90, "endColumn": 47}, {"ruleId": "988", "severity": 1, "message": "1095", "line": 91, "column": 9, "nodeType": null, "endLine": 91, "endColumn": 47}, {"ruleId": "988", "severity": 1, "message": "1096", "line": 92, "column": 9, "nodeType": null, "endLine": 92, "endColumn": 47}, {"ruleId": "988", "severity": 1, "message": "1056", "line": 115, "column": 11, "nodeType": null, "endLine": 115, "endColumn": 39}, {"ruleId": "988", "severity": 1, "message": "1057", "line": 115, "column": 40, "nodeType": null, "endLine": 115, "endColumn": 67}, {"ruleId": "988", "severity": 1, "message": "1060", "line": 116, "column": 11, "nodeType": null, "endLine": 116, "endColumn": 39}, {"ruleId": "988", "severity": 1, "message": "1059", "line": 117, "column": 11, "nodeType": null, "endLine": 117, "endColumn": 41}, {"ruleId": "988", "severity": 1, "message": "1063", "line": 117, "column": 42, "nodeType": null, "endLine": 117, "endColumn": 73}, {"ruleId": "988", "severity": 1, "message": "1093", "line": 118, "column": 11, "nodeType": null, "endLine": 118, "endColumn": 49}, {"ruleId": "988", "severity": 1, "message": "1094", "line": 119, "column": 11, "nodeType": null, "endLine": 119, "endColumn": 49}, {"ruleId": "988", "severity": 1, "message": "1095", "line": 120, "column": 11, "nodeType": null, "endLine": 120, "endColumn": 49}, {"ruleId": "988", "severity": 1, "message": "1096", "line": 121, "column": 11, "nodeType": null, "endLine": 121, "endColumn": 49}, {"ruleId": "980", "severity": 1, "message": "1098", "line": 174, "column": 22, "nodeType": null, "endLine": 174, "endColumn": 84, "fix": "1120"}, {"ruleId": "980", "severity": 1, "message": "1098", "line": 201, "column": 22, "nodeType": null, "endLine": 201, "endColumn": 84, "fix": "1121"}, {"ruleId": "980", "severity": 1, "message": "1091", "line": 234, "column": 30, "nodeType": null, "endLine": 234, "endColumn": 85, "fix": "1122"}, {"ruleId": "980", "severity": 1, "message": "1123", "line": 14, "column": 19, "nodeType": null, "endLine": 14, "endColumn": 84, "fix": "1124"}, {"ruleId": "980", "severity": 1, "message": "1125", "line": 29, "column": 19, "nodeType": null, "endLine": 29, "endColumn": 86, "fix": "1126"}, {"ruleId": "988", "severity": 1, "message": "1127", "line": 78, "column": 9, "nodeType": null, "endLine": 78, "endColumn": 40}, {"ruleId": "988", "severity": 1, "message": "1128", "line": 78, "column": 41, "nodeType": null, "endLine": 78, "endColumn": 69}, {"ruleId": "988", "severity": 1, "message": "1129", "line": 79, "column": 9, "nodeType": null, "endLine": 79, "endColumn": 39}, {"ruleId": "988", "severity": 1, "message": "1130", "line": 79, "column": 40, "nodeType": null, "endLine": 79, "endColumn": 67}, {"ruleId": "988", "severity": 1, "message": "1131", "line": 80, "column": 9, "nodeType": null, "endLine": 80, "endColumn": 54}, {"ruleId": "988", "severity": 1, "message": "1132", "line": 81, "column": 9, "nodeType": null, "endLine": 81, "endColumn": 55}, {"ruleId": "988", "severity": 1, "message": "1133", "line": 82, "column": 9, "nodeType": null, "endLine": 82, "endColumn": 51}, {"ruleId": "988", "severity": 1, "message": "1134", "line": 83, "column": 9, "nodeType": null, "endLine": 83, "endColumn": 52}, {"ruleId": "988", "severity": 1, "message": "1135", "line": 104, "column": 11, "nodeType": null, "endLine": 104, "endColumn": 28}, {"ruleId": "988", "severity": 1, "message": "1056", "line": 107, "column": 11, "nodeType": null, "endLine": 107, "endColumn": 39}, {"ruleId": "988", "severity": 1, "message": "1136", "line": 107, "column": 40, "nodeType": null, "endLine": 107, "endColumn": 68}, {"ruleId": "988", "severity": 1, "message": "1058", "line": 108, "column": 11, "nodeType": null, "endLine": 108, "endColumn": 42}, {"ruleId": "988", "severity": 1, "message": "1063", "line": 108, "column": 43, "nodeType": null, "endLine": 108, "endColumn": 74}, {"ruleId": "988", "severity": 1, "message": "1137", "line": 128, "column": 9, "nodeType": null, "endLine": 128, "endColumn": 40}, {"ruleId": "988", "severity": 1, "message": "1138", "line": 128, "column": 41, "nodeType": null, "endLine": 128, "endColumn": 69}, {"ruleId": "988", "severity": 1, "message": "1139", "line": 129, "column": 9, "nodeType": null, "endLine": 129, "endColumn": 40}, {"ruleId": "988", "severity": 1, "message": "1140", "line": 129, "column": 41, "nodeType": null, "endLine": 129, "endColumn": 69}, {"ruleId": "980", "severity": 1, "message": "1141", "line": 51, "column": 81, "nodeType": null, "endLine": 51, "endColumn": 95, "fix": "1142"}, {"ruleId": "980", "severity": 1, "message": "1143", "line": 59, "column": 77, "nodeType": null, "endLine": 59, "endColumn": 91, "fix": "1144"}, {"ruleId": "988", "severity": 1, "message": "1056", "line": 23, "column": 11, "nodeType": null, "endLine": 23, "endColumn": 39}, {"ruleId": "988", "severity": 1, "message": "1057", "line": 23, "column": 40, "nodeType": null, "endLine": 23, "endColumn": 67}, {"ruleId": "988", "severity": 1, "message": "1060", "line": 24, "column": 11, "nodeType": null, "endLine": 24, "endColumn": 39}, {"ruleId": "988", "severity": 1, "message": "1058", "line": 25, "column": 11, "nodeType": null, "endLine": 25, "endColumn": 42}, {"ruleId": "988", "severity": 1, "message": "1059", "line": 25, "column": 43, "nodeType": null, "endLine": 25, "endColumn": 73}, {"ruleId": "988", "severity": 1, "message": "1063", "line": 26, "column": 11, "nodeType": null, "endLine": 26, "endColumn": 42}, {"ruleId": "988", "severity": 1, "message": "1093", "line": 27, "column": 11, "nodeType": null, "endLine": 27, "endColumn": 49}, {"ruleId": "988", "severity": 1, "message": "1094", "line": 28, "column": 11, "nodeType": null, "endLine": 28, "endColumn": 49}, {"ruleId": "988", "severity": 1, "message": "1095", "line": 29, "column": 11, "nodeType": null, "endLine": 29, "endColumn": 49}, {"ruleId": "988", "severity": 1, "message": "1096", "line": 30, "column": 11, "nodeType": null, "endLine": 30, "endColumn": 49}, {"ruleId": "980", "severity": 1, "message": "1145", "line": 14, "column": 19, "nodeType": null, "endLine": 14, "endColumn": 82, "fix": "1146"}, {"ruleId": "980", "severity": 1, "message": "1147", "line": 37, "column": 49, "nodeType": null, "endLine": 37, "endColumn": 83, "fix": "1148"}, {"ruleId": "1032", "severity": 1, "message": "1149", "line": 34, "column": 6, "nodeType": null, "messageId": "1034", "endLine": 34, "endColumn": 11}, {"ruleId": "980", "severity": 1, "message": "1150", "line": 35, "column": 53, "nodeType": null, "endLine": 35, "endColumn": 93, "fix": "1151"}, {"ruleId": "988", "severity": 1, "message": "1056", "line": 79, "column": 11, "nodeType": null, "endLine": 79, "endColumn": 39}, {"ruleId": "988", "severity": 1, "message": "1057", "line": 79, "column": 40, "nodeType": null, "endLine": 79, "endColumn": 67}, {"ruleId": "988", "severity": 1, "message": "1060", "line": 80, "column": 11, "nodeType": null, "endLine": 80, "endColumn": 39}, {"ruleId": "988", "severity": 1, "message": "1058", "line": 81, "column": 11, "nodeType": null, "endLine": 81, "endColumn": 42}, {"ruleId": "988", "severity": 1, "message": "1059", "line": 81, "column": 43, "nodeType": null, "endLine": 81, "endColumn": 73}, {"ruleId": "988", "severity": 1, "message": "1063", "line": 82, "column": 11, "nodeType": null, "endLine": 82, "endColumn": 42}, {"ruleId": "988", "severity": 1, "message": "1093", "line": 83, "column": 11, "nodeType": null, "endLine": 83, "endColumn": 49}, {"ruleId": "988", "severity": 1, "message": "1094", "line": 84, "column": 11, "nodeType": null, "endLine": 84, "endColumn": 49}, {"ruleId": "988", "severity": 1, "message": "1095", "line": 85, "column": 11, "nodeType": null, "endLine": 85, "endColumn": 49}, {"ruleId": "988", "severity": 1, "message": "1096", "line": 86, "column": 11, "nodeType": null, "endLine": 86, "endColumn": 49}, {"ruleId": "980", "severity": 1, "message": "1152", "line": 116, "column": 50, "nodeType": null, "endLine": 116, "endColumn": 90, "fix": "1153"}, {"ruleId": "980", "severity": 1, "message": "1098", "line": 137, "column": 22, "nodeType": null, "endLine": 137, "endColumn": 84, "fix": "1154"}, {"ruleId": "988", "severity": 1, "message": "1056", "line": 25, "column": 9, "nodeType": null, "endLine": 25, "endColumn": 37}, {"ruleId": "988", "severity": 1, "message": "1057", "line": 25, "column": 38, "nodeType": null, "endLine": 25, "endColumn": 65}, {"ruleId": "988", "severity": 1, "message": "1058", "line": 26, "column": 9, "nodeType": null, "endLine": 26, "endColumn": 40}, {"ruleId": "988", "severity": 1, "message": "1059", "line": 26, "column": 41, "nodeType": null, "endLine": 26, "endColumn": 71}, {"ruleId": "988", "severity": 1, "message": "1056", "line": 39, "column": 5, "nodeType": null, "endLine": 39, "endColumn": 33}, {"ruleId": "988", "severity": 1, "message": "1058", "line": 40, "column": 5, "nodeType": null, "endLine": 40, "endColumn": 36}, {"ruleId": "980", "severity": 1, "message": "1155", "line": 122, "column": 49, "nodeType": null, "endLine": 122, "endColumn": 88, "fix": "1156"}, {"ruleId": "980", "severity": 1, "message": "1066", "line": 130, "column": 55, "nodeType": null, "endLine": 130, "endColumn": 86, "fix": "1157"}, {"ruleId": "988", "severity": 1, "message": "1158", "line": 125, "column": 15, "nodeType": null, "endLine": 125, "endColumn": 50}, {"ruleId": "988", "severity": 1, "message": "1159", "line": 155, "column": 13, "nodeType": null, "endLine": 155, "endColumn": 23}, {"ruleId": "988", "severity": 1, "message": "1160", "line": 155, "column": 24, "nodeType": null, "endLine": 155, "endColumn": 47}, {"ruleId": "988", "severity": 1, "message": "1159", "line": 175, "column": 13, "nodeType": null, "endLine": 175, "endColumn": 23}, {"ruleId": "988", "severity": 1, "message": "1160", "line": 175, "column": 24, "nodeType": null, "endLine": 175, "endColumn": 47}, {"ruleId": "988", "severity": 1, "message": "1160", "line": 195, "column": 20, "nodeType": null, "endLine": 195, "endColumn": 43}, {"ruleId": "988", "severity": 1, "message": "1159", "line": 238, "column": 13, "nodeType": null, "endLine": 238, "endColumn": 23}, {"ruleId": "988", "severity": 1, "message": "1161", "line": 239, "column": 13, "nodeType": null, "endLine": 239, "endColumn": 64}, {"ruleId": "988", "severity": 1, "message": "1162", "line": 292, "column": 13, "nodeType": null, "endLine": 292, "endColumn": 42}, {"ruleId": "988", "severity": 1, "message": "1163", "line": 309, "column": 13, "nodeType": null, "endLine": 309, "endColumn": 64}, {"ruleId": "988", "severity": 1, "message": "1164", "line": 353, "column": 13, "nodeType": null, "endLine": 353, "endColumn": 44}, {"ruleId": "980", "severity": 1, "message": "1165", "line": 366, "column": 61, "nodeType": null, "endLine": 366, "endColumn": 86, "fix": "1166"}, {"ruleId": "980", "severity": 1, "message": "1165", "line": 371, "column": 61, "nodeType": null, "endLine": 371, "endColumn": 86, "fix": "1167"}, {"ruleId": "988", "severity": 1, "message": "1168", "line": 381, "column": 24, "nodeType": null, "endLine": 381, "endColumn": 41}, {"ruleId": "988", "severity": 1, "message": "1169", "line": 429, "column": 13, "nodeType": null, "endLine": 429, "endColumn": 39}, {"ruleId": "988", "severity": 1, "message": "1170", "line": 429, "column": 40, "nodeType": null, "endLine": 429, "endColumn": 57}, {"ruleId": "988", "severity": 1, "message": "1160", "line": 458, "column": 13, "nodeType": null, "endLine": 458, "endColumn": 36}, {"ruleId": "988", "severity": 1, "message": "1170", "line": 458, "column": 37, "nodeType": null, "endLine": 458, "endColumn": 54}, {"ruleId": "988", "severity": 1, "message": "1171", "line": 461, "column": 13, "nodeType": null, "endLine": 461, "endColumn": 36}, {"ruleId": "988", "severity": 1, "message": "1172", "line": 461, "column": 37, "nodeType": null, "endLine": 461, "endColumn": 73}, {"ruleId": "980", "severity": 1, "message": "1173", "line": 488, "column": 51, "nodeType": null, "endLine": 488, "endColumn": 87, "fix": "1174"}, {"ruleId": "980", "severity": 1, "message": "1175", "line": 493, "column": 56, "nodeType": null, "endLine": 493, "endColumn": 82, "fix": "1176"}, {"ruleId": "988", "severity": 1, "message": "1170", "line": 499, "column": 22, "nodeType": null, "endLine": 499, "endColumn": 39}, {"ruleId": "988", "severity": 1, "message": "1171", "line": 502, "column": 5, "nodeType": null, "endLine": 502, "endColumn": 28}, {"ruleId": "988", "severity": 1, "message": "1172", "line": 502, "column": 29, "nodeType": null, "endLine": 502, "endColumn": 65}, {"ruleId": "988", "severity": 1, "message": "1177", "line": 503, "column": 5, "nodeType": null, "endLine": 503, "endColumn": 29}, {"ruleId": "988", "severity": 1, "message": "1178", "line": 503, "column": 30, "nodeType": null, "endLine": 503, "endColumn": 67}, {"ruleId": "988", "severity": 1, "message": "1179", "line": 504, "column": 5, "nodeType": null, "endLine": 504, "endColumn": 41}, {"ruleId": "988", "severity": 1, "message": "1180", "line": 505, "column": 5, "nodeType": null, "endLine": 505, "endColumn": 54}, {"ruleId": "988", "severity": 1, "message": "1181", "line": 507, "column": 5, "nodeType": null, "endLine": 507, "endColumn": 46}, {"ruleId": "988", "severity": 1, "message": "1182", "line": 508, "column": 5, "nodeType": null, "endLine": 508, "endColumn": 59}, {"ruleId": "988", "severity": 1, "message": "1160", "line": 593, "column": 11, "nodeType": null, "endLine": 593, "endColumn": 34}, {"ruleId": "988", "severity": 1, "message": "1170", "line": 593, "column": 35, "nodeType": null, "endLine": 593, "endColumn": 52}, {"ruleId": "988", "severity": 1, "message": "1171", "line": 596, "column": 11, "nodeType": null, "endLine": 596, "endColumn": 34}, {"ruleId": "988", "severity": 1, "message": "1172", "line": 596, "column": 35, "nodeType": null, "endLine": 596, "endColumn": 71}, {"ruleId": "988", "severity": 1, "message": "1183", "line": 597, "column": 11, "nodeType": null, "endLine": 597, "endColumn": 64}, {"ruleId": "988", "severity": 1, "message": "1160", "line": 627, "column": 11, "nodeType": null, "endLine": 627, "endColumn": 34}, {"ruleId": "988", "severity": 1, "message": "1183", "line": 632, "column": 11, "nodeType": null, "endLine": 632, "endColumn": 64}, {"ruleId": "988", "severity": 1, "message": "1184", "line": 633, "column": 11, "nodeType": null, "endLine": 633, "endColumn": 77}, {"ruleId": "988", "severity": 1, "message": "1185", "line": 687, "column": 11, "nodeType": null, "endLine": 687, "endColumn": 32}, {"ruleId": "988", "severity": 1, "message": "1160", "line": 722, "column": 11, "nodeType": null, "endLine": 722, "endColumn": 34}, {"ruleId": "988", "severity": 1, "message": "1170", "line": 722, "column": 35, "nodeType": null, "endLine": 722, "endColumn": 52}, {"ruleId": "988", "severity": 1, "message": "1171", "line": 725, "column": 11, "nodeType": null, "endLine": 725, "endColumn": 34}, {"ruleId": "988", "severity": 1, "message": "1172", "line": 725, "column": 35, "nodeType": null, "endLine": 725, "endColumn": 71}, {"ruleId": "988", "severity": 1, "message": "1177", "line": 726, "column": 11, "nodeType": null, "endLine": 726, "endColumn": 35}, {"ruleId": "988", "severity": 1, "message": "1178", "line": 726, "column": 36, "nodeType": null, "endLine": 726, "endColumn": 73}, {"ruleId": "988", "severity": 1, "message": "1186", "line": 727, "column": 11, "nodeType": null, "endLine": 727, "endColumn": 49}, {"ruleId": "988", "severity": 1, "message": "1179", "line": 734, "column": 11, "nodeType": null, "endLine": 734, "endColumn": 47}, {"ruleId": "988", "severity": 1, "message": "1180", "line": 735, "column": 11, "nodeType": null, "endLine": 735, "endColumn": 60}, {"ruleId": "988", "severity": 1, "message": "1187", "line": 14, "column": 18, "nodeType": null, "endLine": 14, "endColumn": 25}, {"ruleId": "980", "severity": 1, "message": "1188", "line": 15, "column": 68, "nodeType": null, "endLine": 15, "endColumn": 85, "fix": "1189"}, {"ruleId": "988", "severity": 1, "message": "1056", "line": 36, "column": 5, "nodeType": null, "endLine": 36, "endColumn": 33}, {"ruleId": "988", "severity": 1, "message": "1190", "line": 36, "column": 34, "nodeType": null, "endLine": 36, "endColumn": 74}, {"ruleId": "988", "severity": 1, "message": "1191", "line": 37, "column": 5, "nodeType": null, "endLine": 37, "endColumn": 51}, {"ruleId": "988", "severity": 1, "message": "1058", "line": 38, "column": 5, "nodeType": null, "endLine": 38, "endColumn": 36}, {"ruleId": "988", "severity": 1, "message": "1192", "line": 38, "column": 37, "nodeType": null, "endLine": 38, "endColumn": 68}, {"ruleId": "988", "severity": 1, "message": "1193", "line": 39, "column": 5, "nodeType": null, "endLine": 39, "endColumn": 48}, {"ruleId": "988", "severity": 1, "message": "1194", "line": 40, "column": 5, "nodeType": null, "endLine": 40, "endColumn": 33}, {"ruleId": "980", "severity": 1, "message": "1195", "line": 19, "column": 54, "nodeType": null, "endLine": 19, "endColumn": 94, "fix": "1196"}, {"ruleId": "988", "severity": 1, "message": "1030", "line": 23, "column": 9, "nodeType": null, "endLine": 23, "endColumn": 19}, {"ruleId": "988", "severity": 1, "message": "1197", "line": 23, "column": 20, "nodeType": null, "endLine": 23, "endColumn": 29}, {"ruleId": "988", "severity": 1, "message": "1198", "line": 23, "column": 30, "nodeType": null, "endLine": 23, "endColumn": 40}, {"ruleId": "988", "severity": 1, "message": "1058", "line": 25, "column": 9, "nodeType": null, "endLine": 25, "endColumn": 40}, {"ruleId": "988", "severity": 1, "message": "1059", "line": 25, "column": 41, "nodeType": null, "endLine": 25, "endColumn": 71}, {"ruleId": "988", "severity": 1, "message": "1063", "line": 26, "column": 9, "nodeType": null, "endLine": 26, "endColumn": 40}, {"ruleId": "988", "severity": 1, "message": "1093", "line": 27, "column": 9, "nodeType": null, "endLine": 27, "endColumn": 47}, {"ruleId": "988", "severity": 1, "message": "1094", "line": 28, "column": 9, "nodeType": null, "endLine": 28, "endColumn": 47}, {"ruleId": "988", "severity": 1, "message": "1095", "line": 29, "column": 9, "nodeType": null, "endLine": 29, "endColumn": 47}, {"ruleId": "988", "severity": 1, "message": "1096", "line": 30, "column": 9, "nodeType": null, "endLine": 30, "endColumn": 47}, {"ruleId": "1032", "severity": 1, "message": "1199", "line": 18, "column": 7, "nodeType": null, "messageId": "1200", "endLine": 18, "endColumn": 18}, {"ruleId": "975", "severity": 1, "message": "976", "line": 49, "column": 9, "nodeType": "977", "messageId": "978", "endLine": 49, "endColumn": 22, "suggestions": "1201"}, {"ruleId": "975", "severity": 1, "message": "976", "line": 157, "column": 3, "nodeType": "977", "messageId": "978", "endLine": 157, "endColumn": 14, "suggestions": "1202"}, {"ruleId": "1032", "severity": 1, "message": "1199", "line": 18, "column": 7, "nodeType": null, "messageId": "1200", "endLine": 18, "endColumn": 18}, {"ruleId": "1049", "severity": 1, "message": "1050", "line": 6, "column": 30, "nodeType": "1051", "messageId": "1052", "endLine": 6, "endColumn": 33, "suggestions": "1203"}, {"ruleId": "1032", "severity": 1, "message": "1204", "line": 12, "column": 7, "nodeType": null, "messageId": "1034", "endLine": 12, "endColumn": 23}, "no-console", "Unexpected console statement.", "MemberExpression", "unexpected", ["1205"], "better-tailwindcss/multiline", "Incorrect line wrapping. Expected\n\n`text-lg·font-semibold·text-gray-800`\n\nto be\n\n`↵\n······························text-lg·font-semibold·text-gray-800↵\n····························`", {"range": "1206", "text": "1207"}, {"range": "1208", "text": "1207"}, "Incorrect line wrapping. Expected\n\n`mt-1·h-6·w-6·flex-shrink-0·text-[#6b8362]`\n\nto be\n\n`↵\n··························mt-1·h-6·w-6·flex-shrink-0·text-[#6b8362]↵\n························`", {"range": "1209", "text": "1210"}, "Incorrect line wrapping. Expected\n\n`flex·items-center·space-x-3`\n\nto be\n\n`↵\n······················flex·items-center·space-x-3↵\n····················`", {"range": "1211", "text": "1212"}, "better-tailwindcss/no-unregistered-classes", "Unregistered class detected: prose", "Unregistered class detected: prose-lg", "Unregistered class detected: lead", ["1213"], ["1214"], ["1215"], ["1216"], ["1217"], ["1218"], ["1219"], ["1220"], ["1221"], ["1222"], ["1223"], ["1224"], ["1225"], ["1226"], ["1227"], ["1228"], ["1229"], ["1230"], ["1231"], "Unregistered class detected: bokunWidget", "Incorrect line wrapping. Expected\n\n`h-96·w-full·animate-pulse·rounded-lg·bg-gray-200`\n\nto be\n\n`↵\n····h-96·w-full·animate-pulse·rounded-lg·bg-gray-200↵\n··`", {"range": "1232", "text": "1233"}, "Incorrect line wrapping. Expected\n\n`h-80·w-full·animate-pulse·rounded-lg·bg-gray-200`\n\nto be\n\n`↵\n····h-80·w-full·animate-pulse·rounded-lg·bg-gray-200↵\n··`", {"range": "1234", "text": "1235"}, {"range": "1236", "text": "1233"}, "Unregistered class detected: mosaic-grid", "Unregistered class detected: mosaic-main", "Unregistered class detected: mosaic-top", "Unregistered class detected: mosaic-bottom", "Incorrect line wrapping. Expected\n\n`absolute·inset-0·z-30·flex·items-center·justify-center`\n\nto be\n\n`↵\n············absolute·inset-0·z-30·flex·items-center·justify-center↵\n··········`", {"range": "1237", "text": "1238"}, "max-len", "This line has a length of 158. Maximum allowed is 120.", "Program", "max", ["1239"], ["1240"], "Unregistered class detected: bokunButton", "Unregistered class detected: animate-in", "Unregistered class detected: slide-in-from-bottom", "@typescript-eslint/no-unused-vars", "'CookieConsent' is defined but never used. Allowed unused vars must match /^_/u.", "unusedVar", ["1241"], ["1242"], ["1243"], ["1244"], "'useState' is defined but never used. Allowed unused vars must match /^_/u.", "Incorrect line wrapping. Expected\n\n`absolute·bottom-20·z-20·flex·w-full·justify-center`\n\nto be\n\n`↵\n········absolute·bottom-20·z-20·flex·w-full·justify-center↵\n······`", {"range": "1245", "text": "1246"}, "'ReactNode' is defined but never used. Allowed unused vars must match /^_/u.", "Incorrect line wrapping. Expected\n\n`object-cover`\n\nto be\n\n`↵\n··············object-cover↵\n············`", {"range": "1247", "text": "1248"}, "complexity", "Function 'OptimizedImage' has a complexity of 17. Maximum allowed is 15.", "FunctionDeclaration", "complex", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["1249", "1250"], "Incorrect line wrapping. Expected\n\n`h-4·w-4·shrink-0·transition-transform·duration-200`\n\nto be\n\n`↵\n········h-4·w-4·shrink-0·transition-transform·duration-200↵\n······`", {"range": "1251", "text": "1252"}, "Unregistered class detected: data-[state=open]:animate-in", "Unregistered class detected: data-[state=open]:fade-in-0", "Unregistered class detected: data-[state=closed]:animate-out", "Unregistered class detected: data-[state=closed]:fade-out-0", "Unregistered class detected: data-[state=open]:zoom-in-95", "Unregistered class detected: data-[state=open]:slide-in-from-left-1/2", "Unregistered class detected: data-[state=open]:slide-in-from-top-[48%]", "Unregistered class detected: data-[state=closed]:zoom-out-95", "Unregistered class detected: data-[state=closed]:slide-out-to-left-1/2", "Unregistered class detected: data-[state=closed]:slide-out-to-top-[48%]", "Incorrect line wrapping. Expected\n\n`text-sm·text-muted-foreground`\n\nto be\n\n`↵\n····text-sm·text-muted-foreground↵\n··`", {"range": "1253", "text": "1254"}, "Incorrect line wrapping. Expected\n\n`relative·flex·h-10·w-10·shrink-0·overflow-hidden·rounded-full`\n\nto be\n\n`↵\n······relative·flex·h-10·w-10·shrink-0·overflow-hidden·rounded-full↵\n····`", {"range": "1255", "text": "1256"}, "This line has a length of 146. Maximum allowed is 120.", "Incorrect line wrapping. Expected\n\n`flex·h-full·w-full·items-center·justify-center·rounded-full·bg-muted`\n\nto be\n\n`↵\n······flex·h-full·w-full·items-center·justify-center·rounded-full·bg-muted↵\n····`", {"range": "1257", "text": "1258"}, "Incorrect line wrapping. Expected\n\n`[&>svg]:h-3.5·[&>svg]:w-3.5`\n\nto be\n\n`↵\n····[&>svg]:h-3.5·[&>svg]:w-3.5↵\n··`", {"range": "1259", "text": "1260"}, "'CustomComponents' is defined but never used. Allowed unused vars must match /^_/u.", "Incorrect line wrapping. Expected\n\n`rounded-lg·border·bg-card·text-card-foreground·shadow-xs`\n\nto be\n\n`↵\n····rounded-lg·border·bg-card·text-card-foreground·shadow-xs↵\n··`", {"range": "1261", "text": "1262"}, "Incorrect line wrapping. Expected\n\n`text-2xl·leading-none·font-semibold·tracking-tight`\n\nto be\n\n`↵\n······text-2xl·leading-none·font-semibold·tracking-tight↵\n····`", {"range": "1263", "text": "1264"}, "Incorrect line wrapping. Expected\n\n`flex·items-center·p-6·pt-0`\n\nto be\n\n`↵\n····flex·items-center·p-6·pt-0↵\n··`", {"range": "1265", "text": "1266"}, "'NameType' is defined but never used. Allowed unused vars must match /^_/u.", "'Payload' is defined but never used. Allowed unused vars must match /^_/u.", "'ValueType' is defined but never used. Allowed unused vars must match /^_/u.", "Arrow function has a complexity of 20. Maximum allowed is 15.", "ArrowFunctionExpression", "Incorrect line wrapping. Expected\n\n`flex·items-center·justify-center·text-current`\n\nto be\n\n`↵\n······flex·items-center·justify-center·text-current↵\n····`", {"range": "1267", "text": "1268"}, "Incorrect line wrapping. Expected\n\n`py-6·text-center·text-sm`\n\nto be\n\n`↵\n··py-6·text-center·text-sm↵\n`", {"range": "1269", "text": "1270"}, "Incorrect line wrapping. Expected\n\n`ml-auto·text-xs·tracking-widest·text-muted-foreground`\n\nto be\n\n`↵\n····ml-auto·text-xs·tracking-widest·text-muted-foreground↵\n··`", {"range": "1271", "text": "1272"}, "Unregistered class detected: data-[side=bottom]:slide-in-from-top-2", "Unregistered class detected: data-[side=left]:slide-in-from-right-2", "Unregistered class detected: data-[side=right]:slide-in-from-left-2", "Unregistered class detected: data-[side=top]:slide-in-from-bottom-2", "Unregistered class detected: fade-in-80", "Incorrect line wrapping. Expected\n\n`absolute·left-2·flex·h-3.5·w-3.5·items-center·justify-center`\n\nto be\n\n`↵\n······absolute·left-2·flex·h-3.5·w-3.5·items-center·justify-center↵\n····`", {"range": "1273", "text": "1274"}, {"range": "1275", "text": "1274"}, "Incorrect line wrapping. Expected\n\n`-mx-1·my-1·h-px·bg-border`\n\nto be\n\n`↵\n····-mx-1·my-1·h-px·bg-border↵\n··`", {"range": "1276", "text": "1277"}, {"range": "1278", "text": "1272"}, "Incorrect line wrapping. Expected\n\n`fixed·inset-0·z-50·bg-black/80`\n\nto be\n\n`↵\n····fixed·inset-0·z-50·bg-black/80↵\n··`", {"range": "1279", "text": "1280"}, {"range": "1281", "text": "1254"}, {"range": "1282", "text": "1274"}, {"range": "1283", "text": "1274"}, "Incorrect line wrapping. Expected\n\n`-mx-1·my-1·h-px·bg-muted`\n\nto be\n\n`↵\n····-mx-1·my-1·h-px·bg-muted↵\n··`", {"range": "1284", "text": "1285"}, "Incorrect line wrapping. Expected\n\n`text-sm·text-muted-foreground`\n\nto be\n\n`↵\n······text-sm·text-muted-foreground↵\n····`", {"range": "1286", "text": "1287"}, "Incorrect line wrapping. Expected\n\n`text-sm·font-medium·text-destructive`\n\nto be\n\n`↵\n········text-sm·font-medium·text-destructive↵\n······`", {"range": "1288", "text": "1289"}, "Incorrect line wrapping. Expected\n\n`flex·items-center`\n\nto be\n\n`↵\n····flex·items-center↵\n··`", {"range": "1290", "text": "1291"}, "Unregistered class detected: animate-caret-blink", "Incorrect line wrapping. Expected\n\n`flex·h-10·items-center·space-x-1·rounded-md·border·bg-background·p-1`\n\nto be\n\n`↵\n······flex·h-10·items-center·space-x-1·rounded-md·border·bg-background·p-1↵\n····`", {"range": "1292", "text": "1293"}, {"range": "1294", "text": "1274"}, {"range": "1295", "text": "1274"}, {"range": "1296", "text": "1272"}, "Incorrect line wrapping. Expected\n\n`relative·z-10·flex·max-w-max·flex-1·items-center·justify-center`\n\nto be\n\n`↵\n······relative·z-10·flex·max-w-max·flex-1·items-center·justify-center↵\n····`", {"range": "1297", "text": "1298"}, "Incorrect line wrapping. Expected\n\n`group·flex·flex-1·list-none·items-center·justify-center·space-x-1`\n\nto be\n\n`↵\n······group·flex·flex-1·list-none·items-center·justify-center·space-x-1↵\n····`", {"range": "1299", "text": "1300"}, "Unregistered class detected: data-[motion^=from-]:animate-in", "Unregistered class detected: data-[motion^=from-]:fade-in", "Unregistered class detected: data-[motion^=to-]:animate-out", "Unregistered class detected: data-[motion^=to-]:fade-out", "Unregistered class detected: data-[motion=from-end]:slide-in-from-right-52", "Unregistered class detected: data-[motion=from-start]:slide-in-from-left-52", "Unregistered class detected: data-[motion=to-end]:slide-out-to-right-52", "Unregistered class detected: data-[motion=to-start]:slide-out-to-left-52", "Unregistered class detected: origin-top-center", "Unregistered class detected: data-[state=open]:zoom-in-90", "Unregistered class detected: data-[state=visible]:animate-in", "Unregistered class detected: data-[state=visible]:fade-in", "Unregistered class detected: data-[state=hidden]:animate-out", "Unregistered class detected: data-[state=hidden]:fade-out", "Incorrect line wrapping. Expected\n\n`gap-1·pl-2.5`\n\nto be\n\n`↵\n····gap-1·pl-2.5↵\n··`", {"range": "1301", "text": "1302"}, "Incorrect line wrapping. Expected\n\n`gap-1·pr-2.5`\n\nto be\n\n`↵\n····gap-1·pr-2.5↵\n··`", {"range": "1303", "text": "1304"}, "Incorrect line wrapping. Expected\n\n`relative·h-4·w-full·overflow-hidden·rounded-full·bg-secondary`\n\nto be\n\n`↵\n······relative·h-4·w-full·overflow-hidden·rounded-full·bg-secondary↵\n····`", {"range": "1305", "text": "1306"}, "Incorrect line wrapping. Expected\n\n`flex·items-center·justify-center`\n\nto be\n\n`↵\n········flex·items-center·justify-center↵\n······`", {"range": "1307", "text": "1308"}, "'props' is defined but never used. Allowed unused args must match /^_/u.", "Incorrect line wrapping. Expected\n\n`relative·flex-1·rounded-full·bg-border`\n\nto be\n\n`↵\n······relative·flex-1·rounded-full·bg-border↵\n····`", {"range": "1309", "text": "1310"}, "Incorrect line wrapping. Expected\n\n`py-1.5·pr-2·pl-8·text-sm·font-semibold`\n\nto be\n\n`↵\n····py-1.5·pr-2·pl-8·text-sm·font-semibold↵\n··`", {"range": "1311", "text": "1312"}, {"range": "1313", "text": "1274"}, "Incorrect line wrapping. Expected\n\n`text-lg·font-semibold·text-foreground`\n\nto be\n\n`↵\n····text-lg·font-semibold·text-foreground↵\n··`", {"range": "1314", "text": "1315"}, {"range": "1316", "text": "1254"}, "Unregistered class detected: has-data-[variant=inset]:bg-sidebar", "Unregistered class detected: bg-sidebar", "Unregistered class detected: text-sidebar-foreground", "Unregistered class detected: group-data-[variant=floating]:border-sidebar-border", "Unregistered class detected: hover:after:bg-sidebar-border", "Unregistered class detected: hover:group-data-[collapsible=offcanvas]:bg-sidebar", "Unregistered class detected: focus-visible:ring-sidebar-ring", "Incorrect line wrapping. Expected\n\n`flex·flex-col·gap-2·p-2`\n\nto be\n\n`↵\n····flex·flex-col·gap-2·p-2↵\n··`", {"range": "1317", "text": "1318"}, {"range": "1319", "text": "1318"}, "Unregistered class detected: bg-sidebar-border", "Unregistered class detected: text-sidebar-foreground/70", "Unregistered class detected: ring-sidebar-ring", "Unregistered class detected: hover:bg-sidebar-accent", "Unregistered class detected: hover:text-sidebar-accent-foreground", "Incorrect line wrapping. Expected\n\n`flex·w-full·min-w-0·flex-col·gap-1`\n\nto be\n\n`↵\n····flex·w-full·min-w-0·flex-col·gap-1↵\n··`", {"range": "1320", "text": "1321"}, "Incorrect line wrapping. Expected\n\n`group/menu-item·relative`\n\nto be\n\n`↵\n····group/menu-item·relative↵\n··`", {"range": "1322", "text": "1323"}, "Unregistered class detected: active:bg-sidebar-accent", "Unregistered class detected: active:text-sidebar-accent-foreground", "Unregistered class detected: data-[active=true]:bg-sidebar-accent", "Unregistered class detected: data-[active=true]:text-sidebar-accent-foreground", "Unregistered class detected: data-[state=open]:hover:bg-sidebar-accent", "Unregistered class detected: data-[state=open]:hover:text-sidebar-accent-foreground", "Unregistered class detected: peer-hover/menu-button:text-sidebar-accent-foreground", "Unregistered class detected: peer-data-[active=true]/menu-button:text-sidebar-accent-foreground", "Unregistered class detected: border-sidebar-border", "Unregistered class detected: [&>svg]:text-sidebar-accent-foreground", "Unregistered class detected: toaster", "Incorrect line wrapping. Expected\n\n`[&_tr]:border-b`\n\nto be\n\n`↵\n····[&_tr]:border-b↵\n··`", {"range": "1324", "text": "1325"}, "Unregistered class detected: data-[state=open]:slide-in-from-top-full", "Unregistered class detected: data-[state=open]:sm:slide-in-from-bottom-full", "Unregistered class detected: data-[state=closed]:fade-out-80", "Unregistered class detected: data-[state=closed]:slide-out-to-right-full", "Unregistered class detected: data-[swipe=end]:animate-out", "Incorrect line wrapping. Expected\n\n`flex·items-center·justify-center·gap-1`\n\nto be\n\n`↵\n····flex·items-center·justify-center·gap-1↵\n··`", {"range": "1326", "text": "1327"}, "Unregistered class detected: fade-in-0", "Unregistered class detected: zoom-in-95", "'actionTypes' is assigned a value but only used as a type. Allowed unused vars must match /^_/u.", "usedOnlyAsType", ["1328"], ["1329"], ["1330", "1331"], "'devScriptSources' is assigned a value but never used. Allowed unused vars must match /^_/u.", {"fix": "1332", "messageId": "1333", "data": "1334", "desc": "1335"}, [23869, 23906], "`\n                              text-lg font-semibold text-gray-800\n                            `", [26142, 26179], [27524, 27567], "`\n                          mt-1 h-6 w-6 flex-shrink-0 text-[#6b8362]\n                        `", [36189, 36218], "`\n                      flex items-center space-x-3\n                    `", {"fix": "1336", "messageId": "1333", "data": "1337", "desc": "1338"}, {"fix": "1339", "messageId": "1333", "data": "1340", "desc": "1335"}, {"fix": "1341", "messageId": "1333", "data": "1342", "desc": "1343"}, {"fix": "1344", "messageId": "1333", "data": "1345", "desc": "1343"}, {"fix": "1346", "messageId": "1333", "data": "1347", "desc": "1343"}, {"fix": "1348", "messageId": "1333", "data": "1349", "desc": "1343"}, {"fix": "1350", "messageId": "1333", "data": "1351", "desc": "1343"}, {"fix": "1352", "messageId": "1333", "data": "1353", "desc": "1343"}, {"fix": "1354", "messageId": "1333", "data": "1355", "desc": "1343"}, {"fix": "1356", "messageId": "1333", "data": "1357", "desc": "1343"}, {"fix": "1358", "messageId": "1333", "data": "1359", "desc": "1335"}, {"fix": "1360", "messageId": "1333", "data": "1361", "desc": "1343"}, {"fix": "1362", "messageId": "1333", "data": "1363", "desc": "1343"}, {"fix": "1364", "messageId": "1333", "data": "1365", "desc": "1343"}, {"fix": "1366", "messageId": "1333", "data": "1367", "desc": "1343"}, {"fix": "1368", "messageId": "1333", "data": "1369", "desc": "1343"}, {"fix": "1370", "messageId": "1333", "data": "1371", "desc": "1335"}, {"fix": "1372", "messageId": "1333", "data": "1373", "desc": "1343"}, {"fix": "1374", "messageId": "1333", "data": "1375", "desc": "1343"}, [297, 347], "`\n    h-96 w-full animate-pulse rounded-lg bg-gray-200\n  `", [280, 330], "`\n    h-80 w-full animate-pulse rounded-lg bg-gray-200\n  `", [261, 311], [2066, 2122], "`\n            absolute inset-0 z-30 flex items-center justify-center\n          `", {"fix": "1376", "messageId": "1333", "data": "1377", "desc": "1338"}, {"fix": "1378", "messageId": "1333", "data": "1379", "desc": "1343"}, {"fix": "1380", "messageId": "1333", "data": "1381", "desc": "1343"}, {"fix": "1382", "messageId": "1333", "data": "1383", "desc": "1343"}, {"fix": "1384", "messageId": "1333", "data": "1385", "desc": "1343"}, {"fix": "1386", "messageId": "1333", "data": "1387", "desc": "1335"}, [2989, 3041], "`\n        absolute bottom-20 z-20 flex w-full justify-center\n      `", [1694, 1708], "`\n              object-cover\n            `", {"messageId": "1388", "fix": "1389", "desc": "1390"}, {"messageId": "1391", "fix": "1392", "desc": "1393"}, [1175, 1227], "`\n        h-4 w-4 shrink-0 transition-transform duration-200\n      `", [3353, 3384], "`\n    text-sm text-muted-foreground\n  `", [525, 588], "`\n      relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full\n    `", [2073, 2143], "`\n      flex h-full w-full items-center justify-center rounded-full bg-muted\n    `", [2110, 2139], "`\n    [&>svg]:h-3.5 [&>svg]:w-3.5\n  `", [217, 275], "`\n    rounded-lg border bg-card text-card-foreground shadow-xs\n  `", [760, 812], "`\n      text-2xl leading-none font-semibold tracking-tight\n    `", [1558, 1586], "`\n    flex items-center p-6 pt-0\n  `", [893, 940], "`\n      flex items-center justify-center text-current\n    `", [2843, 2869], "`\n  py-6 text-center text-sm\n`", [4816, 4871], "`\n    ml-auto text-xs tracking-widest text-muted-foreground\n  `", [4719, 4781], "`\n      absolute left-2 flex h-3.5 w-3.5 items-center justify-center\n    `", [5677, 5739], [6777, 6804], "`\n    -mx-1 my-1 h-px bg-border\n  `", [7041, 7096], [743, 775], "`\n    fixed inset-0 z-50 bg-black/80\n  `", [2709, 2740], [4894, 4956], [5879, 5941], [6978, 7004], "`\n    -mx-1 my-1 h-px bg-muted\n  `", [3288, 3319], "`\n      text-sm text-muted-foreground\n    `", [3773, 3811], "`\n        text-sm font-medium text-destructive\n      `", [828, 847], "`\n    flex items-center\n  `", [672, 742], "`\n      flex h-10 items-center space-x-1 rounded-md border bg-background p-1\n    `", [5597, 5659], [6519, 6581], [7794, 7849], [511, 576], "`\n      relative z-10 flex max-w-max flex-1 items-center justify-center\n    `", [1037, 1104], "`\n      group flex flex-1 list-none items-center justify-center space-x-1\n    `", [1683, 1697], "`\n    gap-1 pl-2.5\n  `", [2041, 2055], "`\n    gap-1 pr-2.5\n  `", [395, 458], "`\n      relative h-4 w-full overflow-hidden rounded-full bg-secondary\n    `", [1234, 1268], "`\n        flex items-center justify-center\n      `", [1412, 1452], "`\n      relative flex-1 rounded-full bg-border\n    `", [4124, 4164], "`\n    py-1.5 pr-2 pl-8 text-sm font-semibold\n  `", [4837, 4899], [3775, 3814], "`\n    text-lg font-semibold text-foreground\n  `", [4162, 4193], [11323, 11348], "`\n    flex flex-col gap-2 p-2\n  `", [11604, 11629], [15255, 15291], "`\n    flex w-full min-w-0 flex-col gap-1\n  `", [15538, 15564], "`\n    group/menu-item relative\n  `", [565, 582], "`\n    [&_tr]:border-b\n  `", [710, 750], "`\n    flex items-center justify-center gap-1\n  `", {"fix": "1394", "messageId": "1333", "data": "1395", "desc": "1335"}, {"fix": "1396", "messageId": "1333", "data": "1397", "desc": "1343"}, {"messageId": "1388", "fix": "1398", "desc": "1390"}, {"messageId": "1391", "fix": "1399", "desc": "1393"}, {"range": "1400", "text": "1401"}, "removeConsole", {"propertyName": "1402"}, "Remove the console.error().", {"range": "1403", "text": "1401"}, {"propertyName": "1404"}, "Remove the console.warn().", {"range": "1405", "text": "1401"}, {"propertyName": "1402"}, {"range": "1406", "text": "1401"}, {"propertyName": "1407"}, "Remove the console.log().", {"range": "1408", "text": "1401"}, {"propertyName": "1407"}, {"range": "1409", "text": "1401"}, {"propertyName": "1407"}, {"range": "1410", "text": "1401"}, {"propertyName": "1407"}, {"range": "1411", "text": "1401"}, {"propertyName": "1407"}, {"range": "1412", "text": "1401"}, {"propertyName": "1407"}, {"range": "1413", "text": "1401"}, {"propertyName": "1407"}, {"range": "1414", "text": "1401"}, {"propertyName": "1407"}, {"range": "1415", "text": "1401"}, {"propertyName": "1402"}, {"range": "1416", "text": "1401"}, {"propertyName": "1407"}, {"range": "1417", "text": "1401"}, {"propertyName": "1407"}, {"range": "1418", "text": "1401"}, {"propertyName": "1407"}, {"range": "1419", "text": "1401"}, {"propertyName": "1407"}, {"range": "1420", "text": "1401"}, {"propertyName": "1407"}, {"range": "1421", "text": "1401"}, {"propertyName": "1402"}, {"range": "1422", "text": "1401"}, {"propertyName": "1407"}, {"range": "1423", "text": "1401"}, {"propertyName": "1407"}, {"range": "1424", "text": "1401"}, {"propertyName": "1404"}, {"range": "1425", "text": "1401"}, {"propertyName": "1407"}, {"range": "1426", "text": "1401"}, {"propertyName": "1407"}, {"range": "1427", "text": "1401"}, {"propertyName": "1407"}, {"range": "1428", "text": "1401"}, {"propertyName": "1407"}, {"range": "1429", "text": "1401"}, {"propertyName": "1402"}, "suggestUnknown", {"range": "1430", "text": "1431"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "1432", "text": "1433"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "1434", "text": "1401"}, {"propertyName": "1402"}, {"range": "1435", "text": "1401"}, {"propertyName": "1407"}, {"range": "1436", "text": "1431"}, {"range": "1437", "text": "1433"}, [598, 618], "", "error", [236, 274], "warn", [614, 666], [1195, 1256], "log", [1341, 1435], [1936, 2035], [2234, 2305], [2438, 2523], [3350, 3537], [4206, 4311], [4536, 4620], [4727, 4827], [5082, 5165], [5281, 5378], [5451, 5548], [6113, 6216], [6431, 6510], [6739, 6834], [6998, 7094], [7391, 7471], [3581, 3797], [4657, 4819], [885, 935], [1717, 1786], [2056, 2096], [2143, 2193], [1168, 1171], "unknown", [1168, 1171], "never", [1445, 1498], [3956, 4003], [179, 182], [179, 182]]