{"semi": false, "singleQuote": true, "tabWidth": 2, "useTabs": false, "trailingComma": "es5", "printWidth": 120, "bracketSpacing": true, "bracketSameLine": false, "arrowParens": "avoid", "endOfLine": "lf", "embeddedLanguageFormatting": "auto", "htmlWhitespaceSensitivity": "css", "insertPragma": false, "jsxSingleQuote": true, "proseWrap": "preserve", "quoteProps": "as-needed", "requirePragma": false, "vueIndentScriptAndStyle": false, "singleAttributePerLine": false, "plugins": [], "overrides": [{"files": "*.json", "options": {"tabWidth": 2}}, {"files": "*.md", "options": {"printWidth": 80, "proseWrap": "always"}}]}