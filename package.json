{"name": "my-v0-project", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "dev:webpack": "next dev", "dev:trace": "NEXT_TURBOPACK_TRACING=1 next dev --turbopack", "build": "pnpm audit --audit-level moderate && node scripts/generate-sitemap-data.js && next build && cp -r public .next/standalone/", "start": "node .next/standalone/server.js", "lint": "eslint . --ext .ts,.tsx --cache --config eslint.config.mjs", "lint:fix": "eslint . --ext .ts,.tsx --cache --fix --config eslint.config.mjs", "lint:check": "eslint . --ext .ts,.tsx --cache --config eslint.config.mjs", "lint:report": "eslint . --ext .ts,.tsx --format json --output-file eslint-report.json --config eslint.config.mjs", "lint:warn-only": "eslint . --ext .ts,.tsx --max-warnings 1000 --config eslint.config.mjs", "format": "prettier --write .", "format:check": "prettier --check .", "type-check": "tsc --noEmit", "code-quality": "pnpm run type-check && pnpm run lint:report && pnpm run format:check", "code-quality:lenient": "pnpm run type-check && pnpm run lint:warn-only && pnpm run format:check", "fix-all": "pnpm run lint:fix && pnpm run format", "analyze": "ANALYZE=true next build", "security:audit": "pnpm audit --audit-level moderate"}, "dependencies": {"@hookform/resolvers": "5.0.1", "@next/third-parties": "15.3.2", "@nosecone/next": "1.0.0-beta.8", "@radix-ui/react-accordion": "1.2.10", "@radix-ui/react-alert-dialog": "1.1.13", "@radix-ui/react-aspect-ratio": "1.1.6", "@radix-ui/react-avatar": "1.1.9", "@radix-ui/react-checkbox": "1.3.1", "@radix-ui/react-collapsible": "1.1.10", "@radix-ui/react-context-menu": "2.2.14", "@radix-ui/react-dialog": "1.1.13", "@radix-ui/react-dropdown-menu": "2.1.14", "@radix-ui/react-hover-card": "1.1.13", "@radix-ui/react-label": "2.1.6", "@radix-ui/react-menubar": "1.1.14", "@radix-ui/react-navigation-menu": "1.2.12", "@radix-ui/react-popover": "1.1.13", "@radix-ui/react-progress": "1.1.6", "@radix-ui/react-radio-group": "1.3.6", "@radix-ui/react-scroll-area": "1.2.8", "@radix-ui/react-select": "2.2.4", "@radix-ui/react-separator": "1.1.6", "@radix-ui/react-slider": "1.3.4", "@radix-ui/react-slot": "1.2.2", "@radix-ui/react-switch": "1.2.4", "@radix-ui/react-tabs": "1.1.11", "@radix-ui/react-toast": "1.2.13", "@radix-ui/react-toggle": "1.1.8", "@radix-ui/react-toggle-group": "1.1.9", "@radix-ui/react-tooltip": "1.2.6", "@sentry/nextjs": "^9.27.0", "@types/iframe-resizer": "^3.5.13", "@vercel/analytics": "1.5.0", "@vercel/speed-insights": "1.2.0", "autoprefixer": "10.4.21", "class-variance-authority": "0.7.1", "clsx": "2.1.1", "cmdk": "1.1.1", "critters": "^0.0.25", "date-fns": "4.1.0", "embla-carousel-react": "8.6.0", "framer-motion": "^12.12.1", "iframe-resizer": "^4.4.5", "import-in-the-middle": "^1.14.0", "input-otp": "1.4.2", "lucide-react": "0.509.0", "next": "15.3.2", "next-themes": "0.4.6", "react": "19.1.0", "react-day-picker": "9.6.7", "react-dom": "19.1.0", "react-google-reviews": "^1.7.4", "react-hook-form": "7.56.3", "react-resizable-panels": "3.0.1", "recharts": "2.15.3", "require-in-the-middle": "^7.5.2", "sonner": "2.0.3", "tailwind-merge": "3.2.0", "tailwindcss-animate": "1.0.7", "vaul": "1.1.2", "zod": "3.24.4"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.28.0", "@next/bundle-analyzer": "15.3.2", "@next/eslint-plugin-next": "^15.3.3", "@tailwindcss/postcss": "4.1.6", "@types/lodash": "4.17.16", "@types/node": "22.15.17", "@types/react": "19.1.3", "@types/react-dom": "19.1.3", "@typescript-eslint/eslint-plugin": "^8.34.0", "@typescript-eslint/parser": "^8.34.0", "cross-env": "7.0.3", "eslint": "^9.28.0", "eslint-config-next": "^15.3.3", "eslint-config-prettier": "^10.1.5", "eslint-formatter-compact": "^8.40.0", "eslint-import-resolver-typescript": "^4.4.3", "eslint-plugin-better-tailwindcss": "^3.1.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-import-x": "^4.15.1", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "globals": "^16.2.0", "postcss": "8.5.3", "prettier": "^3.5.3", "tailwindcss": "4.1.6", "typescript": "5.8.3"}, "resolutions": {"rollup": "^2.79.1", "rollup-plugin-terser": "^7.0.2", "d3-path": "^3.1.0", "@types/react": "19.1.3", "@types/react-dom": "19.1.3"}, "overrides": {"@types/react": "19.1.3", "@types/react-dom": "19.1.3", "@types/pg": "8.6.0"}}